﻿using MigracionSAPParteTecnica.DTO.MM.Barnices;

namespace MigracionSAPParteTecnica.Validators.MM.Barnices;
public static class NodrizasValidator
{
    public static string? Validar(NodrizasDTO dto)
    {
        if (dto.Nodriza <= 0)
            return "El número de nodriza no puede ser nulo ni 0.";

        var hoy = DateTime.Today;
        if (dto.Fecha < hoy.AddMonths(-2) || dto.Fecha > hoy.AddMonths(2))
            return "La fecha debe estar dentro de un rango de ±2 meses respecto a hoy.";

        if (dto.CodigoBarniz <= 0)
            return "El código de barniz no puede ser nulo.";

        if (string.IsNullOrWhiteSpace(dto.Lote))
            return "El lote no puede estar vacío.";

        if (dto.Cantidad <= 0)
            return "La cantidad no puede ser nula ni 0.";

        return null;
    }
}