﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;
public class TratarNoIncluirRegComprasCommand : IRequest<SingleResult<NoIncluirRegComprasDTO>>
{
    public NoIncluirRegComprasDTO NoIncluirRegCompraDTO { get; set; }

    public TratarNoIncluirRegComprasCommand(NoIncluirRegComprasDTO noIncluirRegCompraDTO)
    {
        NoIncluirRegCompraDTO = noIncluirRegCompraDTO;
    }
}

internal class TratarNoIncluirRegComprasCommandHandler : IRequestHandler<TratarNoIncluirRegComprasCommand, SingleResult<NoIncluirRegComprasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarNoIncluirRegComprasCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<NoIncluirRegComprasDTO>> Handle(TratarNoIncluirRegComprasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<NoIncluirRegComprasDTO>
        {
            Data = new NoIncluirRegComprasDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.NoIncluirRegCompraDTO;

            var existing = await _migracionSAPParteTecnicaContext.NoIncluirRegCompras
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.NoIncluirRegCompras>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.NoIncluirRegCompras.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<NoIncluirRegComprasDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Barnices_NoIncluirRegCompras",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de registro de 'No Incluir Reg Compras': Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.NoIncluirRegCompras.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<NoIncluirRegComprasDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Barnices_NoIncluirRegCompras",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de registro de 'No Incluir Reg Compras': Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarNoIncluirRegComprasCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}