﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editSAPProveedor?.Id > 0)
{
    <EntityLockManager EntityType="SAPProveedor"
                       EntityId="editSAPProveedor.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar proveedor"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este proveedor está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editSAPProveedor" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editSAPProveedor" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código IN2:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-hash" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editSAPProveedor.Codigo_IN2" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Código Business Partner:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-people" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editSAPProveedor.Codigo_BP" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Nombre IN2:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-person-vcard" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPProveedor.Nombre_IN2" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura"/>
                        </div>
                    </Template>
                </DxFormLayoutItem>
                
                <DxFormLayoutItem Caption="Código SAP:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/sap-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxTextBox @bind-Text="editSAPProveedor.Codigo_SAP" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura"/>
                        </div>
                    </Template>
                </DxFormLayoutItem>

                <DxFormLayoutItem Caption="Organización 3410:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="3">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editSAPProveedor.OrgCompras3410"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween"
                                    ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Organización 3411:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="3">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editSAPProveedor.OrgCompras3411"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween"
                                    ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Nombre SAP:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-building" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPProveedor.Nombre_SAP" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Clave Idioma:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-translate" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPProveedor.Clave_Idioma" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<SAPProveedorDTO> OnSave { get; set; }
    private SAPProveedorDTO editSAPProveedor = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(SAPProveedorDTO SAPProveedor)
    {
        editSAPProveedor = SAPProveedor;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        await OnSave.InvokeAsync(editSAPProveedor);
        IsPopupVisible = false;
        ToastService.MostrarOk("Proveedor guardado correctamente.");
    }
}