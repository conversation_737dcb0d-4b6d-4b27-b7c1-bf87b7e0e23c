﻿using System.Text.Json.Serialization;

namespace MigracionSAPParteTecnica.Entities.Eventos;
public class PlanoEstadoCambiadoEvent
{
    [JsonPropertyName("tipo")]
    public string Tipo { get; set; }
    [JsonPropertyName("clienteId")]
    public int ClienteId { get; set; }
    [JsonPropertyName("nombrePlano")]
    public string NombrePlano { get; set; }
    [JsonPropertyName("nuevoEstado")]
    public bool NuevoEstado { get; set; }
    [JsonPropertyName("usuario")]
    public string Usuario { get; set; }
}
