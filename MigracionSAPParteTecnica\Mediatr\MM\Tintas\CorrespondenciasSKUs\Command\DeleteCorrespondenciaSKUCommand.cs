﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;
public class DeleteCorrespondenciaSKUCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteCorrespondenciaSKUCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteCorrespondenciaSKUCommandHandler : IRequestHandler<DeleteCorrespondenciaSKUCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteCorrespondenciaSKUCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteCorrespondenciaSKUCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var correspondencia = await _migracionSAPParteTecnicaContext.CorrespondenciasSkus
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (correspondencia == null)
            {
                result.Errors.Add("No se ha encontrado la correspondencia del sku a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(correspondencia);

            correspondencia.Borrado = true;

            _migracionSAPParteTecnicaContext.CorrespondenciasSkus.Update(correspondencia);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Tintas_CorrespondenciasSKUS",
                IdRegistro = correspondencia.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de la correspondencia del sku: Id='{correspondencia.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteCorrespondenciaSKUCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}