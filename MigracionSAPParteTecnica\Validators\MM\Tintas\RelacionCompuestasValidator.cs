﻿using MigracionSAPParteTecnica.DTO.MM.Tintas;

namespace MigracionSAPParteTecnica.Validators.MM.Tintas;
public static class RelacionCompuestasValidator
{
    public static string? Validar(RelacionCompuestasDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Ingrediente))
            return "El ingrediente no puede estar vacío.";

        if (dto.CodigoTinta == 0)
            return "El RefIn2 no puede ser 0.";

        return null;
    }
}