﻿using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.General;

namespace MigracionSAPParteTecnica.Data.Servin
{
    public class ServinContext : DbContext
    {
        public ServinContext(DbContextOptions<ServinContext> options)
            : base(options)
        {

        }
        public DbSet<ClienteExternoDTO> ClientesExternos { get; set; }
        public DbSet<MaterialesDTO> MaterialesBruto { get; set; }
        public DbSet<MaestroBrutoDTO> MotivosBruto { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ClienteExternoDTO>(entity =>
            {
                entity.HasNoKey();
                entity.ToView("SAP_MigracionDatosTecnicos_Clientes");

                entity.Property(e => e.CodigoIn2).HasColumnName("CodigoIn2");
                entity.Property(e => e.CodigoSapAgrupado).HasColumnName("CodigoSapAgrupado");
                entity.Property(e => e.Nombre).HasColumnName("Nombre");
                entity.Property(e => e.Fiscal).HasColumnName("Fiscal");
                entity.Property(e => e.CodigoSapDestinatario).HasColumnName("DestinatariosSap");
            });

            modelBuilder.Entity<MaterialesDTO>(entity =>
            {
                entity.HasNoKey();
                entity.ToView("SAP_TecnicoMateriales");

                entity.Property(e => e.Codigo).HasColumnName("CODIGO");
                entity.Property(e => e.Descripcion).HasColumnName("DESCRIP");
                entity.Property(e => e.Tipo).HasColumnName("Tipo");
            });

            modelBuilder.Entity<MaestroBrutoDTO>(entity =>
            {
                entity.HasNoKey();
                entity.ToView("SAP_V2_CV_TecnicoMotivos");

                entity.Property(e => e.Motivo).HasColumnName("Motivo");
                entity.Property(e => e.Cliente_IN2).HasColumnName("Cliente_IN2");
                entity.Property(e => e.Cliente_SAP).HasColumnName("Cliente_SAP");
                entity.Property(e => e.RefMotivoCliente).HasColumnName("RefMotivoCliente");
                entity.Property(e => e.Marca).HasColumnName("Marca");
                entity.Property(e => e.Descrip).HasColumnName("Descrip");
                entity.Property(e => e.TipoProducto).HasColumnName("TipoProducto");
                entity.Property(e => e.Embuticion).HasColumnName("Embuticion");
                entity.Property(e => e.Fotolito).HasColumnName("Fotolito");
                entity.Property(e => e.PruebaFisica).HasColumnName("PruebaFisica");
                entity.Property(e => e.Gtin).HasColumnName("Gtin");
                entity.Property(e => e.TipoFlejado).HasColumnName("TipoFlejado");
                entity.Property(e => e.Producto).HasColumnName("Producto");
                entity.Property(e => e.DiametroReal).HasColumnName("DiametroReal");
                entity.Property(e => e.AlturaElemento).HasColumnName("AlturaElemento");
                entity.Property(e => e.Desarrollo).HasColumnName("Desarrollo");
                entity.Property(e => e.ResIzq).HasColumnName("ResIzq");
                entity.Property(e => e.ResDcha).HasColumnName("ResDcha");
                entity.Property(e => e.ResInf).HasColumnName("ResInf");
                entity.Property(e => e.ResSup).HasColumnName("ResSup");
                entity.Property(e => e.IdFormato).HasColumnName("IdFormato");
                entity.Property(e => e.Plano).HasColumnName("Plano");
                entity.Property(e => e.NumeroCuerpos).HasColumnName("NumeroCuerpos");
                entity.Property(e => e.LargoHoja).HasColumnName("LargoHoja");
                entity.Property(e => e.AnchoHoja).HasColumnName("AnchoHoja");
                entity.Property(e => e.HojaScroll).HasColumnName("HojaScroll");
                entity.Property(e => e.LargoScroll).HasColumnName("LargoScroll");
                entity.Property(e => e.PedidoEjemplo).HasColumnName("PedidoEjemplo");
                entity.Property(e => e.Tipo).HasColumnName("Tipo");
                entity.Property(e => e.Tratamiento).HasColumnName("Tratamiento");
            });
        }
    }
}