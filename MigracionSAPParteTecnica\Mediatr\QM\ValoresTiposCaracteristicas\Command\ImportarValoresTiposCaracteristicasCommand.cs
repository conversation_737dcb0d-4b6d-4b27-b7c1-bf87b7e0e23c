﻿//using ClosedXML.Excel;
//using MediatR;
//using Microsoft.EntityFrameworkCore;
//using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
//using MigracionSAPParteTecnica.DTO.QM;
//using MigracionSAPParteTecnica.DTO.ResponseModels;
//using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.QM;
//using MigracionSAPParteTecnica.Services;
//using MigracionSAPParteTecnica.Validators.QM;
//using Nelibur.ObjectMapper;

//namespace MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Command;

//public class ImportarValoresTiposCaracteristicasCommand : IRequest<Result>
//{
//    public Stream ExcelStream { get; }

//    public ImportarValoresTiposCaracteristicasCommand(Stream excelStream)
//    {
//        ExcelStream = excelStream;
//    }
//}

//internal class ImportarValoresTiposCaracteristicasHandler : IRequestHandler<ImportarValoresTiposCaracteristicasCommand, Result>
//{
//    private readonly MigracionSAPParteTecnicaContext _context;
//    private readonly IAuditoriaService _auditoriaService;

//    public ImportarValoresTiposCaracteristicasHandler(
//        MigracionSAPParteTecnicaContext context,
//        IAuditoriaService auditoriaService)
//    {
//        _context = context;
//        _auditoriaService = auditoriaService;
//    }

//    public async Task<Result> Handle(ImportarValoresTiposCaracteristicasCommand request, CancellationToken cancellationToken)
//    {
//        var result = new Result();
//        var errores = new List<string>();
//        int insertados = 0, actualizados = 0, descartados = 0;

//        try
//        {
//            using var workbook = new XLWorkbook(request.ExcelStream);
//            var worksheet = workbook.Worksheet(1);

//            int filaActual = 6;

//            while (!worksheet.Cell(filaActual, 2).IsEmpty()) // mientras haya CodTipo en columna B
//            {
//                var dto = new ValoresTiposCaracteristicasDTO
//                {
//                    //CodTipo = worksheet.Cell(filaActual, 2).GetString().Trim(),          // B
//                    //Caracteristica = worksheet.Cell(filaActual, 3).GetString().Trim(),   // C
//                    MetodoIns = metodo.Id,
//                    Valor = worksheet.Cell(filaActual, 4).GetString().Trim(),            // D
//                    TextoBreve = worksheet.Cell(filaActual, 5).GetString().Trim(),  // E
//                    Borrado = false                 // F
//                };

//                var error = ValorTipoCaracteristicaValidator.Validar(dto);
//                if (error != null)
//                {
//                    errores.Add($"Fila {filaActual}: {error}");
//                    descartados++;
//                    filaActual++;
//                    continue;
//                }

//                var existente = await _context.ValoresTiposCaracteristicas
//                    .FirstOrDefaultAsync(e =>
//                        e.Id == dto.Id &&
//                        e.Valor == dto.Valor, cancellationToken);

//                var clave = $"{dto.TipoCaracteristicaId}_{dto.Valor}";
//                var idAuditoria = clave.GetHashCode();

//                if (existente is null)
//                {
//                    var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.ValoresTiposCaracteristicas>(dto);
//                    await _context.ValoresTiposCaracteristicas.AddAsync(entidad, cancellationToken);
//                    await _context.SaveChangesAsync(cancellationToken);

//                    await _auditoriaService.RegistrarAuditoriaAsync(
//                        "ValoresTiposCaracteristicas", idAuditoria, "INSERT", "Importado desde Excel", cancellationToken);

//                    insertados++;
//                }
//                else
//                {
//                    var originalTexto = existente.TextoBreve;
//                    var originalBorrado = existente.Borrado;

//                    TinyMapper.Map(dto, existente);
//                    await _context.SaveChangesAsync(cancellationToken);

//                    if (originalTexto != existente.TextoBreve || originalBorrado != existente.Borrado)
//                    {
//                        await _auditoriaService.RegistrarAuditoriaAsync(
//                            "ValoresTiposCaracteristicas", idAuditoria, "UPDATE", "Actualizado desde Excel", cancellationToken);
//                        actualizados++;
//                    }
//                }

//                filaActual++;
//            }

//            result.Success = true;
//            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
//            result.Errors = errores;
//        }
//        catch (Exception ex)
//        {
//            result.Success = false;
//            result.Message = $"Error al importar valores tipos características: {ex.Message}";
//        }

//        return result;
//    }
//}