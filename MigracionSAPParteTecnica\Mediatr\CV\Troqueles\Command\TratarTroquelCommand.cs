﻿using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Hubs;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Command;

public class TratarTroquelCommand : IRequest<SingleResult<TroquelesDTO>>
{
    public TroquelesDTO TroquelDTO { get; }

    public TratarTroquelCommand(TroquelesDTO troquelDTO)
    {
        TroquelDTO = troquelDTO;
    }
}

internal class TratarTroquelCommandHandler : IRequestHandler<TratarTroquelCommand, SingleResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHubContext<AppEventsHub> _hubContext;

    public TratarTroquelCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor, IHubContext<AppEventsHub> hubContext)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _hubContext = hubContext;
    }

    private void CopiarAOrigen(TroquelesDTO t)
    {
        t.Cliente_IN2_Origen = t.Cliente_IN2;
        t.Cliente_SAP_Origen = t.Cliente_SAP;
        t.Producto_Origen = t.Familia;
        t.DiametroReal_Origen = t.DiametroReal;
        t.AlturaElemento_Origen = t.AlturaElemento;
        t.Desarrollo_Origen = t.Desarrollo;
        t.ResIzq_Origen = t.ReservasIzquierda;
        t.ResDcha_Origen = t.ReservasDerecha;
        t.ResInf_Origen = t.ReservasInferiores;
        t.ResSup_Origen = t.ReservasSuperiores;
        t.Embuticion_Origen = t.Embuticion;
        t.IdFormato_Origen = t.IdFormatoIn2;
        t.Plano_Origen = t.PlanoEjemplo;
    }

    public async Task<SingleResult<TroquelesDTO>> Handle(TratarTroquelCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<TroquelesDTO>
        {
            Data = new TroquelesDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            if (request.TroquelDTO.Id == 0)
            {
                CopiarAOrigen(request.TroquelDTO);

                var nuevoTroquel = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Troqueles>(request.TroquelDTO);

                await _context.Troqueles.AddAsync(nuevoTroquel, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                request.TroquelDTO.Id = nuevoTroquel.Id;
                result.Data = request.TroquelDTO;


                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "CV_Troqueles",
                    IdRegistro = nuevoTroquel.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevoTroquel),
                    Comentarios = $"Alta de troquel con Id '{nuevoTroquel.Id}'"
                }, cancellationToken);

                await _hubContext.Clients.All.SendAsync(
                    "EventoAplicacion",
                    new
                    {
                        Tipo = "TroquelModificado",
                        Id = result.Data.Id,
                        Cliente = result.Data.Cliente_SAP,
                        Usuario = usuario
                    }
                );
            }
            else
            {
                var currentTroquel = await _context.Troqueles
                    .FirstOrDefaultAsync(t => t.Id == request.TroquelDTO.Id, cancellationToken);

                if (currentTroquel != null)
                {
                    var valoresAntes = JsonSerializer.Serialize(currentTroquel);
                    var updatedTroquel = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Troqueles>(request.TroquelDTO);

                    // Detectar cambios en campos _Origen
                    bool origenModificado =
                        currentTroquel.Cliente_IN2 != currentTroquel.Cliente_IN2_Origen ||
                        currentTroquel.Cliente_SAP != currentTroquel.Cliente_SAP_Origen ||
                        currentTroquel.Familia != currentTroquel.Producto_Origen ||
                        currentTroquel.DiametroReal != currentTroquel.DiametroReal_Origen ||
                        currentTroquel.AlturaElemento != currentTroquel.AlturaElemento_Origen ||
                        currentTroquel.Desarrollo != currentTroquel.Desarrollo_Origen ||
                        currentTroquel.ReservasIzquierda != currentTroquel.ResIzq_Origen ||
                        currentTroquel.ReservasDerecha != currentTroquel.ResDcha_Origen ||
                        currentTroquel.ReservasInferiores != currentTroquel.ResInf_Origen ||
                        currentTroquel.ReservasSuperiores != currentTroquel.ResSup_Origen ||
                        currentTroquel.Embuticion != currentTroquel.Embuticion_Origen ||
                        currentTroquel.IdFormatoIn2 != currentTroquel.IdFormato_Origen;
                        //currentTroquel.PlanoEjemplo != currentTroquel.Plano_Origen;

                    // Marca el flag en el DTO para que llegue a la UI
                    request.TroquelDTO.OrigenModificado = origenModificado;

                    currentTroquel.OrigenModificado = origenModificado;

                    // Si hay cambio, registra auditoría especial
                    if (origenModificado)
                    {
                        await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE-ORIGEN-MODIFICADO",
                            Tabla = "CV_Troqueles",
                            IdRegistro = currentTroquel.Id,
                            DatosAntes = valoresAntes,
                            DatosDespues = JsonSerializer.Serialize(request.TroquelDTO),
                            Comentarios = $"[ALERTA] Se han modificado campos *_Origen* en troquel ID {currentTroquel.Id}"
                        }, cancellationToken);
                    }

                    _context.Entry(currentTroquel).CurrentValues.SetValues(updatedTroquel);
                    currentTroquel.OrigenModificado = origenModificado;
                    await _context.SaveChangesAsync(cancellationToken);

                    result.Data = request.TroquelDTO;

                    var valoresDespues = JsonSerializer.Serialize(currentTroquel);

                    // Auditoría normal
                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "UPDATE",
                        Tabla = "CV_Troqueles",
                        IdRegistro = currentTroquel.Id,
                        DatosAntes = valoresAntes,
                        DatosDespues = valoresDespues,
                        Comentarios = $"Modificación de troquel con Id '{currentTroquel.Id}'"
                    }, cancellationToken);

                    await _hubContext.Clients.All.SendAsync(
                        "EventoAplicacion",
                        new
                        {
                            Tipo = "TroquelModificado",
                            Id = result.Data.Id,
                            Cliente = result.Data.Cliente_SAP,
                            Usuario = usuario
                        }
                    );
                }
                else
                {
                    result.Errors.Add("Troquel no encontrado.");
                }
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarTroquel - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}