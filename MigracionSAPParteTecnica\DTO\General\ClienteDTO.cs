﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;

namespace MigracionSAPParteTecnica.DTO.General;
public class ClienteDTO
{
    public int CodigoIn2 { get; set; }
    public int CodigoSapAgrupado { get; set; }
    public string Nombre { get; set; }
    public string Fiscal { get; set; }
    public virtual List<DestinatarioSapDTO> DestinatarioSap { get; set; } = new List<DestinatarioSapDTO>();
}