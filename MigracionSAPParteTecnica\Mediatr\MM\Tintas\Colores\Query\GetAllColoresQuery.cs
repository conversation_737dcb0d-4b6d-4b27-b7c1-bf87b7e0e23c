﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Query;
public class GetAllColoresQuery : IRequest<ListResult<ColoresDTO>>
{
}

internal class GetAllColoresQueryHandler : IRequestHandler<GetAllColoresQuery, ListResult<ColoresDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllColoresQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ColoresDTO>> Handle(GetAllColoresQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ColoresDTO>
        {
            Data = new List<ColoresDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listColores= await _migracionSAPParteTecnicaContext.Colores
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ColoresDTO>>(listColores).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllColoresQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}