﻿using System.Globalization;

namespace MigracionSAPParteTecnica.Helpers;
public class Tro<PERSON><PERSON><PERSON><PERSON>
{
    public static string GenerarClaveTecnica(int? cliente, string? familia, decimal diametro, decimal altura, decimal desarrollo, decimal resIzq, decimal resDcha, decimal resSup, decimal resInf, bool embuticion)
    {
        return "_" +
                (cliente ?? 0).ToString() + "_" +
                (familia ?? "").Trim().ToUpper() + "_" +
                diametro.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                altura.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                desarrollo.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                resIzq.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                resDcha.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                resSup.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                resInf.ToString("0.00", CultureInfo.InvariantCulture) + "_" +
                (embuticion ? "S" : "N");
    }
}