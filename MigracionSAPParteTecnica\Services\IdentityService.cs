﻿using MigracionSAPParteTecnica.DTO;
using MigracionSAPParteTecnica.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Client;

namespace MigracionSAPParteTecnica.Services;
public class IdentityService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;

    public IdentityService(UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    /// <summary>
    /// Devuelve todos los usuarios registrados.
    /// </summary>
    public async Task<List<ApplicationUser>> ObtenerUsuariosAsync()
    {
        var usuarios = await _userManager.Users.ToListAsync();

        foreach (var user in usuarios)
        {
            var roles = await _userManager.GetRolesAsync(user);
            user.Roles = roles.ToList(); // Lista completa
        }

        return usuarios;
    }

    /// <summary>
    /// Devuelve todos los roles disponibles.
    /// </summary>
    public async Task<List<string>> ObtenerRolesAsync()
    {
        return await _roleManager.Roles.Select(r => r.Name!).ToListAsync();
    }

    /// <summary>
    /// Devuelve los roles asignados a un usuario.
    /// </summary>
    public async Task<IList<string>> ObtenerRolesDeUsuarioAsync(ApplicationUser user)
    {
        return await _userManager.GetRolesAsync(user);
    }

    /// <summary>
    /// Cambia el rol del usuario (elimina los actuales y asigna uno nuevo).
    /// </summary>
    public async Task CambiarRolUsuarioAsync(ApplicationUser user, string nuevoRol)
    {
        var rolesActuales = await _userManager.GetRolesAsync(user);
        await _userManager.RemoveFromRolesAsync(user, rolesActuales);

        if (!await _roleManager.RoleExistsAsync(nuevoRol))
        {
            await _roleManager.CreateAsync(new IdentityRole(nuevoRol));
        }

        await _userManager.AddToRoleAsync(user, nuevoRol);
    }

    /// <summary>
    /// Edita nombre de usuario y correo.
    /// </summary>
    public async Task EditarUsuarioAsync(ApplicationUser user, string nuevoNombre, string nuevoCorreo)
    {
        user.UserName = nuevoNombre;
        user.Email = nuevoCorreo;

        var result = await _userManager.UpdateAsync(user);
        if (!result.Succeeded)
        {
            throw new Exception($"Error al actualizar el usuario: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }
    }

    /// <summary>
    /// Resetea la contraseña del usuario como administrador.
    /// </summary>
    public async Task ResetearPasswordAsync(ApplicationUser user, string nuevaPassword)
    {
        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        var result = await _userManager.ResetPasswordAsync(user, token, nuevaPassword);

        if (!result.Succeeded)
        {
            throw new Exception($"Error al resetear la contraseña: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }
    }

    /// <summary>
    /// Desactiva al usuario (bloqueo por tiempo indefinido).
    /// </summary>
    public async Task BloquearUsuarioAsync(ApplicationUser user)
    {
        user.LockoutEnd = DateTimeOffset.MaxValue;
        await _userManager.UpdateAsync(user);
    }

    /// <summary>
    /// Reactiva al usuario (quita el bloqueo).
    /// </summary>
    public async Task DesbloquearUsuarioAsync(ApplicationUser user)
    {
        user.LockoutEnd = null;
        await _userManager.UpdateAsync(user);
    }

    /// <summary>
    /// Elimina al usuario completamente.
    /// </summary>
    public async Task EliminarUsuarioAsync(ApplicationUser user)
    {
        var result = await _userManager.DeleteAsync(user);
        if (!result.Succeeded)
        {
            throw new Exception($"Error al eliminar el usuario: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }
    }

    public async Task<List<string>> ObtenerTodosLosRolesAsync()
    {
        return await _roleManager.Roles.Select(r => r.Name!).ToListAsync();
    }

    public async Task<List<string>> ObtenerRolesUsuarioAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
            throw new Exception("Usuario no encontrado.");

        return (await ObtenerRolesDeUsuarioAsync(user)).ToList();
    }

    public async Task AsignarRolesUsuarioAsync(string userId, List<string> nuevosRoles)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
            throw new Exception("Usuario no encontrado.");

        var rolesActuales = await _userManager.GetRolesAsync(user);

        // Eliminamos los roles actuales
        var resultadoEliminacion = await _userManager.RemoveFromRolesAsync(user, rolesActuales);
        if (!resultadoEliminacion.Succeeded)
        {
            throw new Exception($"Error al eliminar roles: {string.Join(", ", resultadoEliminacion.Errors.Select(e => e.Description))}");
        }

        // Nos aseguramos de que todos los roles existan antes de asignarlos
        foreach (var rol in nuevosRoles)
        {
            if (!await _roleManager.RoleExistsAsync(rol))
            {
                await _roleManager.CreateAsync(new IdentityRole(rol));
            }
        }

        // Asignamos los nuevos roles
        var resultadoAsignacion = await _userManager.AddToRolesAsync(user, nuevosRoles);
        if (!resultadoAsignacion.Succeeded)
        {
            throw new Exception($"Error al asignar nuevos roles: {string.Join(", ", resultadoAsignacion.Errors.Select(e => e.Description))}");
        }
    }

    public async Task CrearUsuarioAsync(ApplicationUser user, string password)
    {
        var result = await _userManager.CreateAsync(user, password);
        if (!result.Succeeded)
            throw new Exception("Error al crear el usuario: " + string.Join(", ", result.Errors.Select(e => e.Description)));

        if (user.Roles?.Any() == true)
        {
            await AsignarRolesUsuarioAsync(user.Id, user.Roles);
        }
    }
}