﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.DatosMaestros.Query;
public class GetMaterialesByTipoQuery : IRequest<ListResult<MaterialesDTO>>
{
    public string Tipo { get; }

    public GetMaterialesByTipoQuery(string tipo)
    {
        Tipo = tipo.ToUpper();
    }
}

internal class GetMaterialesByTipoQueryHandler : IRequestHandler<GetMaterialesByTipoQuery, ListResult<MaterialesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetMaterialesByTipoQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MaterialesDTO>> Handle(GetMaterialesByTipoQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MaterialesDTO>
        {
            Data = new List<MaterialesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var materiales = await _migracionSAPParteTecnicaContext.Materiales
                .AsNoTracking()
                .Where(m => m.Tipo != null && m.Tipo.ToUpper() == request.Tipo)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<MaterialesDTO>>(materiales);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"ERROR GetMaterialesByTipoQuery ({request.Tipo}): {ex.Message}");
        }

        return result;
    }
}