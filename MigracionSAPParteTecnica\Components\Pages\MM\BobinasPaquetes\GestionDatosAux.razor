﻿@page "/GestionDatosAux"
@attribute [Authorize(Roles = "materiales, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var metodosInspeccion = (DatosAuxMaterialesDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(metodosInspeccion)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="TipoMaterial" Caption="Tipo de Material" />
        <DxGridDataColumn FieldName="Medidas" Caption="Medidas" />
        <DxGridDataColumn FieldName="EspesorReal" Caption="Espesor real" />
        <DxGridDataColumn FieldName="AnchoReal" Caption="Ancho real" />
        <DxGridDataColumn FieldName="LargoReal" Caption="Largo real" />
        <DxGridDataColumn FieldName="LargoScroll" Caption="Largo Scroll" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.DatosAuxMateriales"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/DatosAuxMateriales"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditDatosAuxPopUp @ref="editDatosAux" OnSave="GuardarCambios"/>

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<DatosAuxMaterialesDTO> Data = new();
    string GridSearchText = "";
    private EditDatosAuxPopUp? editDatosAux;
    private DatosAuxMaterialesDTO? selectedDatosAux { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (DatosAuxMaterialesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllDatosAux();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<DatosAuxMaterialesDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedDatosAux = dataItem as DatosAuxMaterialesDTO ?? new DatosAuxMaterialesDTO();
        editDatosAux?.AbrirPopUp(selectedDatosAux);
    }

    async Task GuardarCambios(DatosAuxMaterialesDTO updatedDatosAuxMaterialesDTO)
    {
        _isLoading = true;
        selectedDatosAux = updatedDatosAuxMaterialesDTO;

        var result = await MigracionSAPParteTecnicaService.TratarDatosAuxMateriales(selectedDatosAux);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (DatosAuxMaterialesDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteDatoAux(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Dato auxiliar eliminado correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}