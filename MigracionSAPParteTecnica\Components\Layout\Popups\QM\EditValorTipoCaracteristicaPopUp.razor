﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editValorTipoCaracteristica?.Id > 0)
{
    <EntityLockManager EntityType="ValoresTiposCaracteristicas"
                       EntityId="editValorTipoCaracteristica.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Valores Tipo Característica"
         Width="500px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este valor de tipo de característica está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editValorTipoCaracteristica" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator/>
            <DxFormLayout Data="@editValorTipoCaracteristica" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código tipo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-diagram-3" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox Data="@TiposCaracteristicasList"
                                        Value="TipoCaracteristicaSeleccionada"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((TipoCaracteristicaComboItem? selected) => OnTipoCaracteristicaChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Valor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-tag" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editValorTipoCaracteristica.Valor" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.ValorValoresTipos" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Texto breve valor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-chat-left-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editValorTipoCaracteristica.TextoBreve" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.DescripcionValorValoresTipo" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<ValoresTiposCaracteristicasDTO> OnSave { get; set; }
    [Parameter] public List<TipoCaracteristicaComboItem> TiposCaracteristicasList { get; set; } = new();
    [Parameter] public TipoCaracteristicaComboItem? TipoCaracteristicaSeleccionada { get; set; }
    private ValoresTiposCaracteristicasDTO editValorTipoCaracteristica = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(ValoresTiposCaracteristicasDTO valoresTipoCaracteristicas)
    {
        editValorTipoCaracteristica = valoresTipoCaracteristicas;
        if (TiposCaracteristicasList != null && TiposCaracteristicasList.Any())
        {
            TipoCaracteristicaSeleccionada = TiposCaracteristicasList
                .FirstOrDefault(c => c.Id == editValorTipoCaracteristica.TipoCaracteristicaId);
        }

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    private void OnTipoCaracteristicaChanged(TipoCaracteristicaComboItem? selected)
    {
        TipoCaracteristicaSeleccionada = selected;

        if (selected != null)
        {
            editValorTipoCaracteristica.TipoCaracteristicaId = selected.Id;
        }
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = ValorTipoCaracteristicaValidator.Validar(editValorTipoCaracteristica);
        if (error is not null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editValorTipoCaracteristica);
        IsPopupVisible = false;
        ToastService.MostrarOk("Valor de tipo de característica guardado correctamente.");
    }
}