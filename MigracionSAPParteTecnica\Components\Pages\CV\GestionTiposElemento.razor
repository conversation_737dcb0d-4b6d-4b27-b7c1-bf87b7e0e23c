﻿@page "/GestionTiposElemento"
@attribute [Authorize(Roles = "preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="true"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var tipoElemento = (TipoElementoDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(tipoElemento)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Codigo" Caption="Código SAP" />
        <DxGridDataColumn FieldName="Descripcion" Caption="Descripción SAP" />
    </Columns>
</DxGrid>
<EditTipoElementoPopUp @ref="editTipoElementoPopUp" OnSave="GuardarCambios" />

@code {
    bool _isLoading;
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<TipoElementoDTO> Data = new();
    string GridSearchText = "";
    private EditTipoElementoPopUp? editTipoElementoPopUp;
    private TipoElementoDTO? selectedTipoElemento { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllTiposElemento();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<TipoElementoDTO>(result.Data);
            StateHasChanged();
        }
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (TipoElementoDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    void AbrirPopUp(object dataItem)
    {
        selectedTipoElemento = dataItem as TipoElementoDTO ?? new TipoElementoDTO();
        editTipoElementoPopUp?.AbrirPopUp(selectedTipoElemento);
    }

    async Task GuardarCambios(TipoElementoDTO updatedTipoElemento)
    {
        _isLoading = true;
        selectedTipoElemento = updatedTipoElemento;

        var result = await MigracionSAPParteTecnicaService.TratarTipoElemento(selectedTipoElemento);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (TipoElementoDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteTipoElemento(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Tipo de elemento eliminado correctamente.");
        }

        _isLoading = false;
    }
}