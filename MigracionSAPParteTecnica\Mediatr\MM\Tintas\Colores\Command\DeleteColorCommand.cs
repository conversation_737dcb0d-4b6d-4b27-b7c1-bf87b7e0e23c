﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;

public class DeleteColorCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteColorCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteColorCommandHandler : IRequestHandler<DeleteColorCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteColorCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteColorCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var color = await _migracionSAPParteTecnicaContext.Colores
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (color == null)
            {
                result.Errors.Add("No se ha encontrado el color a borrar.");
                return result;
            }

            color.Borrado = true;

            _migracionSAPParteTecnicaContext.Colores.Update(color);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;


            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Tintas_Colores",
                IdRegistro = color.Id,
                DatosAntes = JsonSerializer.Serialize(color),
                DatosDespues = null,
                Comentarios = $"Eliminación lógica del color: Id='{color.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteColorCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}