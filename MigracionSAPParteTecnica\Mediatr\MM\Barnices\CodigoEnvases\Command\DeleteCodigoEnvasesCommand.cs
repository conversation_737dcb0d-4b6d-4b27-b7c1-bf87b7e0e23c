﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;

public class DeleteCodigoEnvasesCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteCodigoEnvasesCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteCodigoEnvasesCommandHandler : IRequestHandler<DeleteCodigoEnvasesCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteCodigoEnvasesCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteCodigoEnvasesCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var codigoEnvase = await _migracionSAPParteTecnicaContext.CodigoEnvases
                .FirstOrDefaultAsync(c => c.Id == request.Id , cancellationToken);

            if (codigoEnvase == null)
            {
                result.Errors.Add("No se ha encontrado el código de envase a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(codigoEnvase);

            codigoEnvase.Borrado = true;

            _migracionSAPParteTecnicaContext.CodigoEnvases.Update(codigoEnvase);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_CodigoEnvases",
                IdRegistro = codigoEnvase.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de código envase: Id={codigoEnvase.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteCodigoEnvasesCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}