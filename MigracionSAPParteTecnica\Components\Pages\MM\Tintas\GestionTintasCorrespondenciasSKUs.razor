﻿@page "/GestionTintasCorrespondenciasSKUs"
@attribute [Authorize(Roles = "materiales, admin, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var correspondencia = (CorrespondenciasSkusDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(correspondencia)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false"/>
        <DxGridDataColumn FieldName="CodigoDescripcion" Caption="Tinta" />
        <DxGridDataColumn FieldName="CodigoProveedor" Caption="Código proveedor">
            <CellDisplayTemplate Context="context">
                @{
                    var codProveedor = ((CorrespondenciasSkusDTO)context.DataItem)?.CodigoProveedor;
                }
                <span>@(codProveedor?.ToString() ?? "")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Pl" Caption="PL" />
        <DxGridDataColumn FieldName="PesoNeto" Caption="Peso Neto" />
        <DxGridDataColumn FieldName="Consigna" Caption="Consigna">
            <CellDisplayTemplate Context="context">
                @{
                    var consigna = ((CorrespondenciasSkusDTO)context.DataItem)?.Consigna ?? false;
                }
                <span>@(consigna ? "Sí" : "No")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.CorrespondenciasSkus"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/CorrespondenciasSkus"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditCorrespondenciasSKUsPopUp @ref="editCorrespondenciasSKUsPopUp" OnSave="GuardarCambios" MaterialesList="MaterialesList" MaterialSeleccionado="MaterialSeleccionado" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<CorrespondenciasSkusDTO> Data = new();
    string GridSearchText = "";
    private EditCorrespondenciasSKUsPopUp? editCorrespondenciasSKUsPopUp;
    private CorrespondenciasSkusDTO? selectedCorrespondencia { get; set; }
    private List<MaterialComboItem> MaterialesList = new();
    private MaterialComboItem? MaterialSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMateriales();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (CorrespondenciasSkusDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllCorrespondenciasSKUs();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<CorrespondenciasSkusDTO>(result.Data);

            foreach (var item in Data)
            {
                item.CodigoDescripcion = MaterialesList
                    .FirstOrDefault(m => m.Codigo == item.CodigoTinta)?.Display;
            }

            StateHasChanged();
        }
    }

    private async Task CargarMateriales()
    {
        var result = await MigracionSAPParteTecnicaService.GetMaterialesByTipo("TINTA");
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MaterialesList = result.Data.Select(m => new MaterialComboItem
            {
                Codigo = m.Codigo,
                Nombre = m.Descripcion ?? ""
            }).ToList();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedCorrespondencia = dataItem as CorrespondenciasSkusDTO ?? new CorrespondenciasSkusDTO();

        MaterialSeleccionado = MaterialesList
         .FirstOrDefault(c => c.Codigo == selectedCorrespondencia.CodigoTinta);

        editCorrespondenciasSKUsPopUp?.AbrirPopUp(selectedCorrespondencia);
    }

    async Task GuardarCambios(CorrespondenciasSkusDTO updatedCorrespondenciaSKU)
    {
        _isLoading = true;
        selectedCorrespondencia = updatedCorrespondenciaSKU;

        var result = await MigracionSAPParteTecnicaService.TratarCorrespondenciaSKUs(selectedCorrespondencia);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (CorrespondenciasSkusDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteCorrespondenciaSKU(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Correspondencia eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}