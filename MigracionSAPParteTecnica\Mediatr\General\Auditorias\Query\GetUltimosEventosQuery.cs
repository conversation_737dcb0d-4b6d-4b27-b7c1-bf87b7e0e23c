﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.Auditorias.Query;
public class GetUltimosEventosQuery : IRequest<ListResult<AuditoriaDTO>>
{
}

internal class GetUltimosEventosQueryHandler : IRequestHandler<GetUltimosEventosQuery, ListResult<AuditoriaDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetUltimosEventosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<AuditoriaDTO>> Handle(GetUltimosEventosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<AuditoriaDTO>
        {
            Data = new List<AuditoriaDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listRecientes = await _migracionSAPParteTecnicaContext.Auditorias
                .AsNoTracking()
                .OrderByDescending(a => a.Fecha)
                .Take(100)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<AuditoriaDTO>>(listRecientes).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetUltimosEventosQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}