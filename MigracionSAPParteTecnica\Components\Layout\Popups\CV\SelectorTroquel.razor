﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

<DxPopup @bind-Visible="Visible"
         ShowCloseButton="true"
         CloseOnOutsideClick="true"
         HeaderText="Seleccionar troquel"
         Width="1000px"
         Height="700px">
    <div style="padding:16px;">
        <DxGrid Data="@Troqueles" ShowFilterRow="true" SelectionMode="GridSelectionMode.Single"
                SelectedDataItem="@Selected" TextWrapEnabled="false" PageSize="15" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
                SelectedDataItemChanged="@(row => OnSelectedChanged((TroquelesDTO)row))">
            <Columns>
                <DxGridSelectionColumn Width="40px" FixedPosition="GridColumnFixedPosition.Left" />
                <DxGridDataColumn FieldName="Id" FixedPosition="GridColumnFixedPosition.Left" Caption="Id" Width="50px" />
                <DxGridDataColumn FieldName="Cliente_SAP" Caption="Cliente" Width="90px" />
                <DxGridDataColumn FieldName="TipoElemento" Caption="Tipo elemento" Width="130px" />
                <DxGridDataColumn FieldName="DiametroEnvase" Caption="Diámetro Envase" Width="160px" />
                <DxGridDataColumn FieldName="DiametroReal" Caption="Diámetro Real" Width="140px" />
                <DxGridDataColumn FieldName="AlturaEnvase" Caption="Altura envase" Width="140px" />
                <DxGridDataColumn FieldName="AlturaElemento" Caption="Altura elemento" Width="150px" />
                <DxGridDataColumn FieldName="Desarrollo" Caption="Desarrollo" Width="120px" />
                <DxGridDataColumn FieldName="ReservasIzquierda" Caption="ResIzq" Width="90px" />
                <DxGridDataColumn FieldName="ReservasDerecha" Caption="ResDcha" Width="110px" />
                <DxGridDataColumn FieldName="ReservasSuperiores" Caption="ResSup" Width="100px" />
                <DxGridDataColumn FieldName="ReservasInferiores" Caption="ResInf" Width="90px" />
                <DxGridDataColumn FieldName="EmbuticionTexto" Caption="Embutición" Width="120px" />
                <DxGridDataColumn FieldName="@nameof(TroquelesDTO.Descripcion)" Caption="Descripción" Width="250px" />
                <!-- Más columnas si quieres -->
            </Columns>
        </DxGrid>
        <div class="mt-3" style="display:flex;justify-content:flex-end;gap:8px">
            <DxButton Text="Seleccionar" RenderStyle="ButtonRenderStyle.Primary" Click="Confirmar" Enabled="Selected!=null"/>
            <DxButton Text="Cancelar" RenderStyle="ButtonRenderStyle.Secondary" Click="Cancelar"/>
        </div>
    </div>
</DxPopup>

@code {
    [Parameter] public bool Visible { get; set; }
    [Parameter] public EventCallback<bool> VisibleChanged { get; set; }
    [Parameter] public EventCallback<TroquelesDTO> OnSeleccionado { get; set; }

    private List<TroquelesDTO> Troqueles = new();
    private TroquelesDTO Selected;

    protected override async Task OnParametersSetAsync()
    {
        if (Visible && Troqueles.Count == 0)
        {
            // Solo carga una vez. Si necesitas recargar cada vez, quita el "Troqueles.Count == 0"
            Troqueles = (await MigracionSAPParteTecnicaService.GetAllTroqueles()).Data?.ToList() ?? new();
        }
    }

    private void OnSelectedChanged(TroquelesDTO troquel)
    {
        Selected = troquel;
    }

    private async Task Confirmar()
    {
        await OnSeleccionado.InvokeAsync(Selected);
        await VisibleChanged.InvokeAsync(false);
    }

    private async Task Cancelar()
    {
        await VisibleChanged.InvokeAsync(false);
    }
}