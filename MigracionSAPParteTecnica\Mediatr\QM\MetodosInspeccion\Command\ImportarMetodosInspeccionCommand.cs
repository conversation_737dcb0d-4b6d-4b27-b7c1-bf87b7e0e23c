﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.QM;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Command;
public class ImportarMetodosInspeccionCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarMetodosInspeccionCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarMetodosInspeccionCommandHandler : IRequestHandler<ImportarMetodosInspeccionCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarMetodosInspeccionCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarMetodosInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                var dto = new MetodosInspeccionDTO
                {
                    Nombre = worksheet.Cell(filaActual, 2).GetString().Trim(),    
                    Busqueda = worksheet.Cell(filaActual, 3).GetString().Trim(),  
                    Descripcion = worksheet.Cell(filaActual, 4).GetString().Trim(),
                    TextoBreve = worksheet.Cell(filaActual, 5).GetString().Trim(),
                    Borrado = false
                };

                var error = MetodoInspeccionValidator.Validar(dto);
                if (error != null)
                {
                    errores.Add($"Fila {filaActual}: {error}");
                    descartados++;
                    filaActual++;
                    continue;
                }

                var existente = await _context.MetodosInspeccion
                    .FirstOrDefaultAsync(m => m.Nombre == dto.Nombre, cancellationToken);

                var idAuditoria = dto.Nombre.GetHashCode();

                if (existente is null)
                {
                    var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.MetodosInspeccion>(dto);
                    await _context.MetodosInspeccion.AddAsync(nuevo, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);

                    await _auditoria.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "MetodosInspeccion",
                        IdRegistro = nuevo.Id,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(nuevo),
                        Comentarios = "Importado desde Excel"
                    }, cancellationToken);

                    insertados++;
                }
                else
                {
                    var datosAntes = JsonSerializer.Serialize(existente);
                    TinyMapper.Map(dto, existente);

                    await _context.SaveChangesAsync(cancellationToken);

                    var datosDespues = JsonSerializer.Serialize(existente);

                    if (datosAntes != datosDespues)
                    {
                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "MetodosInspeccion",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = $"Actualizado desde Excel: {existente.Descripcion}"
                        }, cancellationToken);

                        actualizados++;
                    }
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar: {ex.Message}";
        }

        return result;
    }
}