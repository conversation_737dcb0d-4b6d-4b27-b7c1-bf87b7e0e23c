﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;
public class DeleteCaracteristicaInspeccionCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteCaracteristicaInspeccionCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteCaracteristicaInspeccionCommandHandler : IRequestHandler<DeleteCaracteristicaInspeccionCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteCaracteristicaInspeccionCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteCaracteristicaInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var caracteristica = await _migracionSAPParteTecnicaContext.CaracteristicasInspeccion
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if(caracteristica == null)
            {
                result.Errors.Add("No se ha encontrado la característica de inspección a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(caracteristica);

            caracteristica.Borrado = true;

            _migracionSAPParteTecnicaContext.CaracteristicasInspeccion.Update(caracteristica);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "QM_CaracteristicasInspeccion",
                IdRegistro = caracteristica.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de característica de inspección: Id='{caracteristica.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteCaracteristicaInspeccionCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}