﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Entities;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.General;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Barnices;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.General;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Tintas;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.QM;
using System;
using System.Collections.Generic;

namespace MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;

public partial class MigracionSAPParteTecnicaContext : IdentityDbContext<ApplicationUser>
{
    public MigracionSAPParteTecnicaContext(DbContextOptions<MigracionSAPParteTecnicaContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Auditoria> Auditorias { get; set; }

    public virtual DbSet<Imposiciones> Imposiciones { get; set; }

    public virtual DbSet<Maestro> Maestro { get; set; }

    public virtual DbSet<Motivos> Motivos { get; set; }

    public virtual DbSet<Plano> Plano { get; set; }

    public virtual DbSet<TipoElemento> TiposElemento { get; set; }

    public virtual DbSet<Troqueles> Troqueles { get; set; }

    public virtual DbSet<EntityLocks> EntityLocks { get; set; }

    public virtual DbSet<CodigoEnvases> CodigoEnvases { get; set; }

    public virtual DbSet<ExcepcionesPreciosLotes> ExcepcionesPreciosLotes { get; set; }

    public virtual DbSet<Familias> Familias { get; set; }

    public virtual DbSet<Naturaleza> Naturaleza { get; set; }

    public virtual DbSet<NoIncluirRegCompras> NoIncluirRegCompras { get; set; }

    public virtual DbSet<Nodrizas> Nodrizas { get; set; }

    public virtual DbSet<ConsignaNestle> ConsignaNestle { get; set; }

    public virtual DbSet<DatosAuxMateriales> DatosAuxMateriales { get; set; }

    public virtual DbSet<Envases> Envases { get; set; }

    public virtual DbSet<Materiales> Materiales { get; set; }

    public virtual DbSet<Colores> Colores { get; set; }

    public virtual DbSet<CorrespondenciasSkus> CorrespondenciasSkus { get; set; }

    public virtual DbSet<RelacionCompuestas> RelacionCompuestas { get; set; }

    public virtual DbSet<CaracteristicasInspeccion> CaracteristicasInspeccion { get; set; }

    public virtual DbSet<MetodosInspeccion> MetodosInspeccion { get; set; }

    public virtual DbSet<TiposCaracteristicas> TiposCaracteristicas { get; set; }

    public virtual DbSet<ValoresTiposCaracteristicas> ValoresTiposCaracteristicas { get; set; }

    public virtual DbSet<SAPCliente> SAP_Clientes { get; set; }

    public virtual DbSet<SAPClienteDestinatario> SAP_Clientes_Destinatarios { get; set; }

    public virtual DbSet<SAPProveedor> SAP_Proveedores { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Auditoria>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Auditori__3214EC073CBF6A0A");

            entity.Property(e => e.Accion)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Fecha)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Resultado).HasMaxLength(20);
            entity.Property(e => e.Tabla)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Usuario)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<Imposiciones>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CV_Impos__3214EC07A54FC58F");

            entity.ToTable("CV_Imposiciones");

            entity.Property(e => e.AnchoHoja).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.AnchoHoja_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("AnchoHoja_Origen");
            entity.Property(e => e.ArranqueEscuadra).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ArranquePinza).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Cliente_IN2).HasColumnName("Cliente_IN2");
            entity.Property(e => e.Cliente_IN2_Origen).HasColumnName("Cliente_IN2_Origen");
            entity.Property(e => e.Cliente_SAP).HasColumnName("Cliente_SAP");
            entity.Property(e => e.Cliente_SAP_Origen).HasColumnName("Cliente_SAP_Origen");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.LargoHoja).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.LargoHoja_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("LargoHoja_Origen");
            entity.Property(e => e.LargoScroll).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.LargoScroll_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("LargoScroll_Origen");
            entity.Property(e => e.Nota).HasMaxLength(200);
            entity.Property(e => e.Pinza).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Plano).HasMaxLength(50);
            entity.Property(e => e.Plano_Origen)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("Plano_Origen");
            entity.Property(e => e.SentidoLectura).HasMaxLength(2);
            entity.Property(e => e.Tipo).HasMaxLength(50);

            entity.HasOne(d => d.IdTroquelNavigation).WithMany(p => p.Imposiciones)
                .HasForeignKey(d => d.IdTroquel)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CV_Imposiciones_CV_Troqueles");
        });

        modelBuilder.Entity<Maestro>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CV_Motiv__3214EC27647BC5D7");

            entity.ToTable("CV_Maestro");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AlturaElemento)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("Altura_elemento");
            entity.Property(e => e.AnchoHoja).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Cliente_IN2).HasColumnName("Cliente_IN2");
            entity.Property(e => e.Cliente_SAP).HasColumnName("Cliente_SAP");
            entity.Property(e => e.Desarrollo).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Descrip)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.DiametroReal)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("Diametro_real");
            entity.Property(e => e.Fotolito)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Gtin)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.IdFormato)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.LargoHoja).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.LargoScroll).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Marca)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Motivo)
                .HasMaxLength(7)
                .IsUnicode(false);
            entity.Property(e => e.Plano)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Producto)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.PruebaFisica)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.RefMotivoCliente)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ResDcha).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ResInf).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ResIzq).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ResSup).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.SentidoLectura)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Tipo)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.TipoFlejado)
                .HasMaxLength(2)
                .IsUnicode(false);
            entity.Property(e => e.TipoProducto)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Tratamiento)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Motivos>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CV_Motiv__3214EC07FF8D1AA7");

            entity.ToTable("CV_Motivos");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CodigoArchivo)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Descrip)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Gtin)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.IdMotivoPrecedente)
                .HasMaxLength(7)
                .IsUnicode(false);
            entity.Property(e => e.IdTratamiento).HasMaxLength(200);
            entity.Property(e => e.Marca)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.MuestraFisica)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.PosFlejado)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.RefMotivoCliente)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ReferenciaColor)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.TipoProducto)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength();

            entity.HasOne(d => d.IdNavigation).WithOne(p => p.Motivos)
                .HasForeignKey<Motivos>(d => d.Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CV_Motivos_CV_Maestro");

            entity.HasOne(d => d.IdTroquelNavigation).WithMany(p => p.Motivos)
                .HasForeignKey(d => d.IdTroquel)
                .HasConstraintName("FK_CV_Motivos_CV_Troqueles");
        });

        modelBuilder.Entity<Plano>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CV_Plano__3214EC27C3396355");

            entity.ToTable("CV_Plano");

            entity.Property(e => e.Activo).HasDefaultValue(true);
            entity.Property(e => e.NombrePlano)
                .IsRequired()
                .HasMaxLength(255);
        });

        modelBuilder.Entity<TipoElemento>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SAP_CV_T__3214EC07BA2ADCA7");

            entity.ToTable("CV_TiposDeElemento");

            entity.Property(e => e.Codigo)
                .IsRequired()
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Troqueles>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CV_Troqu__16DE8EB2CCBC9166");

            entity.ToTable("CV_Troqueles");

            entity.Property(e => e.AlturaElemento).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.AlturaElemento_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("AlturaElemento_Origen");
            entity.Property(e => e.AlturaEnvase).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ClaveTecnica).HasMaxLength(300);
            entity.Property(e => e.Cliente_IN2).HasColumnName("Cliente_IN2");
            entity.Property(e => e.Cliente_IN2_Origen).HasColumnName("Cliente_IN2_Origen");
            entity.Property(e => e.Cliente_SAP).HasColumnName("Cliente_SAP");
            entity.Property(e => e.Cliente_SAP_Origen).HasColumnName("Cliente_SAP_Origen");
            entity.Property(e => e.Desarrollo).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Desarrollo_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("Desarrollo_Origen");
            entity.Property(e => e.Descripcion).IsUnicode(false);
            entity.Property(e => e.DiametroEnvase).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.DiametroReal).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.DiametroReal_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("DiametroReal_Origen");
            entity.Property(e => e.EjeMayor).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.EjeMenor).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Embuticion_Origen).HasColumnName("Embuticion_Origen");
            entity.Property(e => e.Familia)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.FechaAlta).HasColumnType("datetime");
            entity.Property(e => e.FormatoEnvase)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.IdFormatoIn2)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("IdFormato_IN2");
            entity.Property(e => e.IdFormato_Origen)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("IdFormato_Origen");
            entity.Property(e => e.Observaciones)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.PlanoEjemplo)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Plano_Origen)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("Plano_Origen");
            entity.Property(e => e.Producto_Origen)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("Producto_Origen");
            entity.Property(e => e.ResDcha_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("ResDcha_Origen");
            entity.Property(e => e.ResInf_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("ResInf_Origen");
            entity.Property(e => e.ResIzq_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("ResIzq_Origen");
            entity.Property(e => e.ResSup_Origen)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("ResSup_Origen");
            entity.Property(e => e.ReservasDerecha).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ReservasInferiores).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ReservasIzquierda).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.ReservasSuperiores).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Subfamilia)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Tipo)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.TipoElemento)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.OrigenModificado).HasColumnName("OrigenModificado");
        });

        modelBuilder.Entity<EntityLocks>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__EntityLo__3214EC078DE10793");

            entity.HasIndex(e => new { e.EntityType, e.EntityId }, "UQ_EntityType_EntityId").IsUnique();

            entity.Property(e => e.EntityType)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FechaBloqueo).HasColumnType("datetime");
            entity.Property(e => e.Usuario)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<CodigoEnvases>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC0780AD1249");

            entity.ToTable("MM_Barnices_CodigoEnvases");
        });

        modelBuilder.Entity<ExcepcionesPreciosLotes>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC07A3D9C039");

            entity.ToTable("MM_Barnices_ExcepcionesPreciosLotes");

            entity.Property(e => e.Fecha).HasColumnType("datetime");
            entity.Property(e => e.Precio).HasColumnType("decimal(10, 2)");
        });

        modelBuilder.Entity<Familias>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC07121608AF");

            entity.ToTable("MM_Barnices_Familias");

            entity.Property(e => e.DatoIn2)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DatoIN2");
            entity.Property(e => e.DatoSap)
                .IsRequired()
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("DatoSAP");
            entity.Property(e => e.DescripcionSap)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DescripcionSAP");
        });

        modelBuilder.Entity<Naturaleza>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC07DAA6B1A7");

            entity.ToTable("MM_Barnices_Naturaleza");

            entity.Property(e => e.DatoIn2)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DatoIN2");
            entity.Property(e => e.DatoSap)
                .IsRequired()
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("DatoSAP");
            entity.Property(e => e.DescripcionSap)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("DescripcionSAP");
        });

        modelBuilder.Entity<NoIncluirRegCompras>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC07145E9BAC");

            entity.ToTable("MM_Barnices_NoIncluirRegCompras");
        });

        modelBuilder.Entity<Nodrizas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Barni__3214EC07C6FE4562");

            entity.ToTable("MM_Barnices_Nodrizas");

            entity.Property(e => e.Cantidad).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Fecha).HasColumnType("datetime");
            entity.Property(e => e.Lote)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ConsignaNestle>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Bobin__3214EC07519393BC");

            entity.ToTable("MM_BobinasPaquetes_ConsignaNestle");

            entity.Property(e => e.CodigoPaquete)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<DatosAuxMateriales>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Bobin__3214EC07997ABB17");

            entity.ToTable("MM_BobinasPaquetes_DatosAuxMateriales");

            entity.Property(e => e.AnchoReal).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.EspesorReal).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.LargoReal).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.LargoScroll).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Medidas)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.TipoMaterial)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Envases>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Envas__3214EC079BCE305E");

            entity.ToTable("MM_Envases");

            entity.Property(e => e.Altura).HasColumnType("decimal(10, 3)");
            entity.Property(e => e.Ancho).HasColumnType("decimal(10, 3)");
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Largo).HasColumnType("decimal(10, 3)");
            entity.Property(e => e.Nombre)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.PesoBruto).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.PesoNeto).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.TipoUso)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Materiales>(entity =>
        {
            entity.HasKey(e => e.Codigo).HasName("PK__MM_Mater__06370DADFEA74121");

            entity.ToTable("MM_Materiales");

            entity.Property(e => e.Codigo).ValueGeneratedNever();
            entity.Property(e => e.Descripcion).HasMaxLength(255);
            entity.Property(e => e.Tipo).HasMaxLength(50);
        });

        modelBuilder.Entity<Colores>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Tinta__3214EC070A1CED9C");

            entity.ToTable("MM_Tintas_Colores");

            entity.Property(e => e.CodigoColor)
                .IsRequired()
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Color)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<CorrespondenciasSkus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MM_Tintas_CorrespondenciasSKUS_Id");

            entity.ToTable("MM_Tintas_CorrespondenciasSKUS");

            entity.Property(e => e.PesoNeto).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.Pl)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("PL");
        });

        modelBuilder.Entity<RelacionCompuestas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MM_Tinta__3214EC0704372942");

            entity.ToTable("MM_Tintas_RelacionCompuestas");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Ingrediente)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<CaracteristicasInspeccion>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__QM_Carac__3214EC07F19365AB");

            entity.ToTable("QM_CaracteristicasInspeccion");

            entity.Property(e => e.CampoBusqueda)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.LimiteToleranciaInf).HasColumnType("decimal(10, 4)");
            entity.Property(e => e.LimiteToleranciaSup).HasColumnType("decimal(10, 4)");
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TextoBreve)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.UnidadMedida)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.HasOne(d => d.MetodoInsNavigation).WithMany(p => p.CaracteristicasInspeccion)
                .HasForeignKey(d => d.MetodoIns)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QM_CaracteristicasInspeccion_MetodoIns");
        });

        modelBuilder.Entity<MetodosInspeccion>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__QM_Metod__3214EC0729EE91B2");

            entity.ToTable("QM_MetodosInspeccion");

            entity.Property(e => e.Busqueda)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Descripcion)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.TextoBreve)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<TiposCaracteristicas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__QM_Tipos__3214EC0783D4610F");

            entity.ToTable("QM_TiposCaracteristicas");

            entity.Property(e => e.CodigoTipo)
                .IsRequired()
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Tipo)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ValoresTiposCaracteristicas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__QM_Valor__3214EC078FF39B64");

            entity.ToTable("QM_ValoresTiposCaracteristicas");

            entity.Property(e => e.TextoBreve)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.Valor)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.HasOne(d => d.QmTiposCaracteristicas).WithMany(p => p.QmValoresTiposCaracteristicas)
                .HasForeignKey(d => d.TipoCaracteristicaId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QM_ValoresTiposCaracteristicas_TipoCaracteristicaId");
        });

        modelBuilder.Entity<SAPCliente>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SAP_Clientes_Id");

            entity.ToTable("SAP_Clientes");

            entity.HasIndex(e => e.Codigo_IN2, "UQ_SAP_Clientes_Codigo_IN2").IsUnique();

            entity.Property(e => e.Codigo_IN2).HasColumnName("Codigo_IN2");
            entity.Property(e => e.Codigo_SAP).HasColumnName("Codigo_SAP");
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<SAPClienteDestinatario>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SAP_Clientes_Destinatarios_Id");

            entity.ToTable("SAP_Clientes_Destinatarios");

            entity.Property(e => e.Ciudad)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Codigo_Destinatario_SAP).HasColumnName("Codigo_Destinatario_SAP");
            entity.Property(e => e.Codigo_IN2).HasColumnName("Codigo_IN2");
            entity.Property(e => e.Codigo_Postal)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("Codigo_Postal");
            entity.Property(e => e.Direccion)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Pais)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Provincia)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.Cliente).WithMany(p => p.Destinatarios)
                .HasPrincipalKey(p => p.Codigo_IN2)
                .HasForeignKey(d => d.Codigo_IN2)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SAP_ClientesDestinatarios_Cliente");
        });

        modelBuilder.Entity<SAPProveedor>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SAP_Prov__3214EC07589CE249");

            entity.ToTable("SAP_Proveedores");

            entity.Property(e => e.Clave_Idioma)
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("Clave_Idioma");
            entity.Property(e => e.Codigo_BP).HasColumnName("Codigo_BP");
            entity.Property(e => e.Codigo_IN2).HasColumnName("Codigo_IN2");
            entity.Property(e => e.Codigo_SAP)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Codigo_SAP");
            entity.Property(e => e.Nombre_IN2)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("Nombre_IN2");
            entity.Property(e => e.Nombre_SAP)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("Nombre_SAP");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}