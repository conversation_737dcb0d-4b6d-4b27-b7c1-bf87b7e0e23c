﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editCorrespondencia?.Id > 0)
{
    <EntityLockManager EntityType="CorrespondenciasSkus"
                       EntityId="editCorrespondencia.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar correspondencias SKUs"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        <EditForm Model="@editCorrespondencia" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator/>
            <DxFormLayout Data="@editCorrespondencia" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código de tinta:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/waterdrop-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxComboBox Data="@MaterialesList"
                                        Value="MaterialSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((MaterialComboItem? selected) => OnMaterialChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Código proveedor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editCorrespondencia.CodigoProveedor"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="PL:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editCorrespondencia.Pl" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Peso Neto:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/kilograms-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editCorrespondencia.PesoNeto"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Consigna:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-person-raised-hand" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox @bind-Value="editCorrespondencia.Consigna"
                                        CssClass="popup-demo-textbox full-width"
                                        Data="@ConsignaOpciones"
                                        ValueFieldName="Key"
                                        TextFieldName="Value"
                                        Style="flex-grow: 1;" 
                                        ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<CorrespondenciasSkusDTO> OnSave { get; set; }
    [Parameter] public List<MaterialComboItem> MaterialesList { get; set; } = new();
    [Parameter] public MaterialComboItem? MaterialSeleccionado { get; set; }
    private CorrespondenciasSkusDTO editCorrespondencia = new();
    private List<KeyValuePair<bool, string>> ConsignaOpciones = new()
    {
        new(true, "Sí"),
        new(false, "No")
    };

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(CorrespondenciasSkusDTO corresponciaSKU)
    {
        editCorrespondencia = corresponciaSKU;

        if (MaterialesList != null && MaterialesList.Any())
        {
            MaterialSeleccionado = MaterialesList
                .FirstOrDefault(c => c.Codigo == editCorrespondencia.CodigoTinta);
        }

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private void OnMaterialChanged(MaterialComboItem? selected)
    {
        MaterialSeleccionado = selected;

        if (selected != null)
        {
            editCorrespondencia.CodigoTinta = selected.Codigo;
        }
    }

    private async Task GuardarCambios()
    {
        var error = CorrespondenciasSkusValidator.Validar(editCorrespondencia);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editCorrespondencia);
        IsPopupVisible = false;
        ToastService.MostrarOk("Correspondencia guardada correctamente.");
    }
}