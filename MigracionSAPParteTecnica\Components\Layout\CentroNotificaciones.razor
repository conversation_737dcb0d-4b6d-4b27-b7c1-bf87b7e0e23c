﻿@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService

<div class="position-fixed bottom-0 end-0" style="z-index: 9999; width: 360px;">
    <div class="card shadow border-0 rounded-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center py-2 px-3"
             style="cursor: pointer;" @onclick="ToggleCentro">
            <div>
                <i class="bi bi-chat-dots-fill me-2"></i>
                <strong><PERSON><PERSON><PERSON>s</strong>
            </div>
            <i class="bi @(mostrarCentro ? "bi-chevron-down" : "bi-chevron-up") fs-5"></i>
        </div>

        @if (mostrarCentro)
        {
            <div class="list-group list-group-flush overflow-auto" style="max-height: 300px;">
                @if (!Eventos.Any())
                {
                    <div class="list-group-item text-muted text-center small">No hay eventos que mostrar.</div>
                }
                else
                {
                    @foreach (var evento in Eventos.Take(20))
                    {
                        var badgeClass = evento.Accion switch
                        {
                            "INSERT" => "bg-success",
                            "UPDATE" => "bg-primary",
                            "DELETE" => "bg-danger",
                            "MASS UPDATE" => "bg-warning text-dark",
                            _ => "bg-secondary"
                        };

                        <div class="list-group-item small">
                            <div class="d-flex justify-content-between">
                                <div class="text-muted small">@evento.Fecha.ToString("HH:mm dd/MM/yyyy") - @evento.Usuario</div>
                                <span class="badge @badgeClass">@evento.Accion</span>
                            </div>
                            <div>@evento.Comentarios</div>
                            <div class="text-muted small">@evento.Tabla</div>
                        </div>
                    }
                }
            </div>
        }
    </div>
</div>

@code {
    private List<AuditoriaDTO> Eventos = new();
    private bool mostrarCentro = false;

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(500);
        await CargarEventosAsync();
    }

    private async Task CargarEventosAsync()
    {
        var result = await MigracionSAPParteTecnicaService.GetUltimosEventosQuery();
        if (!result.HasErrors)
        {
            Eventos = result.Data;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void ToggleCentro()
    {
        mostrarCentro = !mostrarCentro;
    }
}