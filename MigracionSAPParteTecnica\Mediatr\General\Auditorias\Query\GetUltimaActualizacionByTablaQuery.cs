﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.Auditorias.Query;

public class GetUltimaActualizacionByTablaQuery : IRequest<SingleResult<AuditoriaDTO>>
{
    public string Tabla {  get; set; }
    public GetUltimaActualizacionByTablaQuery(string tabla)
    {
        Tabla = tabla;
    }
}

internal class GetUltimaActualizacionByTablaQueryHandler : IRequestHandler<GetUltimaActualizacionByTablaQuery, SingleResult<AuditoriaDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetUltimaActualizacionByTablaQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }
    public async Task<SingleResult<AuditoriaDTO>> Handle(GetUltimaActualizacionByTablaQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<AuditoriaDTO>
        {
            Data = null,
            Errors = new List<string>()
        };

        try
        {
            var auditoria = await _migracionSAPParteTecnicaContext.Auditorias
                .Where(a => a.Tabla == request.Tabla && a.Accion == "MASS UPDATE")
                .OrderByDescending(a => a.Fecha)
                .FirstOrDefaultAsync(cancellationToken);

            if (auditoria != null)
            {
                result.Data = TinyMapper.Map<AuditoriaDTO>(auditoria);
            }
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetUltimaActualizacionByTablaQuery - {e.InnerException?.Message ?? e.Message}");
        }

        return result;
    }
}