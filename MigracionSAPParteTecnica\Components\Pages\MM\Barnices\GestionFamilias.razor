﻿@page "/GestionFamilias"
@attribute [Authorize(Roles = "materiales, admin, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var familias = (FamiliasDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(familias)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="DatoIn2" Caption="Nombre IN2" />
        <DxGridDataColumn FieldName="DatoSap" Caption="Código en SAP" />
        <DxGridDataColumn FieldName="DescripcionSap" Caption="Descripcion en SAP" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.Familias"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/Familias"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditFamiliaPopUp @ref="editFamilia" OnSave="GuardarCambios" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<FamiliasDTO> Data = new();
    string GridSearchText = "";
    private EditFamiliaPopUp? editFamilia;
    private FamiliasDTO? selectedFamilia { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (FamiliasDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllFamilias();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<FamiliasDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedFamilia = dataItem as FamiliasDTO ?? new FamiliasDTO();
        editFamilia?.AbrirPopUp(selectedFamilia);
    }

    async Task GuardarCambios(FamiliasDTO updatedFamilia)
    {
        _isLoading = true;
        selectedFamilia = updatedFamilia;

        var result = await MigracionSAPParteTecnicaService.TratarFamilia(selectedFamilia);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (FamiliasDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteFamilia(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Familia eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}