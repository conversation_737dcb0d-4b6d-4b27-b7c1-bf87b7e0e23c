﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
public class ImportarColoresCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarColoresCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarColoresCommandHandler : IRequestHandler<ImportarColoresCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarColoresCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarColoresCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new ColoresDTO
                    {
                        CodigoColor = worksheet.Cell(filaActual, 2).GetString().Trim(),
                        Color = worksheet.Cell(filaActual, 3).GetString().Trim(),
                        Borrado = false
                    };

                    if (string.IsNullOrWhiteSpace(dto.CodigoColor) || string.IsNullOrWhiteSpace(dto.Color))
                    {
                        errores.Add($"Fila {filaActual}: El Id y el Color no pueden estar vacíos.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.Colores
                        .FirstOrDefaultAsync(c => c.Id == dto.Id, cancellationToken);

                    var idAuditoria = dto.Id.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Tintas.Colores>(dto);
                        await _context.Colores.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "Colores",
                            IdRegistro = entidad.Id,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var original = existente.Color;

                        TinyMapper.Map(dto, existente);

                        if (original != existente.Color)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "Colores",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Color actualizado: '{original}' → '{existente.Color}'"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception exFila)
                {
                    errores.Add($"Fila {filaActual}: Error → {exFila.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar colores: {ex.Message}";
        }

        return result;
    }
}
