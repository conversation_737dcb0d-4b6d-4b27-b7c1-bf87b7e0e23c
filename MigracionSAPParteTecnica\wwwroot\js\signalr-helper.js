﻿window.signalRHelper = {
    connection: null,
    dotNetRef: null,
    start: function (dotNetRef) {
        this.dotNetRef = dotNetRef;
        this.connection = new signalR.HubConnectionBuilder()
            .withUrl("/appeventshub")
            .build();

        this.connection.on("EventoAplicacion", function (data) {
            console.log("EVENTO RECIBIDO SignalR en JS:", data);
            if (data && data.tipo === "PlanoEstadoCambiado") {
                if (window.signalRHelper.dotNetRef) {
                    window.signalRHelper.dotNetRef.invokeMethodAsync("OnPlanoCambiado", JSON.stringify(data));
                }
            }
            if (data && data.tipo === "ImposicionModificada") {
                if (window.signalRHelper.dotNetRef) {
                    window.signalRHelper.dotNetRef.invokeMethodAsync("OnImposicionModificada", JSON.stringify(data));
                }
            }
            if (data && data.tipo === "TroquelModificado") {
                if (window.signalRHelper.dotNetRef) {
                    window.signalRHelper.dotNetRef.invokeMethodAsync("OnTroquelModificado", JSON.stringify(data));
                }
            }
        });

        this.connection.start()
            .then(() => console.log("Conectado a SignalR!"))
            .catch(err => console.error("Error conectando a SignalR:", err));
    },
    stop: function () {
        if (this.connection) {
            this.connection.stop();
        }
    }
};