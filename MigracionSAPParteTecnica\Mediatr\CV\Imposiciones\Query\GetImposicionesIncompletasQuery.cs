﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using MigracionSAPParteTecnica.Components.Pages;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
public class GetImposicionesIncompletasQuery : IRequest<ListResult<ImposicionesDTO>>
{
}

internal class GetImposicionesIncompletasQueryHandler : IRequestHandler<GetImposicionesIncompletasQuery, ListResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetImposicionesIncompletasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ImposicionesDTO>> Handle(GetImposicionesIncompletasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ImposicionesDTO>()
        {
            Data = new List<ImposicionesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listImposiciones = await _migracionSAPParteTecnicaContext.Imposiciones
                .AsNoTracking()
                .Where(t =>
                    t.Cuerpos == null ||
                    t.SentidoLectura == null ||
                    t.ArranquePinza == null||
                    t.ArranqueEscuadra == null ||
                    t.NumeroAlturas == null ||
                    t.NumeroDesarrollos == null ||
                    t.PosicionEscuadraExt == null ||
                    t.Pinza == null
                )
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ImposicionesDTO>>(listImposiciones).ToList();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetImposicionesIncompletasQuery - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}