﻿@inject AuthenticationStateProvider AuthStateProvider
@inject IEntityLockService EntityLockService

@implements IAsyncDisposable

@code {
    [Parameter] public string EntityType { get; set; }
    [Parameter] public int EntityId { get; set; }
    [Parameter] public EventCallback<LockResult> OnLockStateChanged { get; set; }

    private Timer _timer;
    private const int INTERVALO_RENOVACION_MS = 30000;
    private bool _lectura;
    private string _usuario;
    private DateTime _fecha;

    protected override async Task OnInitializedAsync()
    {
        await ComprobarYCrearLock();

        if (!_lectura)
        {
            _timer = new Timer(async _ =>
            {
                try
                {
                    await RenovarLock();
                }
                catch { /* Puedes loggear si quieres */ }
            }, null, INTERVALO_RENOVACION_MS, INTERVALO_RENOVACION_MS);
        }
    }

    private async Task ComprobarYCrearLock()
    {
        var user = (await AuthStateProvider.GetAuthenticationStateAsync()).User;
        string usuario = user.Identity?.Name ?? "usuario-desconocido";

        var locked = await EntityLockService.LockAsync(EntityType, EntityId, usuario);
        if (!locked)
        {
            var (isLocked, usuarioLock, fechaLock) = await EntityLockService.IsLockedAsync(EntityType, EntityId);
            _lectura = true;
            _usuario = usuarioLock;
            _fecha = fechaLock;
        }
        else
        {
            _lectura = false;
            _usuario = usuario;
            _fecha = DateTime.Now;
        }
        if (OnLockStateChanged.HasDelegate)
            await OnLockStateChanged.InvokeAsync(new LockResult { Lectura = _lectura, Usuario = _usuario, Fecha = _fecha });
    }

    private async Task RenovarLock()
    {
        var user = (await AuthStateProvider.GetAuthenticationStateAsync()).User;
        string usuario = user.Identity?.Name ?? "usuario-desconocido";
        await EntityLockService.RenovarLockAsync(EntityType, EntityId, usuario);
    }

    public async ValueTask DisposeAsync()
    {
        Console.WriteLine("DISPOSEASYNC!!!");
        _timer?.Dispose();
        if (!_lectura)
        {
            var user = (await AuthStateProvider.GetAuthenticationStateAsync()).User;
            string usuario = user.Identity?.Name ?? "usuario-desconocido";
            await EntityLockService.UnlockAsync(EntityType, EntityId, usuario);
        }
    }
}