﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Barnices;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Tintas;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command
{
    public class TratarCorrespondenciaSKUCommand : IRequest<SingleResult<CorrespondenciasSkusDTO>>
    {
        public CorrespondenciasSkusDTO CorrespondenciaSkusDTO { get; set; }

        public TratarCorrespondenciaSKUCommand(CorrespondenciasSkusDTO correspondenciaSkusDTO)
        {
            CorrespondenciaSkusDTO = correspondenciaSkusDTO;
        }
    }
}

internal class TratarCorrespondenciaSKUCommandHandler : IRequestHandler<TratarCorrespondenciaSKUCommand, SingleResult<CorrespondenciasSkusDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarCorrespondenciaSKUCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<CorrespondenciasSkusDTO>> Handle(TratarCorrespondenciaSKUCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<CorrespondenciasSkusDTO>
        {
            Data = new CorrespondenciasSkusDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var dto = request.CorrespondenciaSkusDTO;

        try
        {
            var existing = await _migracionSAPParteTecnicaContext.CorrespondenciasSkus
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<CorrespondenciasSkus>(dto);
                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.CorrespondenciasSkus.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = dto;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Tintas_CorrespondenciasSKUS",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de correspondencia SKU: Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.CorrespondenciasSkus.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<CorrespondenciasSkusDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Tintas_CorrespondenciasSKUS",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de correspondencia SKU: Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarCorrespondenciaSKUCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}