﻿using MigracionSAPParteTecnica.DTO.QM;

namespace MigracionSAPParteTecnica.Validators.QM;

public static class TipoCaracteristicaValidator
{
    private static readonly HashSet<string> ValoresPermitidos = new()
    {
        "0", "1", "2", "5", "8", "9", "A", "B", "C", "D", "E"
    };

    public static string? Validar(TiposCaracteristicasDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.CodigoTipo))
            return "Debe ingresar un código.";

        if (!ValoresPermitidos.Contains(dto.CodigoTipo.ToUpperInvariant()))
            return $"El código debe ser uno de: {string.Join(", ", ValoresPermitidos)}. Mira la ayuda para más información";

        if (string.IsNullOrWhiteSpace(dto.Tipo))
            return "Debe ingresar un tipo.";

        if (dto.Tipo.Length > 8)
            return "El tipo no puede tener más de 8 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.Descripcion))
            return "Debe ingresar la descripción.";

        if (dto.Descripcion.Length > 40)
            return "La descripción no puede tener más de 40 caracteres.";

        return null;
    }
}