﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Threading;
using System.Threading.Tasks;

namespace MigracionSAPParteTecnica.Mediatr.CV.Motivos.Command;
public class ActualizarMotivosCommand : IRequest<Result>
{
}

internal class ActualizarMotivosCommandHandler : IRequestHandler<ActualizarMotivosCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ModularBlockingService _blockingService;

    public ActualizarMotivosCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService,
        IHttpContextAccessor httpContextAccessor, ModularBlockingService blockingService)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _blockingService = blockingService;
    }

    private bool SonIguales(string? a, string? b)
    {
        return string.Equals(a?.Trim(), b?.Trim(), StringComparison.OrdinalIgnoreCase);
    }

    public async Task<Result> Handle(ActualizarMotivosCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var modulo = "CV_Motivos";
        int insertados = 0, actualizados = 0;

        _blockingService.Block(modulo, usuario, "Actualización de motivos en curso");
        try
        {
            var maestros = await _context.Maestro
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var troqueles = await _context.Troqueles
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var motivos = await _context.Motivos
                .ToListAsync(cancellationToken);

            var motivosDic = motivos.ToDictionary(m => m.Id);

            foreach (var maestro in maestros)
            {
                var troquel = troqueles.FirstOrDefault(t =>
                    t.Cliente_IN2 == maestro.Cliente_IN2 &&
                    t.Cliente_SAP == maestro.Cliente_SAP &&
                    t.Familia == maestro.Producto &&
                    t.DiametroReal == maestro.DiametroReal &&
                    t.AlturaElemento == maestro.AlturaElemento &&
                    t.Desarrollo == maestro.Desarrollo &&
                    t.ReservasIzquierda == maestro.ResIzq &&
                    t.ReservasDerecha == maestro.ResDcha &&
                    t.ReservasInferiores == maestro.ResInf &&
                    t.ReservasSuperiores == maestro.ResSup &&
                    t.Embuticion == maestro.Embuticion &&
                    t.IdFormatoIn2 == maestro.IdFormato &&
                    t.PlanoEjemplo == maestro.Plano
                );

                motivosDic.TryGetValue(maestro.Id, out var motivo);

                bool nuevo = false, cambiado = false;
                if (motivo == null)
                {
                    motivo = new Entities.MigracionSAPParteTecnica.CV.Motivos
                    {
                        Id = maestro.Id,
                        Activo = true // Solo si es nuevo
                    };
                    _context.Motivos.Add(motivo);
                    motivosDic.Add(maestro.Id, motivo);
                    insertados++;
                    nuevo = true;
                }

                // Comparar campos nullables y strings correctamente
                if ((motivo.IdTroquel ?? 0) != (troquel?.Id ?? 0)) { motivo.IdTroquel = troquel?.Id; cambiado = true; }
                if ((motivo.IdCliente ?? 0) != (maestro.Cliente_SAP ?? 0)) { motivo.IdCliente = maestro.Cliente_SAP; cambiado = true; }
                if (!SonIguales(motivo.RefMotivoCliente, maestro.RefMotivoCliente)) { motivo.RefMotivoCliente = maestro.RefMotivoCliente; cambiado = true; }
                if (!SonIguales(motivo.Marca, maestro.Marca)) { motivo.Marca = maestro.Marca; cambiado = true; }
                if (!SonIguales(motivo.Descrip, maestro.Descrip)) { motivo.Descrip = maestro.Descrip; cambiado = true; }
                if (!SonIguales(motivo.TipoProducto, maestro.TipoProducto)) { motivo.TipoProducto = maestro.TipoProducto; cambiado = true; }
                if (motivo.Embuticion.GetValueOrDefault() != maestro.Embuticion)
                {
                    motivo.Embuticion = maestro.Embuticion;
                    cambiado = true;
                }
                if (!SonIguales(motivo.CodigoArchivo, maestro.Fotolito)) { motivo.CodigoArchivo = maestro.Fotolito; cambiado = true; }
                if (!SonIguales(motivo.MuestraFisica, maestro.PruebaFisica)) { motivo.MuestraFisica = maestro.PruebaFisica; cambiado = true; }
                if (!SonIguales(motivo.Gtin, maestro.Gtin)) { motivo.Gtin = maestro.Gtin; cambiado = true; }
                if (!SonIguales(motivo.PosFlejado, maestro.TipoFlejado)) { motivo.PosFlejado = maestro.TipoFlejado; cambiado = true; }
                // Campos nulos
                if (motivo.IdTratamiento != null) { motivo.IdTratamiento = null; cambiado = true; }
                if (motivo.ReferenciaColor != null) { motivo.ReferenciaColor = null; cambiado = true; }
                if (motivo.Gama != null) { motivo.Gama = null; cambiado = true; }
                var idMotivoPrecedente = maestro.Motivo ?? (maestro.PedidoEjemplo?.ToString() ?? null);
                if (!SonIguales(motivo.IdMotivoPrecedente, idMotivoPrecedente))
                {
                    motivo.IdMotivoPrecedente = idMotivoPrecedente;
                    cambiado = true;
                }

                if (!nuevo && cambiado)
                    actualizados++;
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Una sola entrada de auditoría para todo el proceso
            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "MASS UPDATE",
                Tabla = "CV_Motivos",
                IdRegistro = 0,
                DatosAntes = null,
                DatosDespues = null,
                Comentarios = $"Actualización masiva de motivos. Insertados: {insertados}, Actualizados: {actualizados}"
            }, cancellationToken);

            result.Success = true;
            result.Message = $"Motivos procesados correctamente. Insertados: {insertados}, Actualizados: {actualizados}";
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = "Error durante la actualización de motivos: " + ex.Message;
        }
        finally
        {
            _blockingService.Unblock(modulo);
        }

        return result;
    }
}