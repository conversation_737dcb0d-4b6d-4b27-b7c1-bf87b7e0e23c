﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Query;
public class GetAllEnvasesQuery : IRequest<ListResult<EnvasesDTO>>
{
}

internal class GetAllEnvasesQueryHandler : IRequestHandler<GetAllEnvasesQuery, ListResult<EnvasesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllEnvasesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<EnvasesDTO>> Handle(GetAllEnvasesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<EnvasesDTO>
        {
            Data = new List<EnvasesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listEnvases= await _migracionSAPParteTecnicaContext.Envases
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<EnvasesDTO>>(listEnvases).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllEnvasesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}