﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.MM.Barnices;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
public class ImportarNaturalezaCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarNaturalezaCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarNaturalezaCommandHandler : IRequestHandler<ImportarNaturalezaCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarNaturalezaCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarNaturalezaCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new NaturalezaDTO
                    {
                        DatoSap = worksheet.Cell(filaActual, 2).GetString().Trim(),
                        DatoIn2 = worksheet.Cell(filaActual, 3).GetString().Trim(),
                        DescripcionSap = worksheet.Cell(filaActual, 4).GetString().Trim(),
                        Borrado = false
                    };

                    var error = NaturalezaValidator.Validar(dto);
                    if (error != null)
                    {
                        errores.Add($"Fila {filaActual}: {error}");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.Naturaleza
                        .FirstOrDefaultAsync(n => n.DatoSap == dto.DatoSap, cancellationToken);

                    var clave = dto.DatoSap;
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.Naturaleza>(dto);
                        await _context.Naturaleza.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "MM_Barnices_Naturaleza",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var descripcionAnterior = existente.DescripcionSap;
                        var datoIn2Anterior = existente.DatoIn2;

                        TinyMapper.Map(dto, existente);

                        if (descripcionAnterior != existente.DescripcionSap || datoIn2Anterior != existente.DatoIn2)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            var cambios = new List<string>();
                            if (descripcionAnterior != existente.DescripcionSap)
                                cambios.Add($"Descripción: '{descripcionAnterior}' → '{existente.DescripcionSap}'");
                            if (datoIn2Anterior != existente.DatoIn2)
                                cambios.Add($"DatoIn2: '{datoIn2Anterior}' → '{existente.DatoIn2}'");

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_Naturaleza",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = string.Join(" | ", cambios)
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;

            result.Success = errores.Count == 0 || insertados > 0 || actualizados > 0;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar naturaleza: {ex.Message}";
        }

        return result;
    }
}