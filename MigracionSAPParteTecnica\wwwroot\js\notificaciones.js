﻿window.hacerPanelArrastrable = function (panelId, headerClass) {
    const panel = document.getElementById(panelId);
    const header = panel.querySelector("." + headerClass);
    let offsetX = 0, offsetY = 0, isDragging = false;

    header.style.cursor = "move";
    panel.style.position = "fixed";

    header.addEventListener("mousedown", function (e) {
        isDragging = true;
        offsetX = e.clientX - panel.offsetLeft;
        offsetY = e.clientY - panel.offsetTop;
        e.preventDefault();
    });

    document.addEventListener("mousemove", function (e) {
        if (!isDragging) return;

        let newLeft = Math.max(0, Math.min(window.innerWidth - panel.offsetWidth, e.clientX - offsetX));
        let newTop = Math.max(0, Math.min(window.innerHeight - panel.offsetHeight, e.clientY - offsetY));

        panel.style.left = newLeft + "px";
        panel.style.top = newTop + "px";
    });

    document.addEventListener("mouseup", function () {
        isDragging = false;
    });
};