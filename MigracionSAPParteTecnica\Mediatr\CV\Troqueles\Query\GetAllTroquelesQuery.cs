﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;
public class GetAllTroquelesQuery : IRequest<ListResult<TroquelesDTO>>
{
}

internal class GetAllTroquelesQueryHandler : IRequestHandler<GetAllTroquelesQuery, ListResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllTroquelesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<TroquelesDTO>> Handle(GetAllTroquelesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TroquelesDTO>
        {
            Data = new List<TroquelesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listTroqueles = await _migracionSAPParteTecnicaContext.Troqueles
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<TroquelesDTO>>(listTroqueles).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllTroquelesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}