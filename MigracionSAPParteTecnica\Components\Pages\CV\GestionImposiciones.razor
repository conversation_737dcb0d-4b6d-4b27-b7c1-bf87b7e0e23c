﻿@page "/GestionImposiciones"
@attribute [Authorize(Roles = "preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject ModularBlockingService BlockingService
@inject IJSRuntime JS

@implements IAsyncDisposable

<CustomLoadingPanel @bind-Visible="_isLoading" Mensaje="@MensajeBloqueo()" />
@if (UltimaActualizacionImposiciones != null)
{
    <div class="alert alert-secondary" style="margin-bottom:10px;">
        Última actualización: <b>@UltimaActualizacionImposiciones.Fecha.ToString("dd/MM/yyyy HH:mm:ss")</b>
        por <b>@UltimaActualizacionImposiciones.Usuario</b>
        @if (!string.IsNullOrWhiteSpace(UltimaActualizacionImposiciones.Comentarios))
        {
            <span> - @UltimaActualizacionImposiciones.Comentarios</span>
        }
    </div>
}
<DxGrid @ref="Grid" Data="Data" PageSize="21" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
        CustomizeElement="OnCustomizeGridElement" SelectionMode="GridSelectionMode.Single"
        SelectedDataItemChanged="@(row => OnImposicionSeleccionada((ImposicionesDTO)row))" SelectedDataItem="@selectedImposicion">
    <Columns>
        <DxGridSelectionColumn Width="40px" FixedPosition="GridColumnFixedPosition.Left" />
        <DxGridCommandColumn Width="60px" FixedPosition="GridColumnFixedPosition.Left">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirImposicionDetalle(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var imposicion = context.DataItem as ImposicionesDTO;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirImposicionDetalle(imposicion)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id Imposicion" FixedPosition="GridColumnFixedPosition.Left" Width="140px" />
        <DxGridDataColumn FieldName="ClienteNombreConcatenado" Caption="Cliente" Width="180px" />
        <DxGridDataColumn FieldName="Plano" Caption="Plano" Width="140px" />
        <DxGridDataColumn FieldName="AnchoHoja" Caption="Ancho hoja" Width="120px" />
        <DxGridDataColumn FieldName="LargoHoja" Caption="Largo hoja" Width="120px" />
        <DxGridDataColumn FieldName="IdTroquel" Caption="Id troquel asociado" Width="180px" />
        <DxGridDataColumn FieldName="Cuerpos" Caption="Nº Cuerpos" Width="120px" />
        <DxGridDataColumn FieldName="NumeroAlturas" Caption="Nº Alturas" Width="110px" />
        <DxGridDataColumn FieldName="NumeroDesarrollos" Caption="Nº Desarrollos" Width="140px" />
        <DxGridDataColumn FieldName="SentidoLectura" Caption="Sentido lectura" Width="150px" />
        <DxGridDataColumn FieldName="HojaScrollTexto" Caption="Hoja Scroll" Width="130px" />
        <DxGridDataColumn FieldName="LargoScroll" Caption="Largo Scroll" Width="130px" />
        <DxGridDataColumn FieldName="Pinza" Caption="Pinza" Width="120px" />
        <DxGridDataColumn FieldName="ArranquePinza" Caption="Arranque Pinza" Width="150px" />
        <DxGridDataColumn FieldName="ArranqueEscuadra" Caption="Arranque Escuadra" Width="150px" />
        <DxGridDataColumn FieldName="PosicionEscuadraExt" Caption="Posición Escuadra Ext" Width="200px" />
        <DxGridDataColumn FieldName="PosicionEscuadraInvTexto" Caption="Posición Escuadra Inv" Width="200px" />
        <DxGridDataColumn FieldName="Tipo" Caption="Tipo" Width="140px" />
        <DxGridDataColumn FieldName="Nota" Caption="Nota" Width="140px" />
        <DxGridDataColumn FieldName="PedidoEjemplo" Caption="Pedido Ejemplo" Width="160px" DisplayFormat="0" />
        <DxGridDataColumn FieldName="Activo" Visible="false" />
    </Columns>
    <ToolbarTemplate>
        <DxToolbar>
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Secondary" Text="Duplicar imposición" Alignment="ToolbarItemAlignment.Left"
                           IconCssClass="bi bi-files" Click="OnDuplicarImposicion" CssClass="ms-3 rounded" />
            <DxToolbarItem Text="Desactivar en masa" IconCssClass="bi bi-slash-circle" Click="MostrarPopupDesactivacion"
                           CssClass="ms-3 rounded" RenderStyle="ButtonRenderStyle.Warning" Alignment="ToolbarItemAlignment.Left" />
            <DxToolbarItem Text="Activar en masa" IconCssClass="bi bi-check-circle" CssClass="ms-3 rounded" RenderStyle="ButtonRenderStyle.Success"
                           Alignment="ToolbarItemAlignment.Left" Click="MostrarPopupActivacion" />
            <DxToolbarItem Text="Editar en masa" IconCssClass="bi bi-pencil-square"
                           CssClass="ms-3 rounded" RenderStyle="ButtonRenderStyle.Info"
                           Alignment="ToolbarItemAlignment.Left" Click="MostrarPopupEdicionMasiva" />
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Secondary" Text="Ver incompletas" Alignment="ToolbarItemAlignment.Right"
                           IconCssClass="bi bi-exclamation-triangle-fill" Click="OnVerIncompletas" CssClass="ms-3 rounded" />
            <DxToolbarItem Text="Ver inactivas" IconCssClass="bi bi-eye-slash" CssClass="ms-3 rounded" Alignment="ToolbarItemAlignment.Right"
                           Click="OnVerInactivas" RenderStyle="ButtonRenderStyle.Secondary" />
            <DxToolbarItem Text="Ver todas" IconCssClass="bi bi-eye" CssClass="ms-3 rounded" Alignment="ToolbarItemAlignment.Right"
                           Click="CargarGrid" RenderStyle="ButtonRenderStyle.Secondary" />
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar imposiciones" CssClass="ms-3 rounded"
                           IconCssClass="bi bi-arrow-clockwise" Click="ActualizarImposiciones" Alignment="ToolbarItemAlignment.Right" />
        </DxToolbar>
    </ToolbarTemplate>
</DxGrid>
<DesactivarImposicionesPopup @ref="PopupDesactivacion" OnConfirmar="@OnConfirmarDesactivacion" ClientesList="ClientesList" />
<PopupEdicionEnMasa @ref="PopupEdicionMasiva"
                    ClientesList="ClientesList"
                    CamposEditables="CamposImposicion"
                    Datos="Data.Cast<object>().ToList()"
                    OnGuardarCambios="GuardarCambiosMasivos" />
@code{
    bool _isLoading { get; set; }
    DxGrid? Grid;
    ObservableCollection<ImposicionesDTO> Data = new();
    private ImposicionesDTO? selectedImposicion { get; set; }
    string GridSearchText = "";
    List<DropDownWrapper> ClientesList = new();
    private DesactivarImposicionesPopup? PopupDesactivacion;
    private EventCallback<(int, string)> OnConfirmarDesactivacion =>
    EventCallback.Factory.Create<(int, string)>(this, async args => await TratarImposiciones(args, PopupDesactivacion?.EsActivar ?? false));
    PopupEdicionEnMasa? PopupEdicionMasiva;
    List<CampoEditable> CamposImposicion = CampoEditableHelper.GetCamposImposicion();
    ObservableCollection<ImposicionesDTO> DatosImposicion = new();
    Dictionary<(int, string), bool> PlanosActivosDic = new();
    bool _bloqueado;
    string? _usuarioBloqueador;
    string? _motivoBloqueo;
    private DotNetObjectReference<GestionImposiciones>? dotNetRef;
    AuditoriaDTO? UltimaActualizacionImposiciones { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        InicializarBloqueoImposiciones();
        await CargarUltimaActualizacionImposiciones();
        await CargarGrid();
        await CargarClientesList();

        _isLoading = false;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            dotNetRef = DotNetObjectReference.Create(this);
            await JS.InvokeVoidAsync("signalRHelper.start", dotNetRef);
        }
    }

    private void OnImposicionSeleccionada(ImposicionesDTO imposicion)
    {
        selectedImposicion = imposicion;
    }

    [JSInvokable("OnPlanoCambiado")]
    public async Task OnPlanoCambiado(string data)
    {
        try
        {
            var evento = System.Text.Json.JsonSerializer.Deserialize<PlanoEstadoCambiadoEvent>(
                data,
                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
            if (evento != null)
            {
                var estadoTexto = evento.NuevoEstado ? "activo" : "inactivo";
                var mensaje = $"El usuario {evento.Usuario} ha cambiado el estado del plano {evento.NombrePlano} del cliente {evento.ClienteId} a {estadoTexto}.";
                ToastService.MostrarInfo(mensaje);
            }
            await CargarGrid();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine("EXCEPCION EN OnPlanoCambiado: " + ex.ToString());
        }
    }

    [JSInvokable("OnImposicionModificada")]
    public async Task OnImposicionModificada(string data)
    {
        try
        {
            var evento = System.Text.Json.JsonSerializer.Deserialize<ImposicionModificadaEvent>(
                data,
                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
            if (evento != null)
            {
                var mensaje = $"El usuario {evento.Usuario} ha modificado la imposición {evento.Id} (plano: {evento.Plano}, cliente: {evento.Cliente})";
                ToastService.MostrarInfo(mensaje);
            }
            await CargarGrid();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine("EXCEPCION EN OnImposicionModificada: " + ex.ToString());
        }
    }

    [JSInvokable("OnTroquelModificado")]
    public async Task OnTroquelModificado(string data)
    {
        try
        {
            var evento = System.Text.Json.JsonSerializer.Deserialize<ImposicionModificadaEvent>(
                data,
                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
            if (evento != null)
            {
                var mensaje = $"El usuario {evento.Usuario} ha modificado el troquel {evento.Id} (cliente: {evento.Cliente})";
                ToastService.MostrarInfo(mensaje);
            }
            await CargarGrid();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine("EXCEPCION EN OnTroquelModificado: " + ex.ToString());
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (dotNetRef != null)
    {
        await JS.InvokeVoidAsync("signalRHelper.stop");
        dotNetRef.Dispose();
    }
    _isLoading = false;
    _bloqueado = false;
    BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;        
    }

    private void InicializarBloqueoImposiciones()
    {
        BlockingService.OnModuleStateChanged += EstadoModuloActualizado;
        var estado = BlockingService.GetState("ConfiguradorVariantes");
        _bloqueado = estado.IsBlocked;
        _usuarioBloqueador = estado.Usuario;
        _motivoBloqueo = estado.Motivo;
    }

    private string MensajeBloqueo()
    {
        if (_bloqueado)
        {
            return $"El usuario {_usuarioBloqueador} está actualizando imposiciones: {_motivoBloqueo}";
        }
        return "Cargando...";
    }

    private void EstadoModuloActualizado(string modulo, ModularBlockingService.BlockingState estado)
    {
        if (modulo == "ConfiguradorVariantes")
        {
            _bloqueado = estado.IsBlocked;
            _usuarioBloqueador = estado.Usuario;
            _motivoBloqueo = estado.Motivo;
            _isLoading = _bloqueado;
            InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        _isLoading = false;
        _bloqueado = false;
        BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllImposiciones();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            var clientesResult = await MigracionSAPParteTecnicaService.GetAllSAPClientes();
            if (clientesResult.Errors.Any())
            {
                ToastService.MostrarError(clientesResult.Errors.First());
            }

            var clientesDic = clientesResult.Data.GroupBy(c => c.Codigo_SAP).ToDictionary(g => g.Key, g => g.First().Nombre);

            var planosResult = await MigracionSAPParteTecnicaService.GetAllPlanos();
            if (planosResult.Errors.Any())
            {
                ToastService.MostrarError(planosResult.Errors.First());
            }
            PlanosActivosDic = planosResult.Data
                .GroupBy(p => (p.Cliente, p.NombrePlano))
                .Select(g => g.First())
                .ToDictionary(
                    p => (p.Cliente, p.NombrePlano),
                    p => p.Activo
                );

            Data = new ObservableCollection<ImposicionesDTO>(
                result.Data.Select(p =>
                {
                    var nombreCliente = (p.Cliente_SAP.HasValue && clientesDic.TryGetValue(p.Cliente_SAP.Value, out var nombre))
                        ? nombre
                        : "Desconocido";
                    p.ClienteNombreConcatenado = $"{p.Cliente_SAP} - {nombreCliente}";
                    return p;
                })
            );

            StateHasChanged();
        }
    }

    public async Task CargarClientesList()
    {
        var resultado = await MigracionSAPParteTecnicaService.GetAllSAPClientes();

        if (resultado != null && resultado.Data != null)
        {
            ClientesList = resultado.Data
                .OrderBy(c => c.Codigo_SAP)
                .Select(c => new DropDownWrapper
                {
                    ID = c.Codigo_SAP,
                    Nombre = $"{c.Codigo_SAP}, {c.Nombre}"
                })
                .ToList();
        }
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (ImposicionesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    void AbrirImposicionDetalle(object? imposicionObj)
    {
        if (imposicionObj is ImposicionesDTO imposicion)
        {
            NavigationManager.NavigateTo($"/imposicion-detalle/{imposicion.Id}");
        }
        else
        {
            NavigationManager.NavigateTo("/imposicion-detalle");
        }
    }

    public async Task ActualizarImposiciones()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await MigracionSAPParteTecnicaService.ActualizarImposiciones();

        if (result.Success)
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
            await CargarUltimaActualizacionImposiciones();
        }
        else
        {
            ToastService.MostrarError($"Error al actualizar imposiciones: {result.Message}");
        }

        _isLoading = false;
    }

    private async Task OnVerIncompletas()
    {
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.GetImposicionesIncompletas();
        if (result.Errors.Any())
        {
            ToastService.MostrarError("Error al obtener imposiciones incompletas.");
        }
        else
        {
            Data = new ObservableCollection<ImposicionesDTO>(result.Data);
            ToastService.MostrarInfo($"Se han cargado {Data.Count} imposiciones incompletas.");
        }

        _isLoading = false;
    }

    private async Task OnVerInactivas()
    {
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.GetImposicionesInactivas();
        if (result.Errors.Any())
        {
            ToastService.MostrarError("Error al obtener imposiciones inactivas.");
        }
        else
        {
            Data = new ObservableCollection<ImposicionesDTO>(result.Data);
            ToastService.MostrarInfo($"Se han cargado {Data.Count} imposiciones inactivas.");
        }

        _isLoading = false;
    }

    void OnCustomizeGridElement(GridCustomizeElementEventArgs e)
    {
        if (e.ElementType != GridElementType.DataRow)
            return;

        var id = (int)e.Grid.GetRowValue(e.VisibleIndex, "Id");
        var imposicion = Data.FirstOrDefault(x => x.Id == id);

        if (imposicion?.IdTroquelNavigation != null && imposicion.IdTroquelNavigation.OrigenModificado == true)
        {
            e.CssClass = "fila-origen-modificado";
            return;
        }

        var activoObj = e.Grid.GetRowValue(e.VisibleIndex, "Activo");
        var planoObj = e.Grid.GetRowValue(e.VisibleIndex, "Plano");
        var clienteObj = e.Grid.GetRowValue(e.VisibleIndex, "Cliente_SAP");

        int? clienteSap = null;
        if (clienteObj is int cInt)
            clienteSap = cInt;
        else if (clienteObj is long cLong)
            clienteSap = (int)cLong;
        else if (clienteObj is short cShort)
            clienteSap = cShort;
        else if (clienteObj is string cStr && int.TryParse(cStr, out var cParsed))
            clienteSap = cParsed;

        string? nombrePlano = planoObj as string;
        bool? imposicionActiva = activoObj as bool?;

        if (imposicionActiva.HasValue && !imposicionActiva.Value)
        {
            e.CssClass = "fila-inactiva";
            return;
        }

        if (imposicionActiva == true && clienteSap.HasValue && !string.IsNullOrEmpty(nombrePlano))
        {
            if (PlanosActivosDic.TryGetValue((clienteSap.Value, nombrePlano), out var planoActivo) && planoActivo == false)
            {
                e.CssClass = "fila-plano-inactivo";
                return;
            }
        }
    }

    void MostrarPopupDesactivacion()
    {
        if (PopupDesactivacion is not null)
        {
            PopupDesactivacion.EsActivar = false;
            PopupDesactivacion.AbrirPopUp(ClientesList);
        }
    }

    void MostrarPopupActivacion()
    {
        if (PopupDesactivacion is not null)
        {
            PopupDesactivacion.EsActivar = true;
            PopupDesactivacion.AbrirPopUp(ClientesList);
        }
    }

    async Task TratarImposiciones((int Cliente, string Plano) filtro, bool activar)
    {
        var coincidencias = Data
            .Where(i => i.Cliente_SAP == filtro.Cliente && i.Plano == filtro.Plano && i.Activo != activar)
            .ToList();

        if (!coincidencias.Any())
        {
            ToastService.MostrarInfo($"No se encontraron imposiciones para {(activar ? "activar" : "desactivar")}.");
            return;
        }

        _isLoading = true;

        foreach (var imposicion in coincidencias)
        {
            imposicion.Activo = activar;
            await MigracionSAPParteTecnicaService.TratarImposicion(imposicion);
        }

        ToastService.MostrarOk($"Se han {(activar ? "activado" : "desactivado")} {coincidencias.Count} imposiciones.");
        await CargarGrid();
        _isLoading = false;
    }

    void MostrarPopupEdicionMasiva()
    {
        if (PopupEdicionMasiva is not null)
        {
            PopupEdicionMasiva.AbrirPopup();
        }
    }

    async Task GuardarCambiosMasivos(List<object> modificados)
    {
        foreach (var item in modificados)
        {
            var imposicion = item as ImposicionesDTO;
            if (imposicion != null)
            {
                await MigracionSAPParteTecnicaService.TratarImposicion(imposicion);
            }
        }

        await CargarGrid();
    }

    private async Task OnDuplicarImposicion()
    {
        if (selectedImposicion == null)
        {
            ToastService.MostrarError("Selecciona primero una imposición de la tabla.");
            return;
        }

        _isLoading = true;
        try
        {
            var result = await MigracionSAPParteTecnicaService.DuplicarImposicion(selectedImposicion.Id);

            if (result.Errors.Any())
            {
                ToastService.MostrarError(string.Join("; ", result.Errors));
            }
            else
            {
                ToastService.MostrarOk($"Imposición duplicada con éxito (nuevo Id: {result.Data.Id}).");
                await CargarGrid();
            }
        }
        catch (Exception ex)
        {
            ToastService.MostrarError("Error al duplicar imposición: " + ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task CargarUltimaActualizacionImposiciones()
    {
        var auditoriaResult = await MigracionSAPParteTecnicaService.GetUltimaActualizacionByTabla("CV_Imposiciones");
        if (auditoriaResult.Errors.Any())
        {
            UltimaActualizacionImposiciones = null;
        }
        else
        {
            UltimaActualizacionImposiciones = auditoriaResult.Data;
        }
    }
}