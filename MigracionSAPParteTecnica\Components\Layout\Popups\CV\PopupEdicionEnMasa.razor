﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Masivamente"
         Width="900px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        <DxFormLayout>
            <DxFormLayoutItem Caption="Cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-person" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="ClienteSeleccionado"
                                    Data="@ClientesList"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Filtros adicionales:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                <Template Context="ItemContext">
                    <div style="display: flex; flex-direction: column; gap: 10px; width: 100%;">
                        @foreach (var filtro in Filtros)
                        {
                            <div style="display: flex; align-items: center; gap: 10px; width: 100%;">
                                <DxComboBox @bind-Value="filtro.Propiedad"
                                            Data="@CamposEditables"
                                            TextFieldName="Nombre"
                                            ValueFieldName="Nombre"
                                            CssClass="popup-demo-textbox"
                                            Placeholder="Campo"
                                            style="flex: 2;" />

                                <DxComboBox @bind-Value="filtro.Operador"
                                            Data="@OperadoresDisponibles"
                                            CssClass="popup-demo-textbox"
                                            Placeholder="Operador"
                                            style="flex: 1;" />

                                <DxTextBox @bind-Text="filtro.Valor"
                                           CssClass="popup-demo-textbox"
                                           Placeholder="Valor"
                                           style="flex: 2;" />

                                @if (filtro.Operador == "Entre")
                                {
                                    <DxTextBox @bind-Text="filtro.ValorSecundario"
                                               CssClass="popup-demo-textbox"
                                               Placeholder="y"
                                               style="flex: 2;" />
                                }

                                <DxButton IconCssClass="bi bi-x"
                                          Click="@(() => EliminarFiltro(filtro))"
                                          RenderStyle="ButtonRenderStyle.Danger" />
                            </div>
                        }

                        <DxButton Text="Agregar filtro" IconCssClass="bi bi-plus-circle"
                                  Click="@AgregarFiltro" RenderStyle="ButtonRenderStyle.Secondary" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Campo a editar:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-pencil-square" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="CampoSeleccionado"
                                    Data="@CamposEditables"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox w-100"
                                    Placeholder="Selecciona el campo a editar" />
                        @if (CampoSeleccionado != null)
                        {
                            <p class="demo-text mt-1 mb-0" style="margin-left: 10px;">
                                Formato esperado: <b>@CampoSeleccionado.Descripcion</b>
                            </p>
                        }
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Valor actual:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-box-arrow-in-left" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="ValorActual" CssClass="popup-demo-textbox full-width w-100" />
                    </div>
                </Template>
            </DxFormLayoutItem>

            <DxFormLayoutItem Caption="Nuevo valor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-box-arrow-right" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="NuevoValor" CssClass="popup-demo-textbox full-width w-100" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem ColSpanMd="12">
                <Template Context="ItemContext">
                    <p class="demo-text cw-480 mt-3">
                        Coincidencias encontradas: <b>@Coincidencias</b>
                    </p>
                    <DxButton Text="Ver" IconCssClass="bi bi-eye"
                              Enabled="@((Coincidencias > 0))"
                              Click="@VerCoincidencias"
                              RenderStyle="ButtonRenderStyle.Secondary" />
                </Template>
            </DxFormLayoutItem>
        </DxFormLayout>
    </BodyContentTemplate>  
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            <DxButton Text="Aplicar edición"
                      Click="AplicarEdicion"
                      RenderStyle="ButtonRenderStyle.Primary"
                      style="background-color: #28a745; color: white; border-color: #28a745;"
                      Enabled="EsFormularioValido()" />
            <DxButton Text="Limpiar" IconCssClass="bi bi-arrow-counterclockwise"
                      Click="LimpiarFormulario" RenderStyle="ButtonRenderStyle.Info" />
            <DxButton Text="Cancelar"
                      Click="CerrarPopup"
                      RenderStyle="ButtonRenderStyle.Secondary" />
        </div>
    </FooterContentTemplate>
</DxPopup>
<DxPopup @bind-Visible="IsPreviewPopupVisible"
         HeaderText="Coincidencias encontradas"
         Width="1000px" Height="550px"
         ShowCloseButton="true" CloseOnEscape="true">
            <DxGrid Data="@CoincidenciasPreview" ShowFilterRow="true"PageSize="12">
                <Columns>
                    <DxGridDataColumn FieldName="Id" Caption="Id" />
                    <DxGridDataColumn FieldName="Cliente" Caption="Cliente" />
                    <DxGridDataColumn FieldName="Plano" Caption="Plano" />
                    <DxGridDataColumn FieldName="AnchoHoja" Caption="Ancho hoja" />
                    <DxGridDataColumn FieldName="LargoHoja" Caption="Largo hoja" />
                    <DxGridDataColumn FieldName="IdTroquel" Caption="Id troquel asociado" />
                    <DxGridDataColumn FieldName="Cuerpos" Caption="Nº Cuerpos" />
                    <DxGridDataColumn FieldName="SentidoLectura" Caption="Sentido lectura" />
                </Columns>
            </DxGrid>
</DxPopup>

@code{
    [Parameter] public List<DropDownWrapper> ClientesList { get; set; } = new();
    [Parameter] public List<CampoEditable> CamposEditables { get; set; } = new();
    [Parameter] public List<object> Datos { get; set; } = new();
    [Parameter] public EventCallback<List<object>> OnGuardarCambios { get; set; }

    private bool IsPopupVisible;
    private bool IsPreviewPopupVisible;

    private int? ClienteSeleccionado;

    private CampoEditable? CampoSeleccionado
    {
        get => _campoSeleccionado;
        set
        {
            if (_campoSeleccionado?.Nombre != value?.Nombre)
            {
                _campoSeleccionado = value;
                ValorActual = null;
                NuevoValor = null;
            }
        }
    }
    private CampoEditable? _campoSeleccionado;

    private string? ValorActual;
    private string? NuevoValor;

    private List<FiltroEdicion> Filtros = new();
    private List<string> OperadoresDisponibles = new() { "Igual", "Mayor", "Menor", "Entre", "Contiene" };

    private List<ImposicionesDTO> CoincidenciasPreview = new();

    private int Coincidencias
    {
        get
        {
            try
            {
                return CampoSeleccionado == null
                    ? 0
                    : Datos.Count(d => CumpleFiltrosYValor(d));
            }
            catch
            {
                return 0;
            }
        }
    }

    void AgregarFiltro() => Filtros.Add(new FiltroEdicion());
    void EliminarFiltro(FiltroEdicion filtro) => Filtros.Remove(filtro);

    private bool EsFormularioValido() =>
    CampoSeleccionado != null &&
    !string.IsNullOrWhiteSpace(ValorActual) &&
    !string.IsNullOrWhiteSpace(NuevoValor) &&
    Filtros.All(f => !string.IsNullOrWhiteSpace(f.Propiedad) && !string.IsNullOrWhiteSpace(f.Valor));


    public void AbrirPopup()
    {
        ClienteSeleccionado = null;
        CampoSeleccionado = null;
        ValorActual = null;
        NuevoValor = null;
        IsPopupVisible = true;
    }

    private void CerrarPopup() {
        IsPopupVisible = false;
    }

    void LimpiarFormulario()
    {
        ClienteSeleccionado = null;
        CampoSeleccionado = null;
        ValorActual = null;
        NuevoValor = null;
        Filtros.Clear();
    }

    private string ObtenerFormatoCampo(string? nombreCampo)
    {
        if (string.IsNullOrWhiteSpace(nombreCampo) || Datos.Count == 0)
            return string.Empty;

        var tipo = Datos[0].GetType();
        var prop = tipo.GetProperty(nombreCampo);
        if (prop == null)
            return string.Empty;

        Type tipoDato = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;

        return tipoDato switch
        {
            Type t when t == typeof(string) => "Texto libre.",
            Type t when t == typeof(int) => "Número entero. Ejemplo: 42",
            Type t when t == typeof(double) => "Número decimal. Ejemplo: 3.14",
            Type t when t == typeof(bool) => "Booleano. Ejemplo: true / false",
            Type t when t == typeof(DateTime) => "Fecha. Ejemplo: 2025-06-12",
            _ => $"Tipo desconocido: {tipoDato.Name}"
        };
    }

    private bool CumpleFiltrosYValor(object item)
    {
        var tipo = item.GetType();

        foreach (var filtro in Filtros)
        {
            if (string.IsNullOrWhiteSpace(filtro.Propiedad) ||
                string.IsNullOrWhiteSpace(filtro.Valor) ||
                string.IsNullOrWhiteSpace(filtro.Operador))
                return false;

            var prop = tipo.GetProperty(filtro.Propiedad);
            if (prop == null) return false;

            var tipoProp = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
            var valorActual = prop.GetValue(item);

            try
            {
                object? valor1 = Convert.ChangeType(filtro.Valor, tipoProp);
                object? valor2 = !string.IsNullOrWhiteSpace(filtro.ValorSecundario)
                                ? Convert.ChangeType(filtro.ValorSecundario, tipoProp)
                                : null;

                switch (filtro.Operador)
                {
                    case "Igual":
                        if (!Equals(valorActual, valor1)) return false;
                        break;

                    case "Mayor":
                        if (valorActual is IComparable compMayor && compMayor.CompareTo(valor1) <= 0)
                            return false;
                        break;

                    case "Menor":
                        if (valorActual is IComparable compMenor && compMenor.CompareTo(valor1) >= 0)
                            return false;
                        break;

                    case "Entre":
                        if (valor1 == null || valor2 == null || !(valorActual is IComparable compEntre))
                            return false;
                        if (compEntre.CompareTo(valor1) < 0 || compEntre.CompareTo(valor2) > 0)
                            return false;
                        break;

                    case "Contiene":
                        if (valorActual is string actualStr && valor1 is string filtroStr)
                        {
                            if (!actualStr.Contains(filtroStr, StringComparison.OrdinalIgnoreCase))
                                return false;
                        }
                        else
                        {
                            return false;
                        }
                        break;

                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        if (CampoSeleccionado == null || string.IsNullOrWhiteSpace(CampoSeleccionado.Nombre))
            return false;

        var campoProp = tipo.GetProperty(CampoSeleccionado.Nombre);
        if (campoProp == null || string.IsNullOrWhiteSpace(ValorActual))
            return false;

        try
        {
            var tipoCampo = Nullable.GetUnderlyingType(campoProp.PropertyType) ?? campoProp.PropertyType;
            var valorActualObj = Convert.ChangeType(ValorActual, tipoCampo);
            var valorExistente = campoProp.GetValue(item);
            return Equals(valorExistente, valorActualObj);
        }
        catch
        {
            return false;
        }
    }

    private async Task AplicarEdicion()
    {
        if (Datos == null || Datos.Count == 0)
        {
            ToastService.MostrarInfo("No hay datos disponibles para editar.");
            return;
        }

        if (CampoSeleccionado == null ||
            string.IsNullOrWhiteSpace(ValorActual) ||
            string.IsNullOrWhiteSpace(NuevoValor))
        {
            ToastService.MostrarError("Completa todos los campos obligatorios.");
            return;
        }

        if (Filtros.Any(f => string.IsNullOrWhiteSpace(f.Propiedad) || string.IsNullOrWhiteSpace(f.Valor)))
        {
            ToastService.MostrarError("Todos los filtros deben tener campo y valor.");
            return;
        }

        var modificados = new List<object>();

        foreach (var item in Datos)
        {
            var tipo = item.GetType();
            bool cumpleFiltros = true;

            foreach (var filtro in Filtros)
            {
                var prop = tipo.GetProperty(filtro.Propiedad);
                if (prop == null)
                {
                    cumpleFiltros = false;
                    break;
                }

                try
                {
                    var filtroValorConvertido = Convert.ChangeType(filtro.Valor, prop.PropertyType);
                    var actualValor = prop.GetValue(item);
                    if (!Equals(actualValor, filtroValorConvertido))
                    {
                        cumpleFiltros = false;
                        break;
                    }
                }
                catch
                {
                    ToastService.MostrarError($"El valor '{filtro.Valor}' no es válido para el campo '{filtro.Propiedad}'.");
                    return;
                }
            }

            if (!cumpleFiltros)
                continue;

            var campoProp = tipo.GetProperty(CampoSeleccionado?.Nombre ?? string.Empty);
            if (campoProp == null)
                continue;

            object? valorActualObj = null;
            object? nuevoValorObj = null;

            try
            {
                valorActualObj = Convert.ChangeType(ValorActual, campoProp.PropertyType);
                nuevoValorObj = Convert.ChangeType(NuevoValor, campoProp.PropertyType);
            }
            catch
            {
                ToastService.MostrarError($"Los valores introducidos no coinciden con el tipo del campo '{CampoSeleccionado}'.");
                return;
            }

            var valorExistente = campoProp.GetValue(item);
            if (!Equals(valorExistente, valorActualObj))
                continue;

            campoProp.SetValue(item, nuevoValorObj);
            modificados.Add(item);
        }

        if (modificados.Any())
        {
            await OnGuardarCambios.InvokeAsync(modificados);
            ToastService.MostrarOk($"{modificados.Count} registros modificados.");
        }
        else
        {
            ToastService.MostrarInfo("No se encontraron coincidencias.");
        }

        IsPopupVisible = false;
    }

    void VerCoincidencias()
    {
        CoincidenciasPreview = Datos
            .OfType<ImposicionesDTO>()
            .Where(d => CumpleFiltrosYValor(d))
            .ToList();

        IsPreviewPopupVisible = true;
    }
}