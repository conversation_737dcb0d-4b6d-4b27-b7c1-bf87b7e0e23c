﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
public class GetAllCodigoEnvasesQuery : IRequest<ListResult<CodigoEnvasesDTO>>
{
}

internal class GetAllCodigoEnvasesQueryHandler : IRequestHandler<GetAllCodigoEnvasesQuery, ListResult<CodigoEnvasesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllCodigoEnvasesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task <ListResult<CodigoEnvasesDTO>> Handle(GetAllCodigoEnvasesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<CodigoEnvasesDTO>
        {
            Data = new List<CodigoEnvasesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listBarnicesCodigoEnvases = await _migracionSAPParteTecnicaContext.CodigoEnvases
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<CodigoEnvasesDTO>>(listBarnicesCodigoEnvases).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllCodigoEnvasesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}