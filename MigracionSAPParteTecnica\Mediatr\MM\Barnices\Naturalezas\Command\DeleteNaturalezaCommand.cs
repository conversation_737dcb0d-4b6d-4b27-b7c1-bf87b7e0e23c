﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;

public class DeleteNaturalezaCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteNaturalezaCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteNaturalezaCommandHandler : IRequestHandler<DeleteNaturalezaCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteNaturalezaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteNaturalezaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var naturaleza = await _migracionSAPParteTecnicaContext.Naturaleza
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (naturaleza == null)
            {
                result.Errors.Add("No se ha encontrado la naturaleza a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(naturaleza);

            naturaleza.Borrado = true;

            _migracionSAPParteTecnicaContext.Naturaleza.Update(naturaleza);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            int idAuditoria = request.Id.GetHashCode();

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_Naturaleza",
                IdRegistro = naturaleza.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de la naturaleza: Id='{naturaleza.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteNaturalezaCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}