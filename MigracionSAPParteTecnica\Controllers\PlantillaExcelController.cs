﻿using Microsoft.AspNetCore.Mvc;
using MigracionSAPParteTecnica.Components.Common;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Controllers;

[Route("api/[controller]")]
[ApiController]
public class PlantillaExcelController : ControllerBase
{
    private readonly IExcelPlantillaService _excelService;
    private readonly IWebHostEnvironment _env;

    public PlantillaExcelController(IExcelPlantillaService excelService, IWebHostEnvironment env)
    {
        _excelService = excelService;
        _env = env;
    }

    [HttpGet("Descargar")]
    public async Task<IActionResult> Descargar(
        [FromQuery] string titulo,
        [FromQuery] string hoja,
        [FromQuery] string visible,
        [FromQuery] string columnas)
    {
        var columnasLista = columnas.Split(',').ToList();

        var config = new ExcelConfig
        {
            NombreArchivo = titulo,
            TituloHoja = hoja,
            TituloVisible = visible,
            Columnas = columnasLista
        };

        var rutaPlantilla = Path.Combine(_env.WebRootPath, "Plantillas", "Plantilla excel.xlsx");

        if (!System.IO.File.Exists(rutaPlantilla))
        {
            return NotFound("La plantilla Excel no fue encontrada.");
        }

        var excelBytes = await _excelService.GenerarExcelDesdePlantillaAsync(rutaPlantilla, config);

        return File(
            excelBytes,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            $"{titulo}.xlsx"
        );
    }
}
