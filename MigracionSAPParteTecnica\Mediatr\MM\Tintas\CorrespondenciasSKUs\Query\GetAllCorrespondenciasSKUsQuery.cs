﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Query;

public class GetAllCorrespondenciasSKUsQuery : IRequest<ListResult<CorrespondenciasSkusDTO>>
{
}

internal class GetAllCorrespondenciasSKUsQueryHandler : IRequestHandler<GetAllCorrespondenciasSKUsQuery, ListResult<CorrespondenciasSkusDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllCorrespondenciasSKUsQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<CorrespondenciasSkusDTO>> Handle(GetAllCorrespondenciasSKUsQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<CorrespondenciasSkusDTO>
        {
            Data = new List<CorrespondenciasSkusDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listCorrespondencias = await _migracionSAPParteTecnicaContext.CorrespondenciasSkus
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<CorrespondenciasSkusDTO>>(listCorrespondencias).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllCorrespondenciasSKUsQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}