﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;

namespace MigracionSAPParteTecnica.Helpers;
public class ImposicionHelper
{
    //public static string GenerarClaveTecnicaImposicion(int cliente, string plano, decimal ancho, decimal largo, decimal scroll, int idTroquel)
    //{
    //    return "_" +
    //            cliente + "_" +
    //            plano.Trim().ToUpper() + "_" +
    //            ancho.ToString("0.00") + "_" +
    //            largo.ToString("0.00") + "_" +
    //            scroll.ToString("0.00") + "_" +
    //            idTroquel;
    //}

    //public static string GenerarNuevoIdImposicion(List<Imposiciones> imposicionesLocales)
    //{
    //    if (!imposicionesLocales.Any())
    //        return "I000001";

    //    var maxId = imposicionesLocales
    //        .Select(i => i.Id)
    //        .Where(id => !string.IsNullOrEmpty(id) && id.Length == 7 && id.StartsWith("I"))
    //        .Max();

    //    var numeroStr = maxId.Substring(1);

    //    if (!int.TryParse(numeroStr, out int numero))
    //        throw new Exception("IdImposicion no tiene el formato esperado.");

    //    numero++;

    //    if (numero > 999999)
    //        throw new Exception("Se alcanzó el número máximo de imposiciones.");

    //    return "I" + numero.ToString("D6");
    //}
}