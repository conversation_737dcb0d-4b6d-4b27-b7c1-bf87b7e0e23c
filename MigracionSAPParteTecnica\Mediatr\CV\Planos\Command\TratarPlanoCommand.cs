﻿using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;
using MigracionSAPParteTecnica.Hubs;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Mediatr.Planos.Command;
public class TratarPlanoCommand : IRequest<SingleResult<PlanoDTO>>
{
    public PlanoDTO PlanoDTO { get; set; }

    public TratarPlanoCommand(PlanoDTO planoDTO)
    {
        PlanoDTO = planoDTO;
    }
}

internal class TratarPlanoCommandHandler : IRequestHandler<TratarPlanoCommand, SingleResult<PlanoDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHubContext<AppEventsHub> _hubContext;

    public TratarPlanoCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor, IHubContext<AppEventsHub> hubContext)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _hubContext = hubContext;
    }

    public async Task<SingleResult<PlanoDTO>> Handle(TratarPlanoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<PlanoDTO>
        {
            Data = new PlanoDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        try
        {
            Plano? existingPlano = null;
            if (request.PlanoDTO.Id > 0)
            {
                existingPlano = await _context.Plano.FirstOrDefaultAsync(p => p.Id == request.PlanoDTO.Id, cancellationToken);
            }

            if (existingPlano == null)
            {
                // CREAR
                var nuevoPlano = new Plano
                {
                    Cliente = request.PlanoDTO.Cliente,
                    NombrePlano = request.PlanoDTO.NombrePlano,
                    Activo = request.PlanoDTO.Activo,
                    Borrado = request.PlanoDTO.Borrado
                };

                _context.Plano.Add(nuevoPlano);
                await _context.SaveChangesAsync(cancellationToken);

                result.Data = new PlanoDTO
                {
                    Id = nuevoPlano.Id,
                    Cliente = nuevoPlano.Cliente,
                    NombrePlano = nuevoPlano.NombrePlano,
                    Activo = nuevoPlano.Activo,
                    Borrado = nuevoPlano.Borrado
                };

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "CV_Plano",
                    IdRegistro = nuevoPlano.Id,
                    DatosAntes = null,
                    DatosDespues = System.Text.Json.JsonSerializer.Serialize(result.Data),
                    Comentarios = $"Alta de plano '{nuevoPlano.NombrePlano}' para cliente {nuevoPlano.Cliente}"
                }, cancellationToken);

                await _hubContext.Clients.All.SendAsync(
                    "EventoAplicacion",
                    new
                    {
                        Tipo = "PlanoEstadoCambiado",
                        ClienteId = result.Data.Cliente,
                        NombrePlano = result.Data.NombrePlano,
                        NuevoEstado = result.Data.Activo,
                        Usuario = usuario
                    }
                );
            }
            else
            {
                var datosAntes = System.Text.Json.JsonSerializer.Serialize(new
                {
                    existingPlano.Cliente,
                    existingPlano.NombrePlano,
                    existingPlano.Activo,
                    existingPlano.Borrado
                });

                existingPlano.Cliente = request.PlanoDTO.Cliente;
                existingPlano.NombrePlano = request.PlanoDTO.NombrePlano;
                existingPlano.Activo = request.PlanoDTO.Activo;
                existingPlano.Borrado = request.PlanoDTO.Borrado;

                await _context.SaveChangesAsync(cancellationToken);

                result.Data = new PlanoDTO
                {
                    Id = existingPlano.Id,
                    Cliente = existingPlano.Cliente,
                    NombrePlano = existingPlano.NombrePlano,
                    Activo = existingPlano.Activo,
                    Borrado = existingPlano.Borrado
                };

                var datosDespues = System.Text.Json.JsonSerializer.Serialize(result.Data);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "CV_Plano",
                    IdRegistro = existingPlano.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de plano: Cliente={existingPlano.Cliente}, NombrePlano='{existingPlano.NombrePlano}'"
                }, cancellationToken);

                Console.WriteLine($"Enviando evento: usuario={usuario}, clienteId={result.Data.Cliente}, nombrePlano={result.Data.NombrePlano}, nuevoEstado={result.Data.Activo}");

                await _hubContext.Clients.All.SendAsync(
                    "EventoAplicacion",
                    new
                    {
                        Tipo = "PlanoEstadoCambiado",
                        ClienteId = result.Data.Cliente,
                        NombrePlano = result.Data.NombrePlano,
                        NuevoEstado = result.Data.Activo,
                        Usuario = usuario
                    }
                );
            }
        }
        catch (DbUpdateException dbEx) when (dbEx.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
        {
            result.Errors.Add("Ya existe un plano con ese Cliente y Nombre.");
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: TratarPlano - {(e.InnerException?.Message ?? e.Message)}");
        }

        return result;
    }
}