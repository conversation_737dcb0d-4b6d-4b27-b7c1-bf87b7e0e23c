﻿namespace MigracionSAPParteTecnica.Helpers;
public static class ExcelDownloadHelper
{
    public static string GenerarUrlDescarga(string baseUrl, string nombreArchivo, string tituloHoja, string tituloVisible, List<string> columnas)
    {
        if (string.IsNullOrWhiteSpace(baseUrl) ||
            string.IsNullOrWhiteSpace(nombreArchivo) ||
            string.IsNullOrWhiteSpace(tituloHoja) ||
            string.IsNullOrWhiteSpace(tituloVisible))
        {
            throw new ArgumentException("Faltan parámetros requeridos para la descarga.");
        }

        var queryParams = new Dictionary<string, string>
        {
            ["titulo"] = nombreArchivo,
            ["hoja"] = tituloHoja,
            ["visible"] = tituloVisible,
            ["columnas"] = string.Join(",", columnas ?? new List<string>())
        };

        var query = string.Join("&", queryParams.Select(kv => $"{kv.Key}={Uri.EscapeDataString(kv.Value)}"));
        return $"{baseUrl}?{query}";
    }
}