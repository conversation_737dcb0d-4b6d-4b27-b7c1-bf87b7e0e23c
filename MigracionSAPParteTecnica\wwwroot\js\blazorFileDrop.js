﻿window.blazorFileDrop = {
    init: function (dropZoneId, inputFileId, dotNetHelper) {
        const dropZone = document.getElementById(dropZoneId);
        const inputFile = document.getElementById(inputFileId);

        if (!dropZone || !inputFile) return;

        dropZone.addEventListener("click", () => inputFile.click());

        dropZone.addEventListener("dragover", function (e) {
            e.preventDefault();
            dropZone.classList.add("drop-zone-hover");
        });

        dropZone.addEventListener("dragleave", function () {
            dropZone.classList.remove("drop-zone-hover");
        });

        dropZone.addEventListener("drop", async function (e) {
            e.preventDefault();
            dropZone.classList.remove("drop-zone-hover");

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(files[0]);
                inputFile.files = dataTransfer.files;

                const event = new Event('change', { bubbles: true });
                inputFile.dispatchEvent(event);

                await dotNetHelper.invokeMethodAsync('SetFileFromDrop', files[0].name);
            }
        });
    }
};