﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
public class DeleteFamiliaCommand : IRequest<SingleResult<bool>>
{
    public int Id{ get; set; }

    public DeleteFamiliaCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteFamiliaCommandHandler : IRequestHandler<DeleteFamiliaCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteFamiliaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteFamiliaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var familia = await _migracionSAPParteTecnicaContext.Familias
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (familia == null)
            {
                result.Errors.Add("No se ha encontrado la familia a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(familia);

            familia.Borrado = true;

            _migracionSAPParteTecnicaContext.Familias.Update(familia);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;


            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_Familias",
                IdRegistro = familia.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de la familia: Id='{familia.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteFamiliaCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}