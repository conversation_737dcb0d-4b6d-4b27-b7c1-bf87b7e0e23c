﻿using MigracionSAPParteTecnica.DTO.QM;

namespace MigracionSAPParteTecnica.Validators.QM;
public static class CaracteristicasInspeccionValidator
{
    public static string? Validar(CaracteristicasInspeccionDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Nombre))
            return "Debe ingresar un nombre para la característica.";

        if (dto.Nombre.Length > 8)
            return "El nombre no puede superar los 8 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.CampoBusqueda))
            return "Debe ingresar un campo de búsqueda.";

        if (dto.CampoBusqueda.Length > 40)
            return "El campo de búsqueda no puede superar los 40 caracteres.";

        if (dto.MetodoIns <= 0)
            return "Debe seleccionar un método de inspección.";

        if (string.IsNullOrWhiteSpace(dto.TextoBreve))
            return "Debe ingresar un texto breve.";

        if (dto.TextoBreve.Length > 40)
            return "El texto breve no puede superar los 40 caracteres.";

        if (dto.Cuantitativa)
        {
            if (dto.NumeroDecimales == null)
                return "Debe ingresar el número de decimales si la característica es cuantitativa.";

            if (string.IsNullOrWhiteSpace(dto.UnidadMedida))
                return "Debe ingresar la unidad de medida si la característica es cuantitativa.";
        }

        if (dto.ToleranciaInf && dto.LimiteToleranciaInf == null)
            return "Debe ingresar el límite inferior de tolerancia si la opción está marcada.";

        if (dto.ToleranciaSup && dto.LimiteToleranciaSup == null)
            return "Debe ingresar el límite superior de tolerancia si la opción está marcada.";

        return null; // Todo correcto
    }
}
