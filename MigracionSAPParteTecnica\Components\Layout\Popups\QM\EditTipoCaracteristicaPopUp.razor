﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editTipoCaracteristica?.Id > 0)
{
    <EntityLockManager EntityType="TiposCaracteristicas"
                       EntityId="editTipoCaracteristica.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Tipo Característica"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        <EditForm Model="@editTipoCaracteristica" OnValidSubmit="GuardarCambios" Context="model">
            @if (_lectura)
            {
                <div class="alert alert-warning mb-2">
                    Este tipo de característica está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
                </div>
            }
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editTipoCaracteristica" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código tipo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-hash" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editTipoCaracteristica.CodigoTipo" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.TipoCodTipo" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tipo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-tags" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editTipoCaracteristica.Tipo" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.TipoCaracteristica" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Descripción:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editTipoCaracteristica.Descripcion" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.TextoBreveCaracteristica" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code {
    private bool IsPopupVisible;
    [Parameter] public EventCallback<TiposCaracteristicasDTO> OnSave { get; set; }
    private TiposCaracteristicasDTO editTipoCaracteristica = new();
    bool IsCodTipoOpen = false;

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(TiposCaracteristicasDTO tipoCaracteristicas)
    {
        editTipoCaracteristica = tipoCaracteristicas;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = TipoCaracteristicaValidator.Validar(editTipoCaracteristica);

        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editTipoCaracteristica);
        IsPopupVisible = false;
        ToastService.MostrarOk("Tipo de característica guardada correctamente.");
    }
}