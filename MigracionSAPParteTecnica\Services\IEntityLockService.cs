﻿using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.General;

namespace MigracionSAPParteTecnica.Services;

public interface IEntityLockService
{
    Task<bool> LockAsync(string entityType, int entityId, string usuario);
    Task<bool> UnlockAsync(string entityType, int entityId, string usuario, bool forzar = false);
    Task<(bool IsLocked, string Usuario, DateTime FechaBloqueo)> IsLockedAsync(string entityType, int entityId);
    Task<bool> RenovarLockAsync(string entityType, int entityId, string usuario);
}

public class EntityLockService : IEntityLockService
{
    private readonly IDbContextFactory<MigracionSAPParteTecnicaContext> _contextFactory;

    public EntityLockService(IDbContextFactory<MigracionSAPParteTecnicaContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public async Task<bool> LockAsync(string entityType, int entityId, string usuario)
    {
        using var context = _contextFactory.CreateDbContext();

        var existing = await context.EntityLocks
            .FirstOrDefaultAsync(x => x.EntityType == entityType && x.EntityId == entityId);

        if (existing != null)
        {
            if (existing.FechaBloqueo < DateTime.UtcNow.AddMinutes(-6))
            {
                context.EntityLocks.Remove(existing);
                await context.SaveChangesAsync();
            }
            else
            {
                return false;
            }
        }

        var lockEntity = new EntityLocks
        {
            EntityType = entityType,
            EntityId = entityId,
            Usuario = usuario,
            FechaBloqueo = DateTime.UtcNow
        };

        context.EntityLocks.Add(lockEntity);
        await context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UnlockAsync(string entityType, int entityId, string usuario, bool forzar = false)
    {
        using var context = _contextFactory.CreateDbContext();

        var existing = await context.EntityLocks
            .FirstOrDefaultAsync(x => x.EntityType == entityType && x.EntityId == entityId);

        if (existing == null)
            return false;

        if (!forzar && existing.Usuario != usuario)
            return false;

        context.EntityLocks.Remove(existing);
        await context.SaveChangesAsync();
        return true;
    }

    public async Task<(bool IsLocked, string Usuario, DateTime FechaBloqueo)> IsLockedAsync(string entityType, int entityId)
    {
        using var context = _contextFactory.CreateDbContext();

        var existing = await context.EntityLocks
            .FirstOrDefaultAsync(x => x.EntityType == entityType && x.EntityId == entityId);

        if (existing == null || existing.FechaBloqueo < DateTime.UtcNow.AddMinutes(-6))
            return (false, null, DateTime.MinValue);

        return (true, existing.Usuario, existing.FechaBloqueo);
    }

    public async Task<bool> RenovarLockAsync(string entityType, int entityId, string usuario)
    {
        using var context = _contextFactory.CreateDbContext();

        var existing = await context.EntityLocks
            .FirstOrDefaultAsync(x => x.EntityType == entityType && x.EntityId == entityId && x.Usuario == usuario);

        if (existing == null)
            return false;

        existing.FechaBloqueo = DateTime.UtcNow;
        await context.SaveChangesAsync();
        return true;
    }
}