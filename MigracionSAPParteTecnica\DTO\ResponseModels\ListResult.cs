﻿using System.Text.Json.Serialization;

namespace MigracionSAPParteTecnica.DTO.ResponseModels;

public class ListResult<T> : PrimitiveResult
{
    [JsonPropertyName("data")]
    public List<T> Data { get; set; } = [];

    [JsonPropertyName("count")]
    public int Count => Data?.Count ?? 0;

    public static ListResult<T> Ok(IEnumerable<T> data) => new()
    {
        Success = true,
        Data = data.ToList()
    };

    public static ListResult<T> Error(string mensaje) => new()
    {
        Success = false,
        Errors = [mensaje]
    };

    public static ListResult<T> Error(IEnumerable<string> errores) => new()
    {
        Success = false,
        Errors = errores.ToList()
    };
}