﻿using MigracionSAPParteTecnica.DTO.CV;

namespace MigracionSAPParteTecnica.Validators.CV;
public static class PlanoValidator
{
    public static string? Validar(PlanoDTO dto)
    {
        if (dto.Cliente <= 0)
            return "Debe seleccionar un cliente válido.";

        if (string.IsNullOrWhiteSpace(dto.NombrePlano))
            return "Debe ingresar un nombre de plano.";

        if (dto.NombrePlano.Length > 255)
            return "El nombre del plano no puede exceder los 255 caracteres.";

        return null;
    }
}