﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="@HeaderText"
         Width="500px" sho
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        <DxFormLayout>
            <DxFormLayoutItem Caption="Cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-person" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="ClienteSeleccionado"
                                    Data="@ClientesList"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Nombre del plano:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-tag" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="PlanoSeleccionado" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayout>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            <DxButton Click="Confirmar"
                      Text="@BotonTexto"
                      RenderStyle="ButtonRenderStyle.Primary"
                      style="@BotonEstilo" />
            <DxButton Click="CerrarPopup"
                      Text="Cancelar"
                      RenderStyle="ButtonRenderStyle.Danger"
                      style="background-color: #dc3545; color: white; border-color: #dc3545;" />
        </div>
    </FooterContentTemplate>
</DxPopup>
@code {
    [Parameter] public bool EsActivar { get; set; } = false;
    private bool IsPopupVisible;
    [Parameter] public List<DropDownWrapper> ClientesList { get; set; }
    [Parameter] public EventCallback<(int, string)> OnConfirmar { get; set; }
    private int? ClienteSeleccionado;
    private string? PlanoSeleccionado;
    private string BotonTexto => EsActivar ? "Activar" : "Desactivar";
    private string BotonEstilo => $"background-color: {(EsActivar ? "#007bff" : "#28a745")}; color: white; border-color: {(EsActivar ? "#007bff" : "#28a745")};";
    private string HeaderText => EsActivar ? "Activar imposiciones" : "Desactivar imposiciones";



    public void AbrirPopUp(List<DropDownWrapper> clientes)
    {
        ClientesList = clientes;
        ClienteSeleccionado = null;
        PlanoSeleccionado = null;
        IsPopupVisible = true;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task Confirmar()
    {
        if (!ClienteSeleccionado.HasValue || string.IsNullOrWhiteSpace(PlanoSeleccionado))
        {
            ToastService.MostrarError("Debe seleccionar cliente y plano.");
            return;
        }

        await OnConfirmar.InvokeAsync((ClienteSeleccionado.Value, PlanoSeleccionado));
        IsPopupVisible = false;
    }
}