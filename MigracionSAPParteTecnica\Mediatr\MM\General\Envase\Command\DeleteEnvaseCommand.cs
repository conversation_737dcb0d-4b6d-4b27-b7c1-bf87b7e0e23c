﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
public class DeleteEnvaseCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteEnvaseCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteEnvaseCommandHandler : IRequestHandler<DeleteEnvaseCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteEnvaseCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteEnvaseCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var envase = await _migracionSAPParteTecnicaContext.Envases
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (envase == null)
            {
                result.Errors.Add("No se ha encontrado el envase a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(envase);

            envase.Borrado = true;

            _migracionSAPParteTecnicaContext.Envases.Update(envase);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            var datosDespues = JsonSerializer.Serialize(envase);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Envases",
                IdRegistro = envase.Id,
                DatosAntes = datosAntes,
                DatosDespues = datosDespues,
                Comentarios = $"Eliminación lógica del envase: Código={request.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteEnvaseCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}