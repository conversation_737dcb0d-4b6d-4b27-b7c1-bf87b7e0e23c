﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.MM.Tintas;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Command;
public class ImportarRelacionCompuestasCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarRelacionCompuestasCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarRelacionCompuestasCommandHandler : IRequestHandler<ImportarRelacionCompuestasCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarRelacionCompuestasCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarRelacionCompuestasCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            var rows = worksheet.RowsUsed().Skip(1);

            foreach (var row in rows)
            {
                var dto = new RelacionCompuestasDTO
                {
                    Ingrediente = row.Cell(1).GetString().Trim(),
                    CodigoTinta = row.Cell(2).GetValue<int>(),
                    Borrado = false
                };

                var error = RelacionCompuestasValidator.Validar(dto);
                if (error != null)
                {
                    errores.Add($"Fila {row.RowNumber()}: {error}");
                    descartados++;
                    continue;
                }

                var existente = await _context.RelacionCompuestas
                    .FirstOrDefaultAsync(x => x.Id == dto.Id, cancellationToken);

                var idAuditoria = dto.Id;

                if (existente is null)
                {
                    var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Tintas.RelacionCompuestas>(dto);
                    await _context.RelacionCompuestas.AddAsync(entidad, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "RelacionCompuestas",
                        IdRegistro = entidad.Id,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(entidad),
                        Comentarios = "Importado desde Excel"
                    }, cancellationToken);

                    insertados++;
                }
                else
                {
                    var datosAntes = JsonSerializer.Serialize(new { existente.Ingrediente, existente.CodigoTinta, existente.Borrado });

                    TinyMapper.Map(dto, existente);
                    await _context.SaveChangesAsync(cancellationToken);

                    var datosDespues = JsonSerializer.Serialize(new { existente.Ingrediente, existente.CodigoTinta, existente.Borrado });

                    if (datosAntes != datosDespues)
                    {
                        await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "RelacionCompuestas",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = "Actualizado desde Excel"
                        }, cancellationToken);

                        actualizados++;
                    }
                }
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar relación compuestas: {ex.Message}";
        }

        return result;
    }
}