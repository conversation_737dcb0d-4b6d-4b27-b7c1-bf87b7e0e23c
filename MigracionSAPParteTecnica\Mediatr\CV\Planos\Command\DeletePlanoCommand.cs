﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.Planos.Command;
public class DeletePlanoCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeletePlanoCommand(int id)
    {
        Id = id;
    }
}

internal class DeletePlanoCommandHandler : IRequestHandler<DeletePlanoCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeletePlanoCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeletePlanoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        try
        {
            var plano = await _context.Plano
                .FirstOrDefaultAsync(p => p.Id == request.Id , cancellationToken);

            if (plano == null)
            {
                result.Errors.Add("No se ha encontrado el plano a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(plano);

            plano.Borrado = true;
            plano.Activo = false;

            _context.Plano.Update(plano);
            await _context.SaveChangesAsync(cancellationToken);

            var datosDespues = JsonSerializer.Serialize(plano);
            var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "CV_Plano",
                IdRegistro = request.Id,
                DatosAntes = datosAntes,
                DatosDespues = datosDespues,
                Comentarios = $"Eliminación lógica de plano: Id={request.Id}'"
            }, cancellationToken);

            result.Data = true;
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeletePlanoCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}