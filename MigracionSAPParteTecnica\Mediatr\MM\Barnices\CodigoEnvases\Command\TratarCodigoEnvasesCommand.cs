﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
public class TratarCodigoEnvasesCommand : IRequest<SingleResult<CodigoEnvasesDTO>>
{
    public CodigoEnvasesDTO CodigoEnvaseDTO { get; set; }

    public TratarCodigoEnvasesCommand(CodigoEnvasesDTO codigoEnvaseDTO)
    {
        CodigoEnvaseDTO = codigoEnvaseDTO;
    }
}

internal class TratarCodigoEnvasesCommandHandler : IRequestHandler<TratarCodigoEnvasesCommand, SingleResult<CodigoEnvasesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarCodigoEnvasesCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<CodigoEnvasesDTO>> Handle(TratarCodigoEnvasesCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<CodigoEnvasesDTO>
        {
            Data = new CodigoEnvasesDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.CodigoEnvaseDTO;

            var existing = await _migracionSAPParteTecnicaContext.CodigoEnvases
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.CodigoEnvases>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.CodigoEnvases.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<CodigoEnvasesDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Barnices_CodigoEnvases",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de código envase: Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.CodigoEnvases.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<CodigoEnvasesDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Barnices_CodigoEnvases",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de código envase: Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarCodigoEnvasesCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}