﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;

public class ImportarNodrizasCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarNodrizasCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarNodrizasCommandHandler : IRequestHandler<ImportarNodrizasCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarNodrizasCommandHandler(
        MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarNodrizasCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 3).IsEmpty())
            {
                try
                {
                    var dto = new NodrizasDTO
                    {
                        Fecha = worksheet.Cell(filaActual, 2).GetDateTime(),
                        Nodriza = worksheet.Cell(filaActual, 3).GetValue<int>(),
                        CodigoBarniz = worksheet.Cell(filaActual, 4).GetValue<int>(),
                        Lote = worksheet.Cell(filaActual, 5).GetString().Trim(),
                        Cantidad = worksheet.Cell(filaActual, 6).GetValue<decimal>(), 
                        Borrado = false
                    };

                    if (dto.Nodriza <= 0 || dto.CodigoBarniz <= 0 || string.IsNullOrWhiteSpace(dto.Lote) || dto.Cantidad <= 0)
                    {
                        errores.Add($"Fila {filaActual}: Datos obligatorios faltantes o inválidos.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.Nodrizas
                        .FirstOrDefaultAsync(n =>
                            n.Nodriza == dto.Nodriza &&
                            n.Fecha.Date == dto.Fecha.Date &&
                            n.CodigoBarniz == dto.CodigoBarniz &&
                            n.Lote == dto.Lote, cancellationToken);

                    var clave = $"{dto.Nodriza}_{dto.Fecha:yyyyMMdd}_{dto.CodigoBarniz}_{dto.Lote}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.Nodrizas>(dto);
                        await _context.Nodrizas.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "MM_Barnices_Nodrizas",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var cantidadAnterior = existente.Cantidad;
                        TinyMapper.Map(dto, existente);

                        if (cantidadAnterior != existente.Cantidad)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_Nodrizas",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Cantidad actualizada: {cantidadAnterior} → {existente.Cantidad}"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar nodrizas: {ex.Message}";
        }

        return result;
    }
}