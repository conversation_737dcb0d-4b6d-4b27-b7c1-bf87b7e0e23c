﻿@page "/GestionMetodosInspeccion"
@attribute [Authorize(Roles = "calidad, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var metodosInspeccion = (MetodosInspeccionDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(metodosInspeccion)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Nombre" Caption="Nombre" />
        <DxGridDataColumn FieldName="Busqueda" Caption="Tipo" />
        <DxGridDataColumn FieldName="Descripcion" Caption="Descripcion" />
        <DxGridDataColumn FieldName="TextoBreve" Caption="Texto Breve" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.MetodosInspeccion"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/MetodosInspeccion"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditMetodosInspeccionPopUp @ref="editMetodoInspeccion" OnSave="GuardarCambios"/>


@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<MetodosInspeccionDTO> Data = new();
    string GridSearchText = "";
    private EditMetodosInspeccionPopUp? editMetodoInspeccion;
    private MetodosInspeccionDTO? selectedMetodoInspeccion { get; set; }
    private PopupImportarExcel? popupImportarExcel;
    private bool IsPopupImportVisible = false;
    string UploadUrlSeleccionada = "api/Upload/MetodosInspeccion";

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (MetodosInspeccionDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllMetodosInspeccion();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<MetodosInspeccionDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedMetodoInspeccion = dataItem as MetodosInspeccionDTO ?? new MetodosInspeccionDTO();
        editMetodoInspeccion?.AbrirPopUp(selectedMetodoInspeccion);
    }

    async Task GuardarCambios(MetodosInspeccionDTO updatedMetodoInspeccion)
    {
        _isLoading = true;
        selectedMetodoInspeccion = updatedMetodoInspeccion;

        var result = await MigracionSAPParteTecnicaService.TratarMetodoInspeccion(selectedMetodoInspeccion);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (MetodosInspeccionDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteMetodoInspeccion(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Tipo de característica eliminada correctamente.");
        }

        _isLoading = false;
    }
    
    void AbrirPopUpCarga(object dataItem)
    {
        IsPopupImportVisible = true;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}