﻿using ClosedXML.Excel;
using MigracionSAPParteTecnica.Components.Common;

namespace MigracionSAPParteTecnica.Services;

public interface IExcelPlantillaService
{
    Task<byte[]> GenerarExcelDesdePlantillaAsync(
        string rutaPlantilla,
        ExcelConfig config,
        int filaInicio = 5);
}

public class ExcelPlantillaService : IExcelPlantillaService
{
    public async Task<byte[]> GenerarExcelDesdePlantillaAsync(
        string rutaPlantilla,
        ExcelConfig config,
        int filaInicio = 5)
    {
        using var stream = new MemoryStream();
        using var plantillaStream = File.OpenRead(rutaPlantilla);
        using var workbook = new XLWorkbook(plantillaStream);

        var worksheet = workbook.Worksheets.First();
        worksheet.Name = config.TituloHoja;

        // Estilo global de la hoja
        worksheet.Style.Font.FontName = "Aptos Narrow";
        worksheet.Style.Font.FontSize = 11;

        // Fondo blanco
        worksheet.Range("A1:Z100").Style.Fill.BackgroundColor = XLColor.White;

        // Título en B2
        var celdaTitulo = worksheet.Cell("B2");
        celdaTitulo.Value = config.TituloVisible;
        celdaTitulo.Style.Font.Bold = true;
        celdaTitulo.Style.Font.FontSize = 24;
        celdaTitulo.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

        // Ajustar ancho de columna B al título
        worksheet.Column(2).AdjustToContents();

        // Insertar encabezados en fila filaInicio (normalmente 5)
        for (int i = 0; i < config.Columnas.Count; i++)
        {
            int col = i + 2; // comienza en columna B (2)
            var celda = worksheet.Cell(filaInicio, col);
            celda.Value = config.Columnas[i];
            celda.Style.Font.Bold = true;
            celda.Style.Font.FontColor = XLColor.White;
            celda.Style.Fill.BackgroundColor = XLColor.FromHtml("#C00000");
            celda.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            celda.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
        }

        int totalColumnas = config.Columnas.Count;

        // Crear tabla (encabezado + fila vacía para formato)
        var rangoTabla = worksheet.Range(filaInicio, 2, filaInicio + 1, totalColumnas + 1);
        var tabla = rangoTabla.CreateTable();
        tabla.Theme = XLTableTheme.None;
        tabla.ShowAutoFilter = true;
        tabla.Style.Font.FontName = "Aptos Narrow";
        tabla.Style.Font.FontSize = 11;
        tabla.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
        tabla.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;

        // Añadir fila de contenido ficticio para mantener formato visible
        for (int i = 0; i < totalColumnas; i++)
        {
            var celda = worksheet.Cell(filaInicio + 1, i + 2);
            celda.Value = " ";
        }

        // Ajustar ancho de columnas según longitud del encabezado
        for (int i = 1; i < totalColumnas; i++) // empezamos desde la segunda columna (col C)
        {
            int colIndex = i + 2;
            worksheet.Column(colIndex).AdjustToContents(filaInicio, filaInicio); // solo el encabezado
            worksheet.Column(colIndex).Width += 2; // añadir un poco más
            worksheet.Column(colIndex).Style.Alignment.WrapText = false;
        }

        // Borde exterior fila de contenido
        var bordeExterior = worksheet.Range(filaInicio + 1, 2, filaInicio + 1, totalColumnas + 1);
        bordeExterior.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        bordeExterior.Style.Border.OutsideBorderColor = XLColor.Black;

        // Mover imagen si existe
        var imagen = worksheet.Pictures.FirstOrDefault();
        if (imagen != null)
        {
            int columnaDestino = totalColumnas + 2;
            imagen.MoveTo(worksheet.Cell(2, columnaDestino));
        }

        workbook.SaveAs(stream);
        return await Task.FromResult(stream.ToArray());
    }
}