﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Maestro.Query;
public class GetAllMaestroQuery : IRequest<ListResult<MaestroDTO>>
{
}

internal class GetAllMaestroQueryHandler : IRequestHandler<GetAllMaestroQuery, ListResult<MaestroDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllMaestroQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MaestroDTO>> Handle(GetAllMaestroQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MaestroDTO>
        {
            Data = new List<MaestroDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listMaestro = await _migracionSAPParteTecnicaContext.Maestro
                .AsNoTracking()
                .ToListAsync();

            result.Data = TinyMapper.Map<List<MaestroDTO>>(listMaestro).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllMaestro - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}