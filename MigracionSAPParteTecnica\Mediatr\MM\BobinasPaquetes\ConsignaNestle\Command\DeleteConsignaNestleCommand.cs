﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Command;
public class DeleteConsignaNestleCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteConsignaNestleCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteConsignaNestleCommandHandler : IRequestHandler<DeleteConsignaNestleCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteConsignaNestleCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteConsignaNestleCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var consignaNestle = await _migracionSAPParteTecnicaContext.ConsignaNestle
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (consignaNestle == null)
            {
                result.Errors.Add("No se ha encontrado el paquete a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(consignaNestle);

            consignaNestle.Borrado = true;

            _migracionSAPParteTecnicaContext.ConsignaNestle.Update(consignaNestle);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_BobinasPaquetes_ConsignaNestle",
                IdRegistro = consignaNestle.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de paquete en consigna: Id={consignaNestle.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteConsignaNestleCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}