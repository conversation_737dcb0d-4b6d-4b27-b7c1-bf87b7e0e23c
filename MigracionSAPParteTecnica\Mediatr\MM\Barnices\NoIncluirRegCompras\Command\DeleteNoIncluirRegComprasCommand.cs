﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;

public class DeleteNoIncluirRegComprasCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteNoIncluirRegComprasCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteNoIncluirRegComprasCommandHandler : IRequestHandler<DeleteNoIncluirRegComprasCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteNoIncluirRegComprasCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteNoIncluirRegComprasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var registroCompra = await _migracionSAPParteTecnicaContext.NoIncluirRegCompras
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (registroCompra == null)
            {
                result.Errors.Add("No se ha encontrado el registro de compras a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(registroCompra);

            registroCompra.Borrado = true;

            _migracionSAPParteTecnicaContext.NoIncluirRegCompras.Update(registroCompra);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_NoIncluirRegCompras",
                IdRegistro = registroCompra.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica del registro de compra: Id='{registroCompra.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteNoIncluirRegComprasCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}