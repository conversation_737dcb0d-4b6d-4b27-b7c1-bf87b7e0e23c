﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Mediatr.CV.TiposElemento.Command;
public class DeleteTipoElementoCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteTipoElementoCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteTipoElementoCommandHandler : IRequestHandler<DeleteTipoElementoCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;

    public DeleteTipoElementoCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService)
    {
        _context = context;
        _auditoriaService = auditoriaService;
    }

    public async Task<SingleResult<bool>> Handle(DeleteTipoElementoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        try
        {
            var tipoElemento = await _context.TiposElemento
                .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

            if (tipoElemento == null)
            {
                result.Errors.Add("No se ha encontrado el tipo de elemento a borrar.");
                return result;
            }

            var datosAntes = System.Text.Json.JsonSerializer.Serialize(new
            {
                tipoElemento.Borrado
            });

            tipoElemento.Borrado = true;
            _context.TiposElemento.Update(tipoElemento);
            await _context.SaveChangesAsync(cancellationToken);

            result.Data = true;

            var datosDespues = System.Text.Json.JsonSerializer.Serialize(new
            {
                tipoElemento.Borrado
            });

            var usuario = _context.GetService<IHttpContextAccessor>()?.HttpContext?.User?.Identity?.Name ?? "sistema";

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "CV_TiposElemento",
                IdRegistro = request.Id,
                DatosAntes = datosAntes,
                DatosDespues = datosDespues,
                Comentarios = $"Eliminación lógica de tipo de elemento: Id={request.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteTipoElementoCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}