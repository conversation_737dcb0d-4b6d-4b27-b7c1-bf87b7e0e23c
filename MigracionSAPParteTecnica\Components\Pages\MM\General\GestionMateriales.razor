﻿@page "/GestionMateriales"
@attribute [Authorize(Roles = "materiales, admin, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
        <Columns>
        <DxGridDataColumn FieldName="Codigo" Caption="Código material">
            <CellDisplayTemplate Context="context">
                @{
                    var codProveedor = ((MaterialesDTO)context.DataItem)?.Codigo;
                }
                <span>@(codProveedor?.ToString() ?? "")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
            <DxGridDataColumn FieldName="Descripcion" Caption="Descripcion" />
            <DxGridDataColumn FieldName="Tipo" Caption="Tipo" />
        </Columns>
        <ToolbarTemplate>
            <DxToolbar>
                <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar materiales" CssClass="ms-3 rounded"
                               IconCssClass="bi bi-arrow-clockwise" Click="ActualizarMateriales" Alignment="ToolbarItemAlignment.Right" />
            </DxToolbar>
        </ToolbarTemplate>
</DxGrid>

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<MaterialesDTO> Data = new();
    string GridSearchText = "";
    private MaterialesDTO? selectedCodigoEnvase { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (MaterialesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllMateriales();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<MaterialesDTO>(result.Data);
            StateHasChanged();
        }
    }

    public async Task ActualizarMateriales()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await ServinService.ActualizarMateriales();

        if (result.Success)
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
        }
        else
        {
            ToastService.MostrarError($"Error al actualizar imposiciones: {result.Message}");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}