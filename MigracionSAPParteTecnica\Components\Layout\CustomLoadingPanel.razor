﻿<DxLoadingPanel @bind-Visible="Visible"
                PositionTarget="@PositionTarget"
                ApplyBackgroundShading="true"
                Text="@DisplayText"
                TextAlignment="LoadingPanelTextAlignment.Right"
                IndicatorAnimationType="WaitIndicatorAnimationType.Flip"
                IndicatorAreaVisible="true"
                CssClass="w-100">
    <IndicatorTemplate>
        <div class="me-4" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
            <img src="../images/logo-litalsa.png" class="h-100" />
        </div>
    </IndicatorTemplate>
</DxLoadingPanel>

@code {
    [Parameter]
    public bool Visible { get; set; }
    [Parameter]
    public string? PositionTarget { get; set; }
    [Parameter]
    public EventCallback<bool> VisibleChanged { get; set; }
    [Parameter]
    public string? Mensaje { get; set; }

    public string DisplayText => string.IsNullOrWhiteSpace(Mensaje)
        ? "Cargando..."
        : Mensaje;

    protected override async Task OnParametersSetAsync()
    {
        PositionTarget = PositionTarget ?? "body";
        await base.OnParametersSetAsync();
    }
}