﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPClientesDestinatarios.Query;

public class GetAllSAPClientesDestinatariosQuery : IRequest<ListResult<SAPClienteDestinatarioDTO>>
{
}

internal class GetAllSAPClientesDestinatariosQueryHandler : IRequestHandler<GetAllSAPClientesDestinatariosQuery, ListResult<SAPClienteDestinatarioDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllSAPClientesDestinatariosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<SAPClienteDestinatarioDTO>> Handle(GetAllSAPClientesDestinatariosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<SAPClienteDestinatarioDTO>
        {
            Data = new List<SAPClienteDestinatarioDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listClientes = await _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<SAPClienteDestinatarioDTO>>(listClientes).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllSAPClientesDestinatariosQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}