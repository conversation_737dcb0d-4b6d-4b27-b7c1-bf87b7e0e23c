﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.TiposElemento.Command;
public class TratarTipoElementoCommand : IRequest<SingleResult<TipoElementoDTO>>
{
    public TipoElementoDTO TipoElementoDTO { get; set; }

    public TratarTipoElementoCommand(TipoElementoDTO tipoElementoDTO)
    {
        TipoElementoDTO = tipoElementoDTO;
    }
}

internal class TratarTipoElementoCommandHandler : IRequestHandler<TratarTipoElementoCommand, SingleResult<TipoElementoDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;

    public TratarTipoElementoCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService)
    {
        _migracionSAPParteTecnicaContext = context;
        _auditoriaService = auditoriaService;
    }

    public async Task<SingleResult<TipoElementoDTO>> Handle(TratarTipoElementoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<TipoElementoDTO>
        {
            Data = new TipoElementoDTO(),
            Errors = new List<string>()
        };

        try
        {
            var dto = request.TipoElementoDTO;

            var usuario = _migracionSAPParteTecnicaContext
                .GetService<IHttpContextAccessor>()?.HttpContext?.User?.Identity?.Name ?? "sistema";

            var existing = await _migracionSAPParteTecnicaContext.TiposElemento
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.TipoElemento>(dto);

                await _migracionSAPParteTecnicaContext.TiposElemento.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = dto;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "CV_TiposElemento",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        nuevo.Id,
                        nuevo.Codigo,
                        nuevo.Descripcion,
                        nuevo.Borrado
                    }),
                    Comentarios = $"Alta de tipo de elemento: Id={nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var valoresAntes = System.Text.Json.JsonSerializer.Serialize(new
                {
                    existing.Id,
                    existing.Codigo,
                    existing.Descripcion,
                    existing.Borrado
                });

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.TiposElemento.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                var valoresDespues = System.Text.Json.JsonSerializer.Serialize(new
                {
                    existing.Id,
                    existing.Codigo,
                    existing.Descripcion,
                    existing.Borrado
                });

                result.Data = dto;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "CV_TiposElemento",
                    IdRegistro = existing.Id,
                    DatosAntes = valoresAntes,
                    DatosDespues = valoresDespues,
                    Comentarios = $"Modificación de tipo de elemento: Id={existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarTipoElementoCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}