﻿@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService
@inject IJSRuntime JS

<div class="eventos-recientes-container">
    <button id="btnNotificaciones" class="eventos-toggle" @onclick="TogglePanel">
        <i class="bi bi-bell-fill"></i>
        @if (eventosNoVistos > 0)
        {
            <span class="notificacion-punto">@eventosNoVistos</span>
        }
    </button>

    <div id="eventosPanel" class="eventos-panel @(mostrarEventos ? "show" : "")">
        <div class="eventos-header">
            <span>🔔 Últimos eventos</span>
            <button class="btn btn-sm btn-link text-danger p-0" @onclick="LimpiarEventos">Limpiar</button>
        </div>
        <ul class="eventos-lista">
            @foreach (var evento in Eventos.Take(15))
            {
                <li>
                    <div>
                        <strong>@evento.Usuario</strong> —
                        <span class="badge bg-secondary">@evento.Accion</span>
                    </div>
                    <div class="text-muted small">@evento.Comentarios</div>
                    <div class="text-muted small">@evento.Fecha.ToString("HH:mm:ss dd/MM") • @evento.Tabla</div>
                </li>
            }
        </ul>
    </div>
</div>

<style>
    .eventos-recientes-container {
        position: fixed;
        z-index: 9999;
    }

    .eventos-toggle {
        position: relative;
        background-color: transparent;
        border: none;
        color: #6f42c1;
        font-size: 1.5rem;
        cursor: pointer;
    }

        .eventos-toggle:hover {
            color: #5a35a0;
        }

    .notificacion-punto {
        position: absolute;
        top: -2px;
        right: -2px;
        background-color: red;
        color: white;
        font-size: 0.65rem;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 16px;
        border-radius: 50%;
        border: 2px solid white;
        animation: parpadeo 1s infinite;
    }

    @@keyframes parpadeo {
        0%, 100%

    {
        opacity: 1;
    }

    50% {
        opacity: 0.2;
    }

    }

    .eventos-panel {
        position: fixed;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 10px;
        width: 320px;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
        padding: 12px;
        transform: translateY(10px);
        opacity: 0;
        pointer-events: none;
        transition: all 0.25s ease-in-out;
        z-index: 10000;
        top: 0;
        left: 0;
    }

        .eventos-panel.show {
            transform: translateY(0);
            opacity: 1;
            pointer-events: auto;
        }

    .eventos-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-weight: bold;
    }

    .eventos-lista {
        list-style: none;
        padding: 0;
        margin: 0;
    }

        .eventos-lista li {
            padding: 6px 0;
            border-bottom: 1px solid #eee;
        }
</style>

@code {
    private List<AuditoriaDTO> Eventos = new();
    private HashSet<int> EventosVistos = new();
    private bool mostrarEventos = false;
    private int eventosNoVistos = 0;

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(500);
        await CargarEventosAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("hacerBotonArrastrable", "btnNotificaciones");
        }
    }

    private async Task CargarEventosAsync()
    {
        var result = await MigracionSAPParteTecnicaService.GetUltimosEventosQuery();
        if (!result.HasErrors)
        {
            Eventos = result.Data;
            eventosNoVistos = Eventos.Count(e => !EventosVistos.Contains(e.Id));
            await InvokeAsync(StateHasChanged);
        }
    }

    public async Task RecargarEventosAsync()
    {
        await CargarEventosAsync();
    }

    private void LimpiarEventos()
    {
        Eventos.Clear();
        EventosVistos.Clear();
        eventosNoVistos = 0;
    }

    private async Task TogglePanel()
    {
        mostrarEventos = !mostrarEventos;

        if (mostrarEventos)
        {
            foreach (var evento in Eventos)
            {
                EventosVistos.Add(evento.Id);
            }
            eventosNoVistos = 0;

            await JS.InvokeVoidAsync("posicionarPanelEventos");
        }
    }
}