﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Query;
public class GetAllDatosAuxMaterialesQuery : IRequest<ListResult<DatosAuxMaterialesDTO>>
{
}

internal class GetAllDatosAuxMaterialesQueryHandler : IRequestHandler<GetAllDatosAuxMaterialesQuery, ListResult<DatosAuxMaterialesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllDatosAuxMaterialesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<DatosAuxMaterialesDTO>> Handle(GetAllDatosAuxMaterialesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<DatosAuxMaterialesDTO>
        {
            Data = new List<DatosAuxMaterialesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listDatosAux = await _migracionSAPParteTecnicaContext.DatosAuxMateriales
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<DatosAuxMaterialesDTO>>(listDatosAux).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllDatosAuxMaterialesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}