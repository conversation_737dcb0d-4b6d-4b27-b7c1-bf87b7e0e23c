﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;

public class ImportarCorrespondenciasSkusCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarCorrespondenciasSkusCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarCorrespondenciasSkusHandler : IRequestHandler<ImportarCorrespondenciasSkusCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarCorrespondenciasSkusHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarCorrespondenciasSkusCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new CorrespondenciasSkusDTO
                    {
                        CodigoTinta = worksheet.Cell(filaActual, 2).GetValue<int>(),
                        CodigoProveedor = worksheet.Cell(filaActual, 3).GetValue<int>(),
                        Pl = worksheet.Cell(filaActual, 4).GetString().Trim(),
                        PesoNeto = worksheet.Cell(filaActual, 5).GetValue<decimal>(),
                        Consigna = worksheet.Cell(filaActual, 6).GetValue<bool>(),
                        Borrado = false
                    };

                    if (dto.CodigoTinta == 0 || dto.CodigoProveedor == 0 || string.IsNullOrWhiteSpace(dto.Pl))
                    {
                        errores.Add($"Fila {filaActual}: Código Litalsa, Código Proveedor y PL son obligatorios.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.CorrespondenciasSkus
                        .FirstOrDefaultAsync(c =>
                            c.CodigoTinta == dto.CodigoTinta &&
                            c.CodigoProveedor == dto.CodigoProveedor &&
                            c.Pl == dto.Pl,
                            cancellationToken);

                    var clave = $"{dto.CodigoTinta}_{dto.CodigoProveedor}_{dto.Pl}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Tintas.CorrespondenciasSkus>(dto);
                        await _context.CorrespondenciasSkus.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "CorrespondenciasSkus",
                            IdRegistro = entidad.Id,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var pesoAnterior = existente.PesoNeto;
                        var consignaAnterior = existente.Consigna;
                        var borradoAnterior = existente.Borrado;

                        TinyMapper.Map(dto, existente);

                        if (pesoAnterior != existente.PesoNeto ||
                            consignaAnterior != existente.Consigna ||
                            borradoAnterior != existente.Borrado)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "CorrespondenciasSkus",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = "Actualizado desde Excel"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    errores.Add($"Fila {filaActual}: Error → {ex.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar CorrespondenciasSkus: {ex.Message}";
        }

        return result;
    }
}