﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
public class GetAllImposicionesQuery : IRequest<ListResult<ImposicionesDTO>>
{
}

internal class GetAllImposicionesQueryHandler : IRequestHandler<GetAllImposicionesQuery, ListResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllImposicionesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ImposicionesDTO>> Handle(GetAllImposicionesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ImposicionesDTO>
        {
            Data = new List<ImposicionesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listImposiciones = await _migracionSAPParteTecnicaContext.Imposiciones
                .AsNoTracking()
                .Include(i => i.IdTroquelNavigation)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ImposicionesDTO>>(listImposiciones).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllImposicionesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}