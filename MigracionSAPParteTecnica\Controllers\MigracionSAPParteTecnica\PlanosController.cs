﻿using Microsoft.AspNetCore.Mvc;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Controllers.MigracionSAPParteTecnica;

[Route("api/planos")]
[ApiController]
public class PlanosController : ControllerBase
{
    private readonly PlanoService _planoService;

    public PlanosController(PlanoService planoService)
    {
        _planoService = planoService;
    }

    [HttpGet("{codigoIn2:int}/{nombrePlano}")]
    public IActionResult GetPdf(int codigoIn2, string nombrePlano)
    {
        var ruta = _planoService.BuscarRutaPlano(codigoIn2, nombrePlano);
        if (ruta == null)
            return NotFound("Plano no encontrado.");

        var bytes = _planoService.LeerPlano(ruta);
        return File(bytes, "application/pdf", $"{nombrePlano}.pdf");
    }

    [HttpHead("{codigoIn2:int}/{nombrePlano}")]
    public IActionResult ComprobarPlanoExiste(int codigoIn2, string nombrePlano)
    {
        var ruta = _planoService.BuscarRutaPlano(codigoIn2, nombrePlano);
        if (ruta == null)
            return NotFound();

        return Ok();
    }
}