﻿using MigracionSAPParteTecnica.DTO.MM.Tintas;

namespace MigracionSAPParteTecnica.Validators.MM.Tintas;
public static class ColoresValidator
{
    public static string? Validar(ColoresDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.CodigoColor))
            return "El código de color no puede estar vacío.";

        if (dto.CodigoColor.Length > 2)
            return "El código de color no puede tener más de dos caracteres.";

        if (string.IsNullOrWhiteSpace(dto.Color))
            return "El nombre del color no puede estar vacío.";

        return null;
    }
}