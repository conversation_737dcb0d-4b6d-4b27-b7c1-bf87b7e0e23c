﻿@page "/GestionPaquetesConsignaNestle"

@attribute [Authorize(Roles = "materiales, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var codigoEnvase = (CodigoEnvasesDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(codigoEnvase)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false"/>
        <DxGridDataColumn FieldName="CodigoPaquete" Caption="Código paquete" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.ConsignaNestle"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/ConsignaNestle"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditPaqueteConsignaNestlePopUp @ref="editPaqueteConsignaNestlePopUp" OnSave="GuardarCambios" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<ConsignaNestleDTO> Data = new();
    string GridSearchText = "";
    private EditPaqueteConsignaNestlePopUp? editPaqueteConsignaNestlePopUp;
    private ConsignaNestleDTO? selectedPaqueteConsignaNestleDTO { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (ConsignaNestleDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllPaquetesConsignaNestle();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<ConsignaNestleDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedPaqueteConsignaNestleDTO = dataItem as ConsignaNestleDTO ?? new ConsignaNestleDTO();
        editPaqueteConsignaNestlePopUp?.AbrirPopUp(selectedPaqueteConsignaNestleDTO);
    }

    async Task GuardarCambios(ConsignaNestleDTO updatedConsignaNestleDTO)
    {
        _isLoading = true;
        selectedPaqueteConsignaNestleDTO = updatedConsignaNestleDTO;

        var result = await MigracionSAPParteTecnicaService.TratarConsignaNestle(selectedPaqueteConsignaNestleDTO);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (ConsignaNestleDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteConsignaNestle(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Paquete de consigna eliminado correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}