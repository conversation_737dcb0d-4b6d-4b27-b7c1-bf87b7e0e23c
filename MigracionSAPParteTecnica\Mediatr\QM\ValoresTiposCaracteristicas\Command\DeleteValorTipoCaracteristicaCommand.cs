﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Command;
public class DeleteValorTipoCaracteristicaCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteValorTipoCaracteristicaCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteValorTipoCaracteristicaCommandHandler : IRequestHandler<DeleteValorTipoCaracteristicaCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteValorTipoCaracteristicaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteValorTipoCaracteristicaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var valorTipoCaracteristica = await _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas
                .FirstOrDefaultAsync(v => v.Id == request.Id, cancellationToken);

            if(valorTipoCaracteristica == null)
            {
                result.Errors.Add("No se ha encontrado el valor de la característica a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(valorTipoCaracteristica);

            valorTipoCaracteristica.Borrado = true;

            _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas.Update(valorTipoCaracteristica);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;


            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "QM_ValoresTiposCaracteristicas",
                IdRegistro = valorTipoCaracteristica.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de valor tipo característica: Id={valorTipoCaracteristica.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteValorTipoCaracteristicaCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}