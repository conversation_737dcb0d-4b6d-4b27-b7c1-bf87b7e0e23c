﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;

namespace MigracionSAPParteTecnica.DTO.CV;
public class TroquelesDTO
{
    public int Id { get; set; }

    public string? IdFormatoIn2 { get; set; }

    public int? Cliente_IN2 { get; set; }

    public int? Cliente_SAP { get; set; }

    public string? TipoElemento { get; set; }

    public string? PlanoEjemplo { get; set; }

    public string? Familia { get; set; }

    public string? Subfamilia { get; set; }

    public string? FormatoEnvase { get; set; }

    public decimal? DiametroEnvase { get; set; }

    public decimal? DiametroReal { get; set; }

    public decimal? AlturaEnvase { get; set; }

    public decimal? AlturaElemento { get; set; }

    public decimal? Desarrollo { get; set; }

    public decimal? ReservasIzquierda { get; set; }

    public decimal? ReservasDerecha { get; set; }

    public decimal? ReservasSuperiores { get; set; }

    public decimal? ReservasInferiores { get; set; }

    public decimal? EjeMayor { get; set; }

    public decimal? EjeMenor { get; set; }

    public bool? Embuticion { get; set; }

    public string? Tipo { get; set; }

    public int? PedidoEjemplo { get; set; }

    public string? Descripcion { get; set; }

    public DateTime? FechaAlta { get; set; }

    public string? Observaciones { get; set; }

    public string? ClaveTecnica { get; set; }

    public bool? Activo { get; set; }

    public int? Cliente_IN2_Origen { get; set; }

    public int? Cliente_SAP_Origen { get; set; }

    public string? Producto_Origen { get; set; }

    public decimal? DiametroReal_Origen { get; set; }

    public decimal? AlturaElemento_Origen { get; set; }

    public decimal? Desarrollo_Origen { get; set; }

    public decimal? ResIzq_Origen { get; set; }

    public decimal? ResDcha_Origen { get; set; }

    public decimal? ResInf_Origen { get; set; }

    public decimal? ResSup_Origen { get; set; }

    public bool? Embuticion_Origen { get; set; }

    public string? IdFormato_Origen { get; set; }

    public string? Plano_Origen { get; set; }

    public bool EsAutomatico { get; set; }

    public bool OrigenModificado { get; set; }
    public string? EmbuticionTexto => (Embuticion ?? false) ? "Si" : "No";
    public string? ClienteNombreConcatenado { get; set; } = "";
}
