﻿using DevExpress.Blazor;
using Microsoft.AspNetCore.Components;
using MigracionSAPParteTecnica.Services;
using System.Collections.ObjectModel;

namespace MigracionSAPParteTecnica.Components.Shared.Grid;

public abstract class GridPageBase<T> : ComponentBase where T : class
{
    protected ObservableCollection<T> Data = new();
    protected DxGrid? Grid;
    protected string GridSearchText = "";
    protected bool _isLoading;

    [Inject] protected IToastService? ToastService { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;
        await OnInitExtraAsync();
        await CargarGrid();
        _isLoading = false;
    }
    
    protected virtual Task OnInitExtraAsync() => Task.CompletedTask;

    protected abstract Task<List<T>> ObtenerDatosAsync();
    protected abstract Task<bool> EliminarElementoAsync(T item);

    protected virtual async Task CargarGrid()
    {
        var datos = await ObtenerDatosAsync();
        Data = new ObservableCollection<T>(datos);
        StateHasChanged();
    }

    protected virtual async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (T)e.DataItem;

        var ok = await EliminarElementoAsync(item);

        if (ok)
        {
            await CargarGrid();
            ToastService?.MostrarOk("Elemento eliminado correctamente.");
        }

        _isLoading = false;
    }

    protected virtual async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (T)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }
}