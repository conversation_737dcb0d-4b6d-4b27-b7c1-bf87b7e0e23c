﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using Nelibur.ObjectMapper;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Barnices;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.General;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Tintas;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.QM;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.General;

namespace MigracionSAPParteTecnica.Infraestructure;
public class MappingConfig
{
    public static void ConfigureMappings()
    {
        // Mapeo de auditoría
        TinyMapper.Bind<List<Auditoria>, List<AuditoriaDTO>>();
        TinyMapper.Bind<List<AuditoriaDTO>, List<Auditoria>>();
        TinyMapper.Bind<Auditoria, AuditoriaDTO>();
        TinyMapper.Bind<AuditoriaDTO, Auditoria>();

        // Mapeo de troquel
        TinyMapper.Bind<List<Troqueles>, List<TroquelesDTO>>();
        TinyMapper.Bind<List<TroquelesDTO>, List<Troqueles>>();
        TinyMapper.Bind<Troqueles, TroquelesDTO>();
        TinyMapper.Bind<Troqueles, Troqueles>();
        TinyMapper.Bind<TroquelesDTO, Troqueles>();

        // Mapeo de plano
        TinyMapper.Bind<List<Plano>, List<PlanoDTO>>();
        TinyMapper.Bind<List<PlanoDTO>, List<Plano>>();
        TinyMapper.Bind<Plano, PlanoDTO>();
        TinyMapper.Bind<PlanoDTO, Plano>();

        // Mapeo de imposición
        TinyMapper.Bind<List<Imposiciones>, List<ImposicionesDTO>>();
        TinyMapper.Bind<List<ImposicionesDTO>, List<Imposiciones>>();
        TinyMapper.Bind<Imposiciones, ImposicionesDTO>();
        TinyMapper.Bind<ImposicionesDTO, Imposiciones>();

        // Mapeo de tipos de características
        TinyMapper.Bind<List<TiposCaracteristicas>, List<TiposCaracteristicasDTO>>();
        TinyMapper.Bind<List<TiposCaracteristicasDTO>, List<TiposCaracteristicas>>();
        TinyMapper.Bind<TiposCaracteristicas, TiposCaracteristicasDTO>();
        TinyMapper.Bind<TiposCaracteristicasDTO, TiposCaracteristicas>();

        // Mapeo de valores de tipos de características
        TinyMapper.Bind<List<ValoresTiposCaracteristicas>, List<ValoresTiposCaracteristicasDTO>>();
        TinyMapper.Bind<List<ValoresTiposCaracteristicasDTO>, List<ValoresTiposCaracteristicas>>();
        TinyMapper.Bind<ValoresTiposCaracteristicas, ValoresTiposCaracteristicasDTO>();
        TinyMapper.Bind<ValoresTiposCaracteristicasDTO, ValoresTiposCaracteristicas>();

        // Mapeo de valores de métodos de inspección
        TinyMapper.Bind<List<MetodosInspeccion>, List<MetodosInspeccionDTO>>();
        TinyMapper.Bind<List<MetodosInspeccionDTO>, List<MetodosInspeccion>>();
        TinyMapper.Bind<MetodosInspeccion, MetodosInspeccionDTO>();
        TinyMapper.Bind<MetodosInspeccionDTO, MetodosInspeccion>();

        // Mapeo de valores de características de inspección
        TinyMapper.Bind<List<CaracteristicasInspeccion>, List<CaracteristicasInspeccionDTO>>();
        TinyMapper.Bind<List<CaracteristicasInspeccionDTO>, List<CaracteristicasInspeccion>>();
        TinyMapper.Bind<CaracteristicasInspeccion, CaracteristicasInspeccionDTO>();
        TinyMapper.Bind<CaracteristicasInspeccionDTO, CaracteristicasInspeccion>();

        // MAPEO DE VALORES DE MM
        // Barnices
        // Mapeo de valores de codigo envase
        TinyMapper.Bind<List<CodigoEnvases>, List<CodigoEnvasesDTO>>();
        TinyMapper.Bind<List<CodigoEnvasesDTO>, List<CodigoEnvases>>();
        TinyMapper.Bind<CodigoEnvases, CodigoEnvasesDTO>();
        TinyMapper.Bind<CodigoEnvasesDTO, CodigoEnvases>();

        // Mapeo de valores de excepciones de precio
        TinyMapper.Bind<List<ExcepcionesPreciosLotes>, List<ExcepcionesPreciosLotesDTO>>();
        TinyMapper.Bind<List<ExcepcionesPreciosLotesDTO>, List<ExcepcionesPreciosLotes>>();
        TinyMapper.Bind<ExcepcionesPreciosLotes, ExcepcionesPreciosLotesDTO>();
        TinyMapper.Bind<ExcepcionesPreciosLotesDTO, ExcepcionesPreciosLotes>();

        // Mapeo de valores de familias
        TinyMapper.Bind<List<Familias>, List<FamiliasDTO>>();
        TinyMapper.Bind<List<FamiliasDTO>, List<Familias>>();
        TinyMapper.Bind<Familias, FamiliasDTO>();
        TinyMapper.Bind<FamiliasDTO, Familias>();

        // Mapeo de valores de Naturalezas
        TinyMapper.Bind<List<Naturaleza>, List<NaturalezaDTO>>();
        TinyMapper.Bind<List<NaturalezaDTO>, List<Naturaleza>>();
        TinyMapper.Bind<Naturaleza, NaturalezaDTO>();
        TinyMapper.Bind<NaturalezaDTO, Naturaleza>();

        // Mapeo de valores de barnices nodriza
        TinyMapper.Bind<List<Nodrizas>, List<NodrizasDTO>>();
        TinyMapper.Bind<List<NodrizasDTO>, List<Nodrizas>>();
        TinyMapper.Bind<Nodrizas, NodrizasDTO>();
        TinyMapper.Bind<NodrizasDTO, Nodrizas>();

        // Mapeo de valores de codigo envase
        TinyMapper.Bind<List<NoIncluirRegCompras>, List<NoIncluirRegComprasDTO>>();
        TinyMapper.Bind<List<NoIncluirRegComprasDTO>, List<NoIncluirRegCompras>>();
        TinyMapper.Bind<NoIncluirRegCompras, NoIncluirRegComprasDTO>();
        TinyMapper.Bind<NoIncluirRegComprasDTO, NoIncluirRegCompras>();

        // Bobinas paquetes
        // Mapeo de valores de paquetes consigna nestle
        TinyMapper.Bind<List<ConsignaNestle>, List<ConsignaNestleDTO>>();
        TinyMapper.Bind<List<ConsignaNestleDTO>, List<ConsignaNestle>>();
        TinyMapper.Bind<ConsignaNestle, ConsignaNestleDTO>();
        TinyMapper.Bind<ConsignaNestleDTO, ConsignaNestle>();

        // Mapeo de valores de datos aux
        TinyMapper.Bind<List<DatosAuxMateriales>, List<DatosAuxMaterialesDTO>>();
        TinyMapper.Bind<List<DatosAuxMaterialesDTO>, List<DatosAuxMateriales>>();
        TinyMapper.Bind<DatosAuxMateriales, DatosAuxMaterialesDTO>();
        TinyMapper.Bind<DatosAuxMaterialesDTO, DatosAuxMateriales>();

        // Generales
        // Mapeo de valores de envases
        TinyMapper.Bind<List<Envases>, List<EnvasesDTO>>();
        TinyMapper.Bind<List<EnvasesDTO>, List<Envases>>();
        TinyMapper.Bind<Envases, EnvasesDTO>();
        TinyMapper.Bind<EnvasesDTO, Envases>();

        // Tintas
        // Mapeo de valores de envases
        TinyMapper.Bind<List<Colores>, List<ColoresDTO>>();
        TinyMapper.Bind<List<ColoresDTO>, List<Colores>>();
        TinyMapper.Bind<Colores, ColoresDTO>();
        TinyMapper.Bind<ColoresDTO, Colores>();

        // Mapeo de correspondencias SKUS
        TinyMapper.Bind<List<CorrespondenciasSkus>, List<CorrespondenciasSkusDTO>>();
        TinyMapper.Bind<List<CorrespondenciasSkusDTO>, List<CorrespondenciasSkus>>();
        TinyMapper.Bind<CorrespondenciasSkus, CorrespondenciasSkusDTO>();
        TinyMapper.Bind<CorrespondenciasSkusDTO, CorrespondenciasSkus>();

        // Mapeo de relacion compuestas
        TinyMapper.Bind<List<RelacionCompuestas>, List<RelacionCompuestasDTO>>();
        TinyMapper.Bind<List<RelacionCompuestasDTO>, List<RelacionCompuestas>>();
        TinyMapper.Bind<RelacionCompuestas, RelacionCompuestasDTO>();
        TinyMapper.Bind<RelacionCompuestasDTO, RelacionCompuestas>();

        // Mapeo de materiales
        TinyMapper.Bind<List<Materiales>, List<MaterialesDTO>>();
        TinyMapper.Bind<List<MaterialesDTO>, List<Materiales>>();
        TinyMapper.Bind<Materiales, MaterialesDTO>();
        TinyMapper.Bind<MaterialesDTO, Materiales>();

        // Mapeo de SAPCliente
        TinyMapper.Bind<List<SAPCliente>, List<SAPClienteDTO>>();
        TinyMapper.Bind<List<SAPClienteDTO>, List<SAPCliente>>();
        TinyMapper.Bind<SAPCliente, SAPClienteDTO>();
        TinyMapper.Bind<SAPClienteDTO, SAPCliente>();

        // Mapeo de SAPClienteDestinatario
        TinyMapper.Bind<List<SAPClienteDestinatario>, List<SAPClienteDestinatarioDTO>>();
        TinyMapper.Bind<List<SAPClienteDestinatarioDTO>, List<SAPClienteDestinatario>>();
        TinyMapper.Bind<SAPClienteDestinatario, SAPClienteDestinatarioDTO>();
        TinyMapper.Bind<SAPClienteDestinatarioDTO, SAPClienteDestinatario>();

        // Mapeo de SAPProveedor
        TinyMapper.Bind<List<SAPProveedor>, List<SAPProveedorDTO>>();
        TinyMapper.Bind<List<SAPProveedorDTO>, List<SAPProveedor>>();
        TinyMapper.Bind<SAPProveedor, SAPProveedorDTO>();
        TinyMapper.Bind<SAPProveedorDTO, SAPProveedor>();

        // Mapeo de TipoElemento
        TinyMapper.Bind<List<TipoElemento>, List<TipoElementoDTO>>();
        TinyMapper.Bind<List<TipoElementoDTO>, List<TipoElemento>>();
        TinyMapper.Bind<TipoElemento, TipoElementoDTO>();
        TinyMapper.Bind<TipoElementoDTO, TipoElemento>();

        // Mapeo de Maestro
        TinyMapper.Bind<List<Maestro>, List<MaestroDTO>>();
        TinyMapper.Bind<List<MaestroDTO>, List<Maestro>>();
        TinyMapper.Bind<Maestro, MaestroDTO>();
        TinyMapper.Bind<MaestroDTO, Maestro>();


        TinyMapper.Bind<MaestroBrutoDTO, MaestroDTO>();
        TinyMapper.Bind<MaestroDTO, Maestro>(config => {
            config.Ignore(x => x.Id);
        });

        TinyMapper.Bind<Maestro, Imposiciones>();

        // Mapeo de Motivo
        TinyMapper.Bind<List<Motivos>, List<MotivosDTO>>();
        TinyMapper.Bind<List<MotivosDTO>, List<Motivos>>();
        TinyMapper.Bind<Motivos, MotivosDTO>();
        TinyMapper.Bind<MotivosDTO, Motivos>();
    }
}