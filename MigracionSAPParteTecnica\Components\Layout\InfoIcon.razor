﻿@code {
    [Parameter] public string BodyText { get; set; } = "";

    [Parameter] public string? TargetId { get; set; }

    private string UniqueId = Guid.NewGuid().ToString("N");
    private string InternalTargetId => TargetId ?? $"info-target-{UniqueId}";
    private string FlyoutId => $"flyout-{UniqueId}";
    private string PositionTarget => $"#{InternalTargetId}";

    private bool IsOpen { get; set; }
}

<button class="icon-flyout-button"
        @onclick="() => IsOpen = !IsOpen"
        id="@InternalTargetId"
        aria-describedby="@FlyoutId"
        type="button">
    <i class="bi bi-info-circle-fill" style="color: #0d6efd; font-size: 1.1rem;"></i>
</button>

<DxFlyout Id="@FlyoutId"
          @bind-IsOpen="IsOpen"
          HeaderVisible="false"
          BodyText="@BodyText"
          PositionTarget="@PositionTarget"
          Width="280px"
          CloseMode="FlyoutCloseMode.Hide"
          CloseOnOutsideClick="true"
          PreventCloseOnPositionTargetClick="true" />
