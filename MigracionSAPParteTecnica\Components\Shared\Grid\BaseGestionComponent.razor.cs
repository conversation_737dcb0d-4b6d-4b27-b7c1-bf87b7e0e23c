﻿//using DevExpress.Blazor;
//using Microsoft.AspNetCore.Components;
//using System.Collections.ObjectModel;

//namespace MigracionSAPParteTecnica.Components.Shared.Grid;
//public abstract class BaseGestionComponent<TDto> : ComponentBase where TDto : class, new()
//{
//    protected bool _isLoading;
//    protected ObservableCollection<TDto> Data = new();
//    protected string GridSearchText = "";
//    protected DxGrid? Grid;
//    protected TDto? SelectedItem;
//    protected abstract Task<List<TDto>> CargarDatos();
//    protected abstract Task<ResultadoOperacion> Guardar(TDto dto);
//    protected abstract Task<ResultadoOperacion> Borrar(int id);
//    protected abstract int ObtenerId(TDto dto);

//    protected virtual async Task CargarGrid()
//    {
//        var resultado = await CargarDatos();
//        Data = new ObservableCollection<TDto>(resultado);
//        StateHasChanged();
//    }

//    protected void AbrirPopUp(object? item)
//    {
//        SelectedItem = item as TDto ?? new TDto();
//        AbrirDialogo(SelectedItem);
//    }

//    protected async Task GuardarCambios(TDto item)
//    {
//        _isLoading = true;
//        var result = await Guardar(item);
//        if (!result.EsValido)
//        {
//            MostrarError(result.Mensaje);
//        }
//        else
//        {
//            await CargarGrid();
//            MostrarOk("Guardado correctamente.");
//        }
//        _isLoading = false;
//    }

//    protected async Task Eliminar(GridDataItemDeletingEventArgs e)
//    {
//        _isLoading = true;
//        var item = (TDto)e.DataItem;
//        var result = await Borrar(ObtenerId(item));
//        if (!result.EsValido)
//        {
//            MostrarError(result.Mensaje);
//        }
//        else
//        {
//            await CargarGrid();
//            MostrarOk("Eliminado correctamente.");
//        }
//        _isLoading = false;
//    }

//    protected abstract void AbrirDialogo(TDto item);
//    protected abstract void MostrarOk(string mensaje);
//    protected abstract void MostrarError(string mensaje);
//}