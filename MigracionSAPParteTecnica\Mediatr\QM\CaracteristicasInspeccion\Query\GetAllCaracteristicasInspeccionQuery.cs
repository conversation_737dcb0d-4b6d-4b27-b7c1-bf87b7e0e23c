﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Query;

public class GetAllCaracteristicasInspeccionQuery : IRequest<ListResult<CaracteristicasInspeccionDTO>>
{
}

internal class GetAllCaracteristicasInspeccionQueryHandler : IRequestHandler<GetAllCaracteristicasInspeccionQuery, ListResult<CaracteristicasInspeccionDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllCaracteristicasInspeccionQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<CaracteristicasInspeccionDTO>> Handle(GetAllCaracteristicasInspeccionQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<CaracteristicasInspeccionDTO>
        {
            Data = new List<CaracteristicasInspeccionDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listCaracteristicas = await _migracionSAPParteTecnicaContext.CaracteristicasInspeccion
                .AsNoTracking()
                .Include(t => t.MetodoInsNavigation)
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<CaracteristicasInspeccionDTO>>(listCaracteristicas).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllCaracteristicasInspeccionQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}