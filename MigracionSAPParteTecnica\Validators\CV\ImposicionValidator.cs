﻿using MigracionSAPParteTecnica.DTO.CV;

namespace MigracionSAPParteTecnica.Validators.CV;
public static class ImposicionValidator
{
    public static string? Validar(ImposicionesDTO dto)
    {
        if (dto.HojaScroll.GetValueOrDefault() && !dto.LargoScroll.HasValue)
            return "Debe indicar el largo del scroll si la hoja es scroll.";

        if (!dto.NumeroAlturas.HasValue)
            return "Debe indicar el número de alturas.";

        if (!dto.NumeroDesarrollos.HasValue)
            return "Debe indicar el número de desarrollos.";

        if (string.IsNullOrWhiteSpace(dto.SentidoLectura))
            return "Debe indicar el sentido de lectura.";

        if (!dto.ArranquePinza.HasValue)
            return "Debe indicar el arranque por pinza.";

        if (!dto.ArranqueEscuadra.HasValue)
            return "Debe indicar el arranque por escuadra.";

        if (!dto.PosicionEscuadraExt.HasValue)
            return "Debe indicar la posición de escuadra exterior.";

        return null;
    }
}