﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;

public class DeleteDatosAuxCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteDatosAuxCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteDatosAuxCommandHandler : IRequestHandler<DeleteDatosAuxCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteDatosAuxCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteDatosAuxCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var datoAux = await _migracionSAPParteTecnicaContext.DatosAuxMateriales
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (datoAux == null)
            {
                result.Errors.Add("No se ha encontrado el dato auxiliar a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(datoAux);

            datoAux.Borrado = true;

            _migracionSAPParteTecnicaContext.DatosAuxMateriales.Update(datoAux);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_BobinasPaquetes_DatosAuxMateriales",
                IdRegistro = datoAux.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de dato auxiliar: Código={datoAux.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteDatosAuxCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}