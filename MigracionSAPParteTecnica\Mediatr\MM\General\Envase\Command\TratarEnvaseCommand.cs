﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;


namespace MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
public class TratarEnvaseCommand : IRequest<SingleResult<EnvasesDTO>>
{
    public EnvasesDTO EnvaseDTO { get; set; }

    public TratarEnvaseCommand(EnvasesDTO envaseDTO)
    {
        EnvaseDTO = envaseDTO;
    }
}

internal class TratarEnvaseCommandHandler : IRequestHandler<TratarEnvaseCommand, SingleResult<EnvasesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarEnvaseCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<EnvasesDTO>> Handle(TratarEnvaseCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<EnvasesDTO>
        {
            Data = new EnvasesDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.EnvaseDTO;

            var existing = await _migracionSAPParteTecnicaContext.Envases
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.General.Envases>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.Envases.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<EnvasesDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Envases",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de envase: Codigo = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.Envases.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<EnvasesDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Envases",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de envase: Codigo = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarEnvaseCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}