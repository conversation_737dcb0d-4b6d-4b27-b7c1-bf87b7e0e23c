﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;

public class TratarExcepcionesPreciosLotesCommand : IRequest<SingleResult<ExcepcionesPreciosLotesDTO>>
{
    public ExcepcionesPreciosLotesDTO ExcepcionPreciosLotesDTO { get; set; }

    public TratarExcepcionesPreciosLotesCommand(ExcepcionesPreciosLotesDTO excepcionPreciosLotesDTO)
    {
        ExcepcionPreciosLotesDTO = excepcionPreciosLotesDTO;
    }
}

internal class TratarExcepcionesPreciosLotesCommandHandler : IRequestHandler<TratarExcepcionesPreciosLotesCommand, SingleResult<ExcepcionesPreciosLotesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarExcepcionesPreciosLotesCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<ExcepcionesPreciosLotesDTO>> Handle(TratarExcepcionesPreciosLotesCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<ExcepcionesPreciosLotesDTO>
        {
            Data = new ExcepcionesPreciosLotesDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var dto = request.ExcepcionPreciosLotesDTO;

        try
        {

            var existing = await _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.ExcepcionesPreciosLotes>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<ExcepcionesPreciosLotesDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Barnices_ExcepcionesPreciosLotes",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de excepción de precio lote: Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);
                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<ExcepcionesPreciosLotesDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Barnices_ExcepcionesPreciosLotes",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de excepción de precio lote: Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarMetodosInspeccion - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}
