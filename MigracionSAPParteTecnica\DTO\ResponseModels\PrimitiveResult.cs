﻿using System.Text.Json.Serialization;

namespace MigracionSAPParteTecnica.DTO.ResponseModels;

[Serializable]
public class PrimitiveResult
{
    [JsonPropertyName("success")]
    public bool Success { get; set; } = true;

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("errors")]
    public List<string> Errors { get; set; } = [];

    [JsonPropertyName("info")]
    public List<string> Info { get; set; } = [];

    [JsonPropertyName("hasErrors")]
    public bool HasErrors => Errors.Any();

    [JsonPropertyName("hasInfo")]
    public bool HasInfo => Info.Any();

    [JsonIgnore]
    public string? FirstError => Errors.FirstOrDefault();

    public static PrimitiveResult Ok(string? message = null) => new()
    {
        Success = true,
        Message = message ?? string.Empty
    };

    public static PrimitiveResult Error(string error) => new()
    {
        Success = false,
        Errors = [error]
    };

    public static PrimitiveResult Error(IEnumerable<string> errores) => new()
    {
        Success = false,
        Errors = errores.ToList()
    };
}