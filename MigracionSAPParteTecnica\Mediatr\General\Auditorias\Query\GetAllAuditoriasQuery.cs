﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.Auditorias.Query;
public class GetAllAuditoriasQuery : IRequest<ListResult<AuditoriaDTO>>
{
}

internal class GetAllAuditoriasQueryHandler : IRequestHandler<GetAllAuditoriasQuery, ListResult<AuditoriaDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllAuditoriasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<AuditoriaDTO>> Handle(GetAllAuditoriasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<AuditoriaDTO>
        {
            Data = new List<AuditoriaDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listAuditoria = await _migracionSAPParteTecnicaContext.Auditorias
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<AuditoriaDTO>>(listAuditoria).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllAuditorias - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}