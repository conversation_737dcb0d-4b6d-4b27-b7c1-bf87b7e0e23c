﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link href="_content/DevExpress.Blazor.Themes/blazing-berry.bs5.css" rel="stylesheet" asp-append-version="true" />
    <link rel="stylesheet" href="MigracionSAPParteTecnica.styles.css" />
    <link rel="stylesheet" href="bootstrap/bootstrap-icons.min.css" />
    @* <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"> *@
    @* <link rel="stylesheet" href="_content/DevExpress.Blazor.Reporting.Viewer/css/dx-blazor-reporting-components.bs5.css"> *@
    @* <link rel="stylesheet" href="bootstrap/bootstrap.min.css" /> *@
    @DxResourceManager.RegisterScripts()
    @* <link rel="stylesheet" href="app.css" /> *@
    <link rel="stylesheet" href="MigracionSAPParteTecnica.styles.css" />
    <link rel="icon" type="image/png" href="/favicon.ico" />
    <HeadOutlet />
</head>

<body>
    <CascadingAuthenticationState>
        <Routes @rendermode="InteractiveServer" />
    </CascadingAuthenticationState>
    <script src="js/downloadHelper.js"></script>
    <script src="js/blazorFileDrop.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.10/signalr.min.js"></script>
    <script src="js/eventosNotificaciones.js"></script>
    <script src="js/notificaciones.js"></script>
    <script src="js/signalr-helper.js"></script>
    <script src="_framework/blazor.web.js"></script>
    <link rel="stylesheet" href="app.css" />

</body>

</html>