﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPClientesDestinatarios.Command;
public class DeleteSAPClienteDestinatarioCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteSAPClienteDestinatarioCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteSAPClienteDestinatarioCommandHandler : IRequestHandler<DeleteSAPClienteDestinatarioCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteSAPClienteDestinatarioCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteSAPClienteDestinatarioCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var destinatario = await _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (destinatario == null)
            {
                result.Errors.Add("No se ha encontrado el destinatario a borrar.");
                return result;
            }

            destinatario.Borrado = true;

            _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios.Update(destinatario);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            var datosAntes = JsonSerializer.Serialize(destinatario);

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "SAP_Clientes_Destinatarios",
                IdRegistro = destinatario.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica del destinatario: ID={request.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteSAPClienteCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}