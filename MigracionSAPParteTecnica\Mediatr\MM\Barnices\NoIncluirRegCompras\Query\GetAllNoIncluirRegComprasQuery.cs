﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Query;

public class GetAllNoIncluirRegComprasQuery : IRequest<ListResult<NoIncluirRegComprasDTO>>
{
}

internal class GetAllNoIncluirRegComprasQueryHandler : IRequestHandler<GetAllNoIncluirRegComprasQuery, ListResult<NoIncluirRegComprasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllNoIncluirRegComprasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<NoIncluirRegComprasDTO>> Handle(GetAllNoIncluirRegComprasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<NoIncluirRegComprasDTO>
        {
            Data = new List<NoIncluirRegComprasDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listNoIncluir = await _migracionSAPParteTecnicaContext.NoIncluirRegCompras
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<NoIncluirRegComprasDTO>>(listNoIncluir).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllNoIncluirRegComprasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}