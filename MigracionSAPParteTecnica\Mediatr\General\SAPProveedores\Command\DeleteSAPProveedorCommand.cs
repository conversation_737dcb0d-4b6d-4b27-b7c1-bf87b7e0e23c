﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPProveedores.Command;
public class DeleteSAPProveedorCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteSAPProveedorCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteSAPProveedorCommandHandler : IRequestHandler<DeleteSAPProveedorCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteSAPProveedorCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteSAPProveedorCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var proveedor = await _migracionSAPParteTecnicaContext.SAP_Proveedores
                .FirstOrDefaultAsync(c => c.Codigo_IN2 == request.Id, cancellationToken);

            if (proveedor == null)
            {
                result.Errors.Add("No se ha encontrado el proveedor a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(proveedor);

            proveedor.Borrado = true;

            _migracionSAPParteTecnicaContext.SAP_Proveedores.Update(proveedor);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "SAP_Proveedores",
                IdRegistro = proveedor.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica del proveedor: Id={proveedor.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteSAPProveedorCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}