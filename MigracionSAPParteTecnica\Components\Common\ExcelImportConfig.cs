﻿namespace MigracionSAPParteTecnica.Components.Common;

public class ExcelImportConfig
{
    public static readonly ExcelConfig TiposCaracteristicas = new()
    {
        NombreArchivo = "TiposCaracteristicas",
        TituloHoja = "Tipos",
        TituloVisible = "Tipos de característica",
        Columnas = new() { "CodTipo", "Tipo", "Descripcion" },
        UploadUrl = "api/Upload/TiposCaracteristicas",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig ValoresTiposCaracteristicas = new()
    {
        NombreArchivo = "ValoresTiposCaracteristicas",
        TituloHoja = "Valores",
        TituloVisible = "VALORES DE TIPOS DE CARACTERÍSTICA",
        Columnas = new() { "CodTipo", "Caracteristica", "Valor", "TextoBreveValor" },
        UploadUrl = "api/Upload/ValoresTiposCaracteristicas",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig MetodosInspeccion = new()
    {
        NombreArchivo = "MetodosInspeccion",
        TituloHoja = "Metodos",
        TituloVisible = "MÉTODOS DE INSPECCIÓN",
        Columnas = new() { "Nombre", "Tipo", "Descripción", "TextoBreve" },
        UploadUrl = "api/Upload/MetodosInspeccion",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig CaracteristicasInspeccion = new()
    {
        NombreArchivo = "CaracteristicasInspeccion",
        TituloHoja = "Caracteristicas",
        TituloVisible = "CARACTERÍSTICAS DE INSPECCIÓN",
        Columnas = new() { "Nombre", "Tipo", "Descripcion", "TextoBreve" },
        UploadUrl = "api/Upload/CaracteristicasInspeccion",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig CodigoEnvases = new()
    {
        NombreArchivo = "CodigoEnvases",
        TituloHoja = "Envases",
        TituloVisible = "CÓDIGOS DE ENVASE",
        Columnas = new() { "Codigo", "PesoNeto" },
        UploadUrl = "api/Upload/CodigoEnvases",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig ExcepcionesPreciosLotes = new()
    {
        NombreArchivo = "ExcepcionesPreciosLotes",
        TituloHoja = "Excepciones",
        TituloVisible = "EXCEPCIONES DE PRECIOS EN LOTES",
        Columnas = new() { "IdBarniz", "Fecha", "Precio" },
        UploadUrl = "api/Upload/ExcepcionesPreciosLotes",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig Familias = new()
    {
        NombreArchivo = "Familias",
        TituloHoja = "Familias",
        TituloVisible = "FAMILIAS DE BARNICES",
        Columnas = new() { "DatoIn2", "DatoSap", "DescripcionSap" },
        UploadUrl = "api/Upload/Familias",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig Naturaleza = new()
    {
        NombreArchivo = "Naturaleza",
        TituloHoja = "Naturaleza",
        TituloVisible = "NATURALEZA DE BARNICES",
        Columnas = new() { "DatoSap", "DatoIn2", "DescripcionSap" },
        UploadUrl = "api/Upload/Naturaleza",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig Nodrizas = new()
    {
        NombreArchivo = "Nodrizas",
        TituloHoja = "Nodrizas",
        TituloVisible = "NODRIZAS DE BARNIZ",
        Columnas = new() { "Fecha", "Nodriza", "IdBarniz", "Lote", "Cantidad" },
        UploadUrl = "api/Upload/Nodrizas",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig NoIncluirRegCompras = new()
    {
        NombreArchivo = "NoIncluirRegCompras",
        TituloHoja = "NoIncluir",
        TituloVisible = "NO INCLUIR EN REGISTRO DE COMPRAS",
        Columnas = new() { "Codigo" },
        UploadUrl = "api/Upload/NoIncluirRegCompras",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig DatosAuxMateriales = new()
    {
        NombreArchivo = "DatosAuxMateriales",
        TituloHoja = "AuxMateriales",
        TituloVisible = "DATOS AUXILIARES DE MATERIALES",
        Columnas = new()
    {
        "TipoMaterial", "Medidas", "EspesorReal", "AnchoReal",
        "LargoReal", "LargoScroll"
    },
        UploadUrl = "api/Upload/DatosAuxMateriales",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig ConsignaNestle = new()
    {
        NombreArchivo = "ConsignaNestle",
        TituloHoja = "ConsignaNestle",
        TituloVisible = "CONSIGNA NESTLÉ",
        Columnas = new() { "IdPaquete" },
        UploadUrl = "api/Upload/ConsignaNestle",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig Envases = new()
    {
        NombreArchivo = "Envases",
        TituloHoja = "Envases",
        TituloVisible = "ENVASES",
        Columnas = new()
    {
        "TipoUso", "Nombre", "Descripcion", "PesoNeto",
        "PesoBruto", "Largo", "Ancho", "Altura"
    },
        UploadUrl = "api/Upload/Envases",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig Colores = new()
    {
        NombreArchivo = "Colores",
        TituloHoja = "Colores",
        TituloVisible = "COLORES DE TINTAS",
        Columnas = new() { "Color", "Nombre" },
        UploadUrl = "api/Upload/Colores",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig CorrespondenciasSkus = new()
    {
        NombreArchivo = "CorrespondenciasSkus",
        TituloHoja = "Skus",
        TituloVisible = "CORRESPONDENCIA DE SKUS",
        Columnas = new()
    {
        "CodigoLitalsa", "CodigoProveedor", "Pl", "PesoNeto", "Consigna"
    },
        UploadUrl = "api/Upload/CorrespondenciasSkus",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };

    public static readonly ExcelConfig RelacionCompuestas = new()
    {
        NombreArchivo = "RelacionCompuestas",
        TituloHoja = "Relaciones",
        TituloVisible = "RELACIÓN DE COMPUESTAS",
        Columnas = new() { "Ingrediente", "RefIn2" },
        UploadUrl = "api/Upload/RelacionCompuestas",
        ExportUrl = "api/PlantillaExcel/Descargar"
    };
}

public class ExcelConfig
{
    public string NombreArchivo { get; set; } = string.Empty;
    public string TituloHoja { get; set; } = string.Empty;
    public string TituloVisible { get; set; } = string.Empty;
    public List<string> Columnas { get; set; } = new();
    public string UploadUrl { get; set; } = string.Empty;
    public string ExportUrl { get; set; } = string.Empty;
}