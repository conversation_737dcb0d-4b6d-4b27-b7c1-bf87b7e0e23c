﻿using MigracionSAPParteTecnica.DTO.MM.General;

namespace MigracionSAPParteTecnica.Validators.General;

public static class EnvasesValidator
{
    public static string? Validar(EnvasesDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Nombre))
            return "El nombre no puede estar vacío.";

        if (string.IsNullOrWhiteSpace(dto.TipoUso))
            return "El tipo de uso no puede estar vacío.";

        if (string.IsNullOrWhiteSpace(dto.Descripcion))
            return "La descripción no puede estar vacía.";

        if (dto.PesoNeto == 0)
            return "El peso neto no puede ser 0.";

        if (dto.PesoBruto == 0)
            return "El peso bruto no puede ser 0.";

        if (dto.Largo == 0)
            return "El largo no puede ser 0.";

        if (dto.Ancho == 0)
            return "El ancho no puede ser 0.";

        if (dto.Altura == 0)
            return "La altura no puede ser 0.";

        return null;
    }
}