﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Command;
public class DeleteSAPClienteCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteSAPClienteCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteSAPClienteCommandHandler : IRequestHandler<DeleteSAPClienteCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteSAPClienteCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteSAPClienteCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var cliente = await _migracionSAPParteTecnicaContext.SAP_Clientes
                .FirstOrDefaultAsync(c => c.Codigo_IN2 == request.Id, cancellationToken);

            if (cliente == null)
            {
                result.Errors.Add("No se ha encontrado el cliente a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(cliente);

            cliente.Borrado = true;

            _migracionSAPParteTecnicaContext.SAP_Clientes.Update(cliente);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            var datosDespues = JsonSerializer.Serialize(cliente);

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "SAP_Clientes",
                IdRegistro = cliente.Id, // Usa el int correcto de tu entidad, puede ser cliente.Id o cliente.Codigo_IN2
                DatosAntes = datosAntes,
                DatosDespues = datosDespues,
                Comentarios = $"Eliminación lógica del cliente: Id={request.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteSAPClienteCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}