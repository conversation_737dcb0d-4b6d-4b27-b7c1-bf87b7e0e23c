﻿using System.ComponentModel;

namespace MigracionSAPParteTecnica;
public class Enums
{
    public enum TiposElemento
    {
        [Description("Cuerpo Agrafado")]
        CAG,
        [Description("Cuerpo 2 Piezas Embutición")]
        C2E,
        [Description("Cuerpo 3 Piezas Soldado")]
        C3S,
        [Description("Tapa Básica - Fondo OT")]
        TBA,
        [Description("Tapa con Reservas Asas")]
        TAS,
        [Description("Tapa Termosellable (peel off)")]
        TTE,
        [Description("Tapa Fácil Apertura EOE")]
        TFA,
        [Description("Tapa Alta Embutición")]
        TEM,
        [Description("Tapa Tapón Corona")]
        TCO,
        [Description("Tapa Muselet")]
        TMU,
        [Description("Tapa Cápsula Ropp")]
        TRP,
        [Description("Tapa Twist Off")]
        TTW
    }

    public enum SentidosLectura
    {
        [Description("Normal")]
        A,
        [Description("Invertido")]
        B,
        [Description("Ver plano")]
        C,
        [Description("Cabeza con cabeza")]
        Ai,
        [Description("Pie con pie")]
        Bi,
        [Description("Ver plano")]
        Ci,
        [Description("Ver plano")]
        Di
    }

    public enum PosicionesEscuadraExt
    {
        [Description("Lado derecho")]
        Derecha = 2,

        [Description("Lado izquierdo")]
        Izquierda = 3
    }

    public enum TipoMaterial
    {
        Paquete,
        Bobina
    }

    public enum TiposUsos
    {
        Tintas,
        Barnices
    }
}