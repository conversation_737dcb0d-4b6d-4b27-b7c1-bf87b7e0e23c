﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editPaqueteConsignaNestle?.Id > 0)
{
    <EntityLockManager EntityType="ConsignaNestle"
                       EntityId="editPaqueteConsignaNestle.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Paquete consigna Nestlé"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este paquete de consigna de Nestlé está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editPaqueteConsignaNestle" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editPaqueteConsignaNestle" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código paquete:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12" ReadOnly="@_lectura">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/paquete-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxTextBox @bind-Text="editPaqueteConsignaNestle.CodigoPaquete" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<ConsignaNestleDTO> OnSave { get; set; }
    private ConsignaNestleDTO editPaqueteConsignaNestle = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(ConsignaNestleDTO consignaNestleDTO)
    {
        editPaqueteConsignaNestle = consignaNestleDTO;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = ConsignaNestleValidator.Validar(editPaqueteConsignaNestle);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editPaqueteConsignaNestle);
        IsPopupVisible = false;
        ToastService.MostrarOk("Paquete de consigna Nestlé guardado correctamente.");
    }
}