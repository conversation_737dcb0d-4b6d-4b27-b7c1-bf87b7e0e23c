﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Query;

public class GetAllSAPClientesQuery : IRequest<ListResult<SAPClienteDTO>>
{
}

internal class GetAllSAPClientesQueryHandler : IRequestHandler<GetAllSAPClientesQuery, ListResult<SAPClienteDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllSAPClientesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<SAPClienteDTO>> Handle(GetAllSAPClientesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<SAPClienteDTO>
        {
            Data = new List<SAPClienteDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listClientes = await _migracionSAPParteTecnicaContext.SAP_Clientes
                .AsNoTracking()
                .Include(d => d.Destinatarios)
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<SAPClienteDTO>>(listClientes).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllSAPClientes - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}