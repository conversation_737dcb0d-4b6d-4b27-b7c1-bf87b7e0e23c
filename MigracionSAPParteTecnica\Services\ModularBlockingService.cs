﻿namespace MigracionSAPParteTecnica.Services;

public class ModularBlockingService
{
    public class BlockingState
    {
        public bool IsBlocked { get; set; }
        public string? Usuario { get; set; }
        public DateTime? HoraBloqueo { get; set; }
        public string? Motivo { get; set; }
    }

    private readonly Dictionary<string, BlockingState> _moduleStates = new();
    public event Action<string, BlockingState>? OnModuleStateChanged;

    public BlockingState GetState(string module)
        => _moduleStates.TryGetValue(module, out var state) ? state : new BlockingState { IsBlocked = false };

    public void Block(string module, string usuario, string? motivo = null)
    {
        var state = new BlockingState
        {
            IsBlocked = true,
            Usuario = usuario,
            HoraBloqueo = DateTime.Now,
            Motivo = motivo
        };
        _moduleStates[module] = state;
        OnModuleStateChanged?.Invoke(module, state);
    }

    public void Unblock(string module)
    {
        var state = new BlockingState { IsBlocked = false };
        _moduleStates[module] = state;
        OnModuleStateChanged?.Invoke(module, state);
    }
}