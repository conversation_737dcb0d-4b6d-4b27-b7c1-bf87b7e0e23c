﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;
public class ActualizarImposicionesCommand : IRequest<Result> { }


internal class ActualizarImposicionesCommandHandler : IRequestHandler<ActualizarImposicionesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ModularBlockingService _blockingService;
    private readonly IToastService? _toastService;

    public ActualizarImposicionesCommandHandler(
        MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext,
        IAuditoriaService auditoriaService,
        IHttpContextAccessor httpContextAccessor,
        ModularBlockingService blockingService,
        IToastService? toastService = null
    )
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _blockingService = blockingService;
        _toastService = toastService;
    }

    private List<string> ExpandirPlanos(string? planoEjemplo)
    {
        var planosRaw = planoEjemplo?.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries) ?? Array.Empty<string>();
        var planos = new List<string>();
        if (planosRaw.Length > 0)
        {
            planos.Add(planosRaw[0]);
            for (int i = 1; i < planosRaw.Length; i++)
            {
                var sufijo = planosRaw[i];
                var basePlano = planosRaw[0];
                var idxGuion = basePlano.LastIndexOf('-');
                if (idxGuion != -1 && sufijo.All(char.IsDigit))
                    planos.Add(basePlano.Substring(0, idxGuion + 1) + sufijo);
                else
                    planos.Add(sufijo);
            }
        }
        return planos;
    }

    public async Task<Result> Handle(ActualizarImposicionesCommand request, CancellationToken cancellationToken)
    {
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var modulo = "ConfiguradorVariantes";
        var result = new Result();
        int insertados = 0, actualizados = 0;

        _blockingService.Block(modulo, usuario, "Actualización de imposiciones en curso");
        try
        {
            // 1. Motivos con datos válidos
            var motivos = await _migracionSAPParteTecnicaContext.Maestro
                .Where(m => m.AnchoHoja.HasValue && m.LargoHoja.HasValue && !string.IsNullOrEmpty(m.Plano))
                .ToListAsync(cancellationToken);

            // 2. Expandir cada motivo en todos sus planos
            var motivosExpandido = motivos
                .SelectMany(m =>
                    ExpandirPlanos(m.Plano).Select(planoUnit => new
                    {
                        m.Cliente_SAP,
                        m.Cliente_IN2,
                        Plano = planoUnit,
                        m.AnchoHoja,
                        m.LargoHoja,
                        m.LargoScroll,
                        m.NumeroCuerpos,
                        m.Tipo,
                        m.PedidoEjemplo,
                        // para matching de troquel:
                        m.Producto,
                        m.DiametroReal,
                        m.AlturaElemento,
                        m.Desarrollo,
                        m.ResIzq,
                        m.ResDcha,
                        m.ResInf,
                        m.ResSup,
                        m.Embuticion,
                        m.IdFormato
                    })
                ).ToList();

            // 3. Buscar todos los troqueles posibles en memoria (pocos registros)
            var troqueles = await _migracionSAPParteTecnicaContext.Troqueles.AsNoTracking().ToListAsync(cancellationToken);

            // 4. Procesar cada motivo expandido agrupado por la clave única de imposición
            var grupos = motivosExpandido.GroupBy(m => new
            {
                m.Cliente_SAP,
                m.Cliente_IN2,
                m.Plano,
                m.AnchoHoja,
                m.LargoHoja,
                m.LargoScroll,
                m.NumeroCuerpos,
                m.Producto,
                m.DiametroReal,
                m.AlturaElemento,
                m.Desarrollo,
                m.ResIzq,
                m.ResDcha,
                m.ResInf,
                m.ResSup,
                m.Embuticion,
                m.IdFormato
            });

            foreach (var grupo in grupos)
            {
                var motivo = grupo.First();

                // Buscar el troquel por clave (sin tener en cuenta el plano)
                var troquel = troqueles.FirstOrDefault(t =>
                    t.Cliente_IN2_Origen == grupo.Key.Cliente_IN2 &&
                    t.Cliente_SAP_Origen == grupo.Key.Cliente_SAP &&
                    t.Producto_Origen == grupo.Key.Producto &&
                    t.DiametroReal_Origen == grupo.Key.DiametroReal &&
                    t.AlturaElemento_Origen == grupo.Key.AlturaElemento &&
                    t.Desarrollo_Origen == grupo.Key.Desarrollo &&
                    t.ResIzq_Origen == grupo.Key.ResIzq &&
                    t.ResDcha_Origen == grupo.Key.ResDcha &&
                    t.ResInf_Origen == grupo.Key.ResInf &&
                    t.ResSup_Origen == grupo.Key.ResSup &&
                    t.Embuticion_Origen == grupo.Key.Embuticion &&
                    t.IdFormato_Origen == grupo.Key.IdFormato
                );

                if (troquel == null)
                {
                    _toastService?.MostrarInfo($"No hay troquel para el motivo {grupo.Key.Cliente_IN2} {grupo.Key.Plano}");
                    continue;
                }

                int idTroquel = troquel.Id;

                // Buscar imposición existente SOLO automática
                var imposicionExistente = await _migracionSAPParteTecnicaContext.Imposiciones.FirstOrDefaultAsync(i =>
                    i.Cliente_SAP_Origen == grupo.Key.Cliente_SAP &&
                    i.Cliente_IN2_Origen == grupo.Key.Cliente_IN2 &&
                    i.Plano_Origen == grupo.Key.Plano &&
                    i.AnchoHoja_Origen == grupo.Key.AnchoHoja &&
                    i.LargoHoja_Origen == grupo.Key.LargoHoja &&
                    i.LargoScroll_Origen == grupo.Key.LargoScroll &&
                    i.NumeroCuerpos_Origen == grupo.Key.NumeroCuerpos &&
                    i.IdTroquel_Origen == idTroquel &&
                    i.EsAutomatico == true, // SOLO actualizar las automáticas
                    cancellationToken
                );

                var tipoFinal = grupo.Select(x => x.Tipo).Distinct().Count() > 1 ? "MIXTO" : grupo.First().Tipo;
                var pedidoEjemplo = grupo.Max(x => x.PedidoEjemplo ?? 0);

                if (imposicionExistente == null)
                {
                    // Insertar como automática
                    var nueva = new Entities.MigracionSAPParteTecnica.CV.Imposiciones
                    {
                        Cliente_SAP = grupo.Key.Cliente_SAP,
                        Cliente_IN2 = grupo.Key.Cliente_IN2,
                        Plano = grupo.Key.Plano,
                        AnchoHoja = grupo.Key.AnchoHoja,
                        LargoHoja = grupo.Key.LargoHoja,
                        LargoScroll = grupo.Key.LargoScroll,
                        IdTroquel = idTroquel,
                        Tipo = tipoFinal,
                        PedidoEjemplo = pedidoEjemplo,
                        Cuerpos = grupo.Key.NumeroCuerpos,
                        FechaRegistro = DateTime.Now,
                        Activo = true,
                        Cliente_SAP_Origen = grupo.Key.Cliente_SAP,
                        Cliente_IN2_Origen = grupo.Key.Cliente_IN2,
                        Plano_Origen = grupo.Key.Plano,
                        AnchoHoja_Origen = grupo.Key.AnchoHoja,
                        LargoHoja_Origen = grupo.Key.LargoHoja,
                        LargoScroll_Origen = grupo.Key.LargoScroll,
                        NumeroCuerpos_Origen = grupo.Key.NumeroCuerpos,
                        IdTroquel_Origen = idTroquel,
                        EsAutomatico = true
                    };
                    _migracionSAPParteTecnicaContext.Imposiciones.Add(nueva);
                    insertados++;
                }
                else
                {
                    bool cambiado = false;
                    if (imposicionExistente.Tipo != tipoFinal)
                    {
                        imposicionExistente.Tipo = tipoFinal;
                        cambiado = true;
                    }
                    if ((imposicionExistente.PedidoEjemplo ?? 0) < pedidoEjemplo)
                    {
                        imposicionExistente.PedidoEjemplo = pedidoEjemplo;
                        cambiado = true;
                    }
                    if (cambiado)
                        actualizados++;
                }
            }

            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "MASS UPDATE",
                Tabla = "CV_Imposiciones",
                IdRegistro = 0,
                DatosAntes = null,
                DatosDespues = null,
                Comentarios = $"Actualización masiva de imposiciones. Insertados: {insertados}, Actualizados: {actualizados}"
            }, cancellationToken);

            result.Success = true;
            result.Message = $"Imposiciones procesadas correctamente. Insertadas: {insertados}, Actualizados: {actualizados}";
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = "Error durante la actualización: " + ex.Message;
        }
        finally
        {
            _blockingService.Unblock(modulo);
        }

        return result;
    }
}