﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;
public class TratarDatosAuxMaterialesCommand : IRequest<SingleResult<DatosAuxMaterialesDTO>>
{
    public DatosAuxMaterialesDTO DatosAuxMaterialesDTO { get; set; }

    public TratarDatosAuxMaterialesCommand(DatosAuxMaterialesDTO datosAuxMaterialesDTO)
    {
        DatosAuxMaterialesDTO = datosAuxMaterialesDTO;
    }
}

internal class TratarDatosAuxMaterialesCommandHandler : IRequestHandler<TratarDatosAuxMaterialesCommand, SingleResult<DatosAuxMaterialesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarDatosAuxMaterialesCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<DatosAuxMaterialesDTO>> Handle(TratarDatosAuxMaterialesCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<DatosAuxMaterialesDTO>
        {
            Data = new DatosAuxMaterialesDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.DatosAuxMaterialesDTO;

            var existing = await _migracionSAPParteTecnicaContext.DatosAuxMateriales
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.BobinasPaquetes.DatosAuxMateriales>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.DatosAuxMateriales.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<DatosAuxMaterialesDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_BobinasPaquetes_DatosAuxMateriales",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de dato auxiliar: Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.DatosAuxMateriales.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<DatosAuxMaterialesDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_BobinasPaquetes_DatosAuxMateriales",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de dato auxiliar: Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarDatosAuxMaterialesCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}