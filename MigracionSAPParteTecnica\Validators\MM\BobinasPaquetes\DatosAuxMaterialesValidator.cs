﻿using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using System.Globalization;
using System.Text.RegularExpressions;

namespace MigracionSAPParteTecnica.Validators.MM.BobinasPaquetes;
public static class DatosAuxMaterialesValidator
{
    public static string? Validar(DatosAuxMaterialesDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.TipoMaterial))
            return "Debe indicar el tipo de material.";

        if (dto.EspesorReal == 0)
            return "El espesor real no puede ser 0.";

        if (dto.AnchoReal == 0)
            return "El ancho real no puede ser 0.";

        if (dto.TipoMaterial.ToLower() == "paquete")
        {
            if (dto.LargoReal == null || dto.LargoReal == 0)
                return "El largo real no puede ser nulo ni 0 para materiales tipo 'paquete'.";
        }

        if (string.IsNullOrWhiteSpace(dto.Medidas))
            return "Debe indicar las medidas.";

        var medidas = dto.Medidas.Replace(',', '.').Trim();

        if (dto.TipoMaterial.ToLower() == "paquete")
        {
            if (dto.LargoReal == null || dto.LargoReal == 0)
                return "El largo real no puede ser nulo ni 0 para materiales tipo 'paquete'.";

            // Formato: decimal x decimal x decimal espacio R|S
            var regexPaquete = new Regex(@"^\d+(\.\d+)?x\d+(\.\d+)?x\d+(\.\d+)? [RS]$", RegexOptions.IgnoreCase);
            if (!regexPaquete.IsMatch(medidas))
                return "El formato de medidas para 'paquete' debe ser 'espesorxanchoxlargo T', por ejemplo: '0.13x900x1500 R'.";
        }
        else if (dto.TipoMaterial.ToLower() == "bobina")
        {
            // Formato: decimal x decimal
            var regexBobina = new Regex(@"^\d+(\.\d+)?x\d+(\.\d+)?$", RegexOptions.IgnoreCase);
            if (!regexBobina.IsMatch(medidas))
                return "El formato de medidas para 'bobina' debe ser 'espesorxancho', por ejemplo: '0.13x900'.";
        }

        return null;
    }
}