﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Query;

public class GetAllConsignaNestleQuery : IRequest<ListResult<ConsignaNestleDTO>>
{
}

internal class GetAllConsignaNestleQueryHandler : IRequestHandler<GetAllConsignaNestleQuery, ListResult<ConsignaNestleDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllConsignaNestleQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ConsignaNestleDTO>> Handle(GetAllConsignaNestleQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ConsignaNestleDTO>
        {
            Data = new List<ConsignaNestleDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listConsignaNestle = await _migracionSAPParteTecnicaContext.ConsignaNestle
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ConsignaNestleDTO>>(listConsignaNestle).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllConsignaNestleQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}