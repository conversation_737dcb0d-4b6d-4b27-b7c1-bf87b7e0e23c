﻿@inject IJSRuntime JSRuntime

<DxToolbar>
    <!-- Botón de exportar plantilla -->
    <DxToolbarItem Text="@TextoExportar"
                   IconCssClass="bi bi-file-earmark-excel"
                   RenderStyle="ButtonRenderStyle.Secondary"
                   CssClass="ms-3 rounded"
                   Alignment="ToolbarItemAlignment.Right"
                   Click="DescargarPlantilla" />

    <!-- Botón de importar desde Excel -->
    <DxToolbarItem Text="@TextoImportar"
                   IconCssClass="bi bi-upload"
                   RenderStyle="ButtonRenderStyle.Secondary"
                   CssClass="ms-3 rounded"
                   Alignment="ToolbarItemAlignment.Right"
                   Click="@AbrirPopup" />
</DxToolbar>

<PopupImportarExcel @ref="Popup"
                    @bind-IsVisible="PopupVisible"
                    ImportEndpoint="@ImportEndpoint"
                    OnArchivoSubido="ArchivoSubido"
                    OnImportacionFinalizada="OnImportacionFinalizada" />

@code {
    [Parameter] public string TextoExportar { get; set; } = "Descargar Plantilla";
    [Parameter] public string TextoImportar { get; set; } = "Importar Plantilla";

    [Parameter] public ExcelConfig Config { get; set; } = new();
    [Parameter] public EventCallback<string> OnArchivoSubido { get; set; }

    [Parameter] public string ImportEndpoint { get; set; } = string.Empty;
    [Parameter] public EventCallback<string> OnImportacionFinalizada { get; set; }

    private bool PopupVisible;
    private PopupImportarExcel? Popup;

    private void AbrirPopup()
    {
        PopupVisible = true;
    }

    private async Task DescargarPlantilla()
    {
        try
        {
            var fullUrl = ExcelDownloadHelper.GenerarUrlDescarga(
                Config.ExportUrl,
                Config.NombreArchivo,
                Config.TituloHoja,
                Config.TituloVisible,
                Config.Columnas
            );

            await JSRuntime.InvokeVoidAsync("downloadFileFromUrl", fullUrl);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generando URL de descarga: {ex.Message}");
        }
    }

    private async Task ArchivoSubido(string nombre)
    {
        if (OnArchivoSubido.HasDelegate)
            await OnArchivoSubido.InvokeAsync(nombre);
    }
}