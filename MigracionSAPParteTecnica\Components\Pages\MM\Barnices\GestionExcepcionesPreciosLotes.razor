﻿@page "/GestionExcepcionesPreciosLotes"
@attribute [Authorize(Roles = "materiales, admin, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var excepcionesPrecio = (ExcepcionesPreciosLotesDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(excepcionesPrecio)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="CodigoDescripcion" Caption="Barniz" />
        <DxGridDataColumn FieldName="Fecha" Caption="Fecha" />
        <DxGridDataColumn FieldName="Precio" Caption="Precio" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.ExcepcionesPreciosLotes"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/ExcepcionesPreciosLotes"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditExcepcionPrecioLotePopUp @ref="editExcepcionPrecioLote" OnSave="GuardarCambios" MaterialesList="MaterialesList" MaterialSeleccionado="MaterialSeleccionado" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<ExcepcionesPreciosLotesDTO> Data = new();
    string GridSearchText = "";
    private EditExcepcionPrecioLotePopUp? editExcepcionPrecioLote;
    private ExcepcionesPreciosLotesDTO? selectedExcepcionPrecioLote { get; set; }
    private List<MaterialComboItem> MaterialesList = new();
    private MaterialComboItem? MaterialSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMateriales();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (ExcepcionesPreciosLotesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllExcepcionesPrecios();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<ExcepcionesPreciosLotesDTO>(result.Data);

            foreach (var item in Data)
            {
                var material = MaterialesList.FirstOrDefault(m => m.Codigo == item.CodigoBarniz);
                item.CodigoDescripcion = material?.Display ?? item.CodigoBarniz.ToString();
            }

            StateHasChanged();
        }
    }

    private async Task CargarMateriales()
    {
        var result = await MigracionSAPParteTecnicaService.GetMaterialesByTipo("BARNIZ");
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MaterialesList = result.Data.Select(m => new MaterialComboItem
            {
                Codigo = m.Codigo,
                Nombre = m.Descripcion ?? ""
            }).ToList();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedExcepcionPrecioLote = dataItem as ExcepcionesPreciosLotesDTO ?? new ExcepcionesPreciosLotesDTO();

        MaterialSeleccionado = MaterialesList
         .FirstOrDefault(c => c.Codigo == selectedExcepcionPrecioLote.CodigoBarniz);

        editExcepcionPrecioLote?.AbrirPopUp(selectedExcepcionPrecioLote);
    }

    async Task GuardarCambios(ExcepcionesPreciosLotesDTO updatedExcepcionPrecioLote)
    {
        _isLoading = true;
        selectedExcepcionPrecioLote = updatedExcepcionPrecioLote;

        var result = await MigracionSAPParteTecnicaService.TratarExcepcionesPreciosLotes(selectedExcepcionPrecioLote);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (ExcepcionesPreciosLotesDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteExcepcionPrecioLote(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Excepción de precio eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}