﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "MigracionSAPParteTecnicaContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\MigracionSAPParteTecnica",
   "OutputPath": "Entities\\MigracionSAPParteTecnica",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "MigracionSAPParteTecnica",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[Auditorias]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_Imposiciones]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_Maestro]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_Motivos]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_Plano]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_TiposDeElemento]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CV_Troqueles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EntityLocks]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_CodigoEnvases]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_ExcepcionesPreciosLotes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_Familias]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_Naturaleza]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_Nodrizas]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Barnices_NoIncluirRegCompras]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_BobinasPaquetes_ConsignaNestle]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_BobinasPaquetes_DatosAuxMateriales]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Envases]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Materiales]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Tintas_Colores]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Tintas_CorrespondenciasSKUS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MM_Tintas_RelacionCompuestas]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[QM_CaracteristicasInspeccion]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[QM_MetodosInspeccion]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[QM_TiposCaracteristicas]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[QM_ValoresTiposCaracteristicas]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SAP_Clientes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SAP_Clientes_Destinatarios]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SAP_Proveedores]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}