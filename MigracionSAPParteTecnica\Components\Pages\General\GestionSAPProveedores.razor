﻿@page "/GestionSAPProveedores"
@attribute [Authorize(Roles = "admin,almacen,logistica,preprint, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="true"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var proveedor = (SAPProveedorDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(proveedor)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false" />
        <DxGridDataColumn FieldName="Codigo_IN2" Caption="Código IN2" />
        <DxGridDataColumn FieldName="Nombre_IN2" Caption="Nombre IN2" />
        <DxGridDataColumn FieldName="Codigo_BP" Caption="Código Business Partner">
            <CellDisplayTemplate Context="context">
                @{
                    var destinatario = ((SAPProveedorDTO)context.DataItem)?.Codigo_BP;
                }
                <span>@(destinatario?.ToString() ?? "")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Codigo_SAP" Caption="Código SAP" />
        <DxGridDataColumn FieldName="Nombre_SAP" Caption="Nombre SAP" />
        <DxGridDataColumn FieldName="OrgCompras3410" Caption="Organización 3410" />
        <DxGridDataColumn FieldName="OrgCompras3411" Caption="Organización 3411" />
        <DxGridDataColumn FieldName="Clave_Idioma" Caption="Clave del idioma" />
    </Columns>
</DxGrid>
<EditSAPProveedorPopUp @ref="editSAPProveedorPopUp" OnSave="GuardarCambios" />

@code{
    bool _isLoading;
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<SAPProveedorDTO> Data = new();
    string GridSearchText = "";
    private EditSAPProveedorPopUp? editSAPProveedorPopUp;
    private SAPProveedorDTO? selectedSAPProveedor { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllSAPProveedores();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<SAPProveedorDTO>(result.Data);
            StateHasChanged();
        }
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (SAPProveedorDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    void AbrirPopUp(object dataItem)
    {
        selectedSAPProveedor = dataItem as SAPProveedorDTO ?? new SAPProveedorDTO();
        editSAPProveedorPopUp?.AbrirPopUp(selectedSAPProveedor);
    }

    async Task GuardarCambios(SAPProveedorDTO updatedSAPProveedor)
    {
        _isLoading = true;
        selectedSAPProveedor = updatedSAPProveedor;

        var result = await MigracionSAPParteTecnicaService.TratarSAPProveedor(selectedSAPProveedor);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (SAPProveedorDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteSAPProveedor(item.Codigo_IN2);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Proveedor eliminado correctamente.");
        }

        _isLoading = false;
    }
}