﻿@page "/GestionSAPDestinatarios"
@attribute [Authorize(Roles = "admin,almacen,logistica,preprint, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var destinatarios = (SAPClienteDestinatarioDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(destinatarios)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false" />
        <DxGridDataColumn FieldName="Codigo_Destinatario_SAP" Caption="Código destinatario">
            <CellDisplayTemplate Context="context">
                @{
                    var destinatario = ((SAPClienteDestinatarioDTO)context.DataItem)?.Codigo_Destinatario_SAP;
                }
                <span>@(destinatario?.ToString() ?? "")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Codigo_IN2" Caption="Código IN2" />
        <DxGridDataColumn FieldName="Nombre" Caption="Nombre" />
        <DxGridDataColumn FieldName="Direccion" Caption="Dirección" />
        <DxGridDataColumn FieldName="Ciudad" Caption="Ciudad" />
        <DxGridDataColumn FieldName="Provincia" Caption="Provincia" />
        <DxGridDataColumn FieldName="Pais" Caption="País" />
        <DxGridDataColumn FieldName="Codigo_Postal" Caption="Código postal" />
    </Columns>
</DxGrid>
<EditSAPClienteDestinatarioPopUp @ref="editDestinatario" OnSave="GuardarCambios" ClientesList="ClientesList" ClienteSeleccionado="ClienteSeleccionado"/>

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<SAPClienteDestinatarioDTO> Data = new();
    string GridSearchText = "";
    private EditSAPClienteDestinatarioPopUp? editDestinatario;
    private SAPClienteDestinatarioDTO? selectedDestinatario { get; set; }
    private List<ClienteComboItem> ClientesList = new();
    private ClienteComboItem? ClienteSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarClientes();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (SAPClienteDestinatarioDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllSAPClientesDestinatarios();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<SAPClienteDestinatarioDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedDestinatario = dataItem as SAPClienteDestinatarioDTO ?? new SAPClienteDestinatarioDTO();

        ClienteSeleccionado = ClientesList
         .FirstOrDefault(c => c.Codigo_IN2 == selectedDestinatario.Codigo_IN2);

        editDestinatario?.AbrirPopUp(selectedDestinatario);
    }

    async Task GuardarCambios(SAPClienteDestinatarioDTO updatedDestinatario)
    {
        _isLoading = true;
        selectedDestinatario = updatedDestinatario;

        var result = await MigracionSAPParteTecnicaService.TratarSAPClienteDestinatario(selectedDestinatario);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    private async Task CargarClientes()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllSAPClientes();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            ClientesList = result.Data.Select(m => new ClienteComboItem
            {
                Codigo_IN2 = m.Codigo_IN2,
                Nombre = m.Nombre ?? ""
            }).ToList();
        }
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (SAPClienteDestinatarioDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteSAPClienteDestinatario(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Destinatario eliminado correctamente.");
        }

        _isLoading = false;
    }
}