﻿using DevExpress.Blazor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPProveedores.Query;
public class GetAllSAPProveedoresQuery : IRequest<ListResult<SAPProveedorDTO>>
{
}

internal class GetAllSAPProveedoresQueryHandler : IRequestHandler<GetAllSAPProveedoresQuery, ListResult<SAPProveedorDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllSAPProveedoresQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<SAPProveedorDTO>> Handle(GetAllSAPProveedoresQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<SAPProveedorDTO>
        {
            Data = new List<SAPProveedorDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listProveedores = await _migracionSAPParteTecnicaContext.SAP_Proveedores
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<SAPProveedorDTO>>(listProveedores).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllSAPProveedores - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}