﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;
public class ImportarNoIncluirRegComprasCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarNoIncluirRegComprasCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarNoIncluirRegComprasCommandHandler : IRequestHandler<ImportarNoIncluirRegComprasCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarNoIncluirRegComprasCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarNoIncluirRegComprasCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new NoIncluirRegComprasDTO
                    {
                        CodigoBarniz = worksheet.Cell(filaActual, 2).GetValue<int>(),
                        Borrado = false
                    };

                    if (dto.CodigoBarniz <= 0)
                    {
                        errores.Add($"Fila {filaActual}: El código debe ser mayor que cero.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.NoIncluirRegCompras
                        .FirstOrDefaultAsync(x => x.CodigoBarniz == dto.CodigoBarniz, cancellationToken);

                    var idAuditoria = dto.CodigoBarniz.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.NoIncluirRegCompras>(dto);
                        await _context.NoIncluirRegCompras.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "MM_Barnices_NoIncluirRegCompras",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var borradoAnterior = existente.Borrado;

                        TinyMapper.Map(dto, existente);

                        if (borradoAnterior != existente.Borrado)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_NoIncluirRegCompras",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Estado borrado actualizado: {borradoAnterior} → {existente.Borrado}"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar NoIncluirRegCompras: {ex.Message}";
        }

        return result;
    }
}