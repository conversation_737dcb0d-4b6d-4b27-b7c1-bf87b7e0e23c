﻿@page "/GestionNoIncluirRegCompras"

@attribute [Authorize(Roles = "materiales, admin, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var noIncluirRegCompras = (NoIncluirRegComprasDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(noIncluirRegCompras)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="CodigoDescripcion" Caption="Barniz" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.NoIncluirRegCompras"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/NoIncluirRegCompras"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditNoIncluirRegComprasPopUp @ref="editNoIncluirRegCompras" MaterialesList="MaterialesList" MaterialSeleccionado="MaterialSeleccionado" OnSave="GuardarCambios" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<NoIncluirRegComprasDTO> Data = new();
    string GridSearchText = "";
    private EditNoIncluirRegComprasPopUp? editNoIncluirRegCompras;
    private NoIncluirRegComprasDTO? selectedNoIncluirRgistro { get; set; }
    private List<MaterialComboItem> MaterialesList = new();
    private MaterialComboItem? MaterialSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMateriales();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (NoIncluirRegComprasDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllNoIncluirRegCompras();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<NoIncluirRegComprasDTO>(result.Data);

            foreach (var item in Data)
            {
                var material = MaterialesList.FirstOrDefault(m => m.Codigo == item.CodigoBarniz);
                item.CodigoDescripcion = material?.Display ?? item.CodigoBarniz.ToString();
            }

            StateHasChanged();
        }
    }

    private async Task CargarMateriales()
    {
        var result = await MigracionSAPParteTecnicaService.GetMaterialesByTipo("BARNIZ");
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MaterialesList = result.Data.Select(m => new MaterialComboItem
            {
                Codigo = m.Codigo,
                Nombre = m.Descripcion ?? ""
            }).ToList();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedNoIncluirRgistro = dataItem as NoIncluirRegComprasDTO ?? new NoIncluirRegComprasDTO();

        MaterialSeleccionado = MaterialesList
         .FirstOrDefault(c => c.Codigo == selectedNoIncluirRgistro.CodigoBarniz);

        editNoIncluirRegCompras?.AbrirPopUp(selectedNoIncluirRgistro);
    }

    async Task GuardarCambios(NoIncluirRegComprasDTO updatedNoIncluirRegCompras)
    {
        _isLoading = true;
        selectedNoIncluirRgistro = updatedNoIncluirRegCompras;

        var result = await MigracionSAPParteTecnicaService.TratarNoIncluirRegCompras(selectedNoIncluirRgistro);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (NoIncluirRegComprasDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteNoIncluirRegCompras(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Registro eliminado correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}