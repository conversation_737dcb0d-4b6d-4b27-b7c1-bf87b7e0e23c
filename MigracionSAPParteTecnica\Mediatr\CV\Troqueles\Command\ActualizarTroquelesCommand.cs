﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Command;
public class ActualizarTroquelesCommand : IRequest<Result>
{
}

internal class ActualizarTroquelesCommandHandler : IRequestHandler<ActualizarTroquelesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ModularBlockingService _blockingService;

    public ActualizarTroquelesCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService,
        IHttpContextAccessor httpContextAccessor, ModularBlockingService blockingService)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _blockingService = blockingService;
    }
    public async Task<Result> Handle(ActualizarTroquelesCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        int insertados = 0, actualizados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var modulo = "ConfiguradorVariantes";

        _blockingService.Block(modulo, usuario, "Actualización de troqueles en curso");
        try
        {
            var motivos = await _context.Maestro.AsNoTracking().ToListAsync(cancellationToken);

            var grupos = motivos.GroupBy(m => new
            {
                m.Cliente_IN2,
                m.Cliente_SAP,
                m.Producto,
                m.DiametroReal,
                m.AlturaElemento,
                m.Desarrollo,
                m.ResIzq,
                m.ResDcha,
                m.ResInf,
                m.ResSup,
                m.Embuticion,
                m.IdFormato
            });

            var troqueles = await _context.Troqueles.ToListAsync(cancellationToken);

            foreach (var grupo in grupos)
            {
                var tipos = grupo.Select(m => m.Tipo).Distinct().ToList();
                string tipoFinal = (tipos.Contains("LITOGRAFIA") && tipos.Contains("BARNIZADO")) ? "MIXTO" : tipos.FirstOrDefault();

                var pedidoEjemplo = grupo.Max(m => m.PedidoEjemplo);

                var existente = troqueles.FirstOrDefault(t =>
                    t.Cliente_IN2_Origen == grupo.Key.Cliente_IN2 &&
                    t.Cliente_SAP_Origen == grupo.Key.Cliente_SAP &&
                    t.Producto_Origen == grupo.Key.Producto &&
                    t.DiametroReal_Origen == grupo.Key.DiametroReal &&
                    t.AlturaElemento_Origen == grupo.Key.AlturaElemento &&
                    t.Desarrollo_Origen == grupo.Key.Desarrollo &&
                    t.ResIzq_Origen == grupo.Key.ResIzq &&
                    t.ResDcha_Origen == grupo.Key.ResDcha &&
                    t.ResInf_Origen == grupo.Key.ResInf &&
                    t.ResSup_Origen == grupo.Key.ResSup &&
                    t.Embuticion_Origen == grupo.Key.Embuticion &&
                    t.IdFormato_Origen == grupo.Key.IdFormato
                );

                bool cambiado = false;

                if (existente == null)
                {
                    var nuevo = new Entities.MigracionSAPParteTecnica.CV.Troqueles
                    {
                        Cliente_IN2 = grupo.Key.Cliente_IN2,
                        Cliente_SAP = grupo.Key.Cliente_SAP,
                        Familia = grupo.Key.Producto,
                        DiametroReal = grupo.Key.DiametroReal,
                        AlturaElemento = grupo.Key.AlturaElemento,
                        Desarrollo = grupo.Key.Desarrollo,
                        ReservasIzquierda = grupo.Key.ResIzq,
                        ReservasDerecha = grupo.Key.ResDcha,
                        ReservasInferiores = grupo.Key.ResInf,
                        ReservasSuperiores = grupo.Key.ResSup,
                        Embuticion = grupo.Key.Embuticion,
                        IdFormatoIn2 = grupo.Key.IdFormato,
                        PlanoEjemplo = grupo.Select(x => x.Plano).FirstOrDefault(),
                        Tipo = tipoFinal,
                        PedidoEjemplo = pedidoEjemplo,
                        Activo = true,

                        // Guardar la clave de origen:
                        Cliente_IN2_Origen = grupo.Key.Cliente_IN2,
                        Cliente_SAP_Origen = grupo.Key.Cliente_SAP,
                        Producto_Origen = grupo.Key.Producto,
                        DiametroReal_Origen = grupo.Key.DiametroReal,
                        AlturaElemento_Origen = grupo.Key.AlturaElemento,
                        Desarrollo_Origen = grupo.Key.Desarrollo,
                        ResIzq_Origen = grupo.Key.ResIzq,
                        ResDcha_Origen = grupo.Key.ResDcha,
                        ResInf_Origen = grupo.Key.ResInf,
                        ResSup_Origen = grupo.Key.ResSup,
                        Embuticion_Origen = grupo.Key.Embuticion,
                        IdFormato_Origen = grupo.Key.IdFormato,
                        //Plano_Origen = grupo.Key.Plano,
                        EsAutomatico = true
                    };
                    _context.Troqueles.Add(nuevo);
                    insertados++;
                }
                else
                {
                    if (existente.Tipo != tipoFinal) { existente.Tipo = tipoFinal; cambiado = true; }
                    if (existente.PedidoEjemplo != pedidoEjemplo) { existente.PedidoEjemplo = pedidoEjemplo; cambiado = true; }
                    if (cambiado) actualizados++;
                }
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Auditoría masiva
            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "MASS UPDATE",
                Tabla = "CV_Troqueles",
                IdRegistro = 0,
                DatosAntes = null,
                DatosDespues = null,
                Comentarios = $"Actualización masiva de troqueles. Insertados: {insertados}, Actualizados: {actualizados}"
            }, cancellationToken);

            result.Success = true;
            result.Message = $"Troqueles procesados correctamente. Insertados: {insertados}, Actualizados: {actualizados}";
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al actualizar troqueles: {ex.Message}";
        }
        finally
        {
            _blockingService.Unblock(modulo);
        }

        return result;
    }
}