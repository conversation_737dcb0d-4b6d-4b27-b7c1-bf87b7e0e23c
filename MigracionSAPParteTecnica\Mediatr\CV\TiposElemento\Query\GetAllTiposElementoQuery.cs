﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.Planos.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.TiposElemento.Query;

public class GetAllTiposElementoQuery : IRequest<ListResult<TipoElementoDTO>>
{
}

internal class GetAllTiposElementoQueryHandler : IRequestHandler<GetAllTiposElementoQuery, ListResult<TipoElementoDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllTiposElementoQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<TipoElementoDTO>> Handle(GetAllTiposElementoQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TipoElementoDTO>
        {
            Data = new List<TipoElementoDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listTipos = await _migracionSAPParteTecnicaContext.TiposElemento
                .AsNoTracking()
                .ToListAsync();

            result.Data = TinyMapper.Map<List<TipoElementoDTO>>(listTipos).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllTiposElemento - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}