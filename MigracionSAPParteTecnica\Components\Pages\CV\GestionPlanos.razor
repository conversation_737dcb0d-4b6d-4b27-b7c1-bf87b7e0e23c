﻿@page "/GestionPlanos"
@attribute [Authorize(Roles = "preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="true"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
        CustomizeElement="OnCustomizeGridElement">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopup(new PlanoDTO())"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var plano = (PlanoDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopup(plano)" style="text-decoration: none; padding-right: 15px; color: #c75fff;"></a>
                <a class="bi bi-x-lg bold" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="ClienteNombreConcatenado" Caption="Cliente" />
        <DxGridDataColumn FieldName="NombrePlano" Caption="Nombre del plano" />
        <DxGridDataColumn FieldName="ActivoTexto" Caption="Activo" />
    </Columns>
    <ToolbarTemplate>
        <DxToolbar>
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar planos" CssClass="ms-3 rounded"
                           Alignment="ToolbarItemAlignment.Right" Click="ActualizarPlanos" />
        </DxToolbar>
    </ToolbarTemplate>
</DxGrid>
<EditPlanoPopUp @ref="editPlanoPopUp" OnSave="GuardarCambios"/>

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<PlanoDTO> Data = new();
    string GridSearchText = "";
    public List<DropDownWrapper> ClientesList { get; set; } = new();
    private EditPlanoPopUp? editPlanoPopUp;
    private PlanoDTO? selectedPlano { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarClientes();
        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllPlanos();

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            var clientesResult = await MigracionSAPParteTecnicaService.GetAllSAPClientes();
            if (clientesResult.Errors.Any())
            {
                ToastService.MostrarError(clientesResult.Errors.First());
            }

            var clientesDic = clientesResult.Data.GroupBy(c => c.Codigo_SAP).ToDictionary(g => g.Key, g => g.First().Nombre);

            Data = new ObservableCollection<PlanoDTO>(
                result.Data.Select(p =>
                {
                    var nombreCliente = clientesDic.TryGetValue(p.Cliente, out var nombre) ? nombre : "Desconocido";
                    p.ClienteNombreConcatenado = $"{p.Cliente} - {nombreCliente}";
                    return p;
                })
            );

            StateHasChanged();
        }
    }

    public async Task CargarClientes()
    {
        var resultado = await MigracionSAPParteTecnicaService.GetAllSAPClientes();

        if (resultado != null && resultado.Data != null)
        {
            ClientesList = resultado.Data
                .OrderBy(c => c.Codigo_SAP)
                .Select(c => new DropDownWrapper
                {
                    ID = c.Codigo_SAP,
                    Nombre = $"{c.Codigo_SAP}, {c.Nombre}"
                })
                .ToList();
        }
    }

    private void AbrirPopup(object dataItem)
    {
        selectedPlano = dataItem as PlanoDTO ?? new PlanoDTO();
        editPlanoPopUp?.AbrirPopUp(selectedPlano, ClientesList);
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (PlanoDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (PlanoDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeletePlanoCommand(item.Cliente, item.NombrePlano);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Ausencia eliminada correctamente.");
        }

        _isLoading = false;
    }

    async Task GuardarCambios(PlanoDTO updatedPlano)
    {
        selectedPlano = updatedPlano;
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.TratarPlano(selectedPlano);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    public async Task ActualizarPlanos()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await MigracionSAPParteTecnicaService.ActualizarPlanos();

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
        }

        _isLoading = false;
    }

    void OnCustomizeGridElement(GridCustomizeElementEventArgs e)
    {
        if (e.ElementType != GridElementType.DataRow)
            return;

        var activoObj = e.Grid.GetRowValue(e.VisibleIndex, "Activo");

        if (activoObj is bool activo && !activo)
        {
            e.CssClass = "fila-inactiva";
        }
    }
}