﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;
public class GetTroquelesInactivosQuery : IRequest<ListResult<TroquelesDTO>>
{
}

internal class GetTroquelesInactivosQueryHandler : IRequestHandler<GetTroquelesInactivosQuery, ListResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetTroquelesInactivosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<TroquelesDTO>> Handle(GetTroquelesInactivosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TroquelesDTO>
        {
            Data = new List<TroquelesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listTroqueles = await _migracionSAPParteTecnicaContext.Troqueles
                .AsNoTracking()
                .Where(t => t.Activo == false)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<TroquelesDTO>>(listTroqueles).ToList();
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetTroquelesInactivosQuery - {e.InnerException?.Message ?? e.Message}");
        }

        return result;
    }
}