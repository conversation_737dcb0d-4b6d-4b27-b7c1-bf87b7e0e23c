﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
public class GetImposicionesInactivasQuery : IRequest<ListResult<ImposicionesDTO>>
{
}

internal class GetImposicionesInactivasQueryHandler : IRequestHandler<GetImposicionesInactivasQuery, ListResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetImposicionesInactivasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ImposicionesDTO>> Handle(GetImposicionesInactivasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ImposicionesDTO>
        {
            Data = new List<ImposicionesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listImposiciones = await _migracionSAPParteTecnicaContext.Imposiciones
                .AsNoTracking()
                .Where(t => t.Activo == false)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ImposicionesDTO>>(listImposiciones).ToList();
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetImposicionesInactivasQuery - {e.InnerException?.Message ?? e.Message}");
        }

        return result;
    }
}