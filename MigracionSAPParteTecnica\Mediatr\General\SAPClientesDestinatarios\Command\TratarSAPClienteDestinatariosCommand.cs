﻿using DocumentFormat.OpenXml.InkML;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Command;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.General.SAPClientesDestinatarios.Command;

public class TratarSAPClienteDestinatariosCommand : IRequest<SingleResult<SAPClienteDestinatarioDTO>>
{
    public SAPClienteDestinatarioDTO SAPClienteDestinatarioDTO { get; set; }

    public TratarSAPClienteDestinatariosCommand(SAPClienteDestinatarioDTO sAPClienteDestinatarioDTO)
    {
        SAPClienteDestinatarioDTO = sAPClienteDestinatarioDTO;
    }
}

internal class TratarSAPClienteDestinatariosCommandHandler : IRequestHandler<TratarSAPClienteDestinatariosCommand, SingleResult<SAPClienteDestinatarioDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarSAPClienteDestinatariosCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<SAPClienteDestinatarioDTO>> Handle(TratarSAPClienteDestinatariosCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<SAPClienteDestinatarioDTO>
        {
            Data = new SAPClienteDestinatarioDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.SAPClienteDestinatarioDTO;

            var existing = await _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.General.SAPClienteDestinatario>(dto);

                await _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<SAPClienteDestinatarioDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "SAP_Clientes_Destinatarios",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de destinatario: Id = {nuevo.Id} (Código SAP: {nuevo.Codigo_Destinatario_SAP})"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.SAP_Clientes_Destinatarios.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<SAPClienteDestinatarioDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "SAP_Clientes_Destinatarios",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de destinatario: Id = {existing.Id} (Código SAP: {existing.Codigo_Destinatario_SAP})"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarSAPClienteDestinatariosCommand - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}