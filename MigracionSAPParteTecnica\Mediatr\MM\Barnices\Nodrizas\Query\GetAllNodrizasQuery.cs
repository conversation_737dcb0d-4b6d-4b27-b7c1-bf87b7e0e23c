﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Query;

public class GetAllNodrizasQuery : IRequest<ListResult<NodrizasDTO>>
{
}

internal class GetAllNodrizasQueryHandler : IRequestHandler<GetAllNodrizasQuery, ListResult<NodrizasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllNodrizasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<NodrizasDTO>> Handle(GetAllNodrizasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<NodrizasDTO>
        {
            Data = new List<NodrizasDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listNodrizasCodigoEnvases = await _migracionSAPParteTecnicaContext.Nodrizas
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<NodrizasDTO>>(listNodrizasCodigoEnvases).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllNodrizasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}