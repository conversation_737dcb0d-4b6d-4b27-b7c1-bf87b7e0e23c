﻿using KellermanSoftware.CompareNetObjects;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Data.Servin;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.CV.Maestro.Command;
public class ActualizarMaestroCommand : IRequest<Result>
{
}

internal class ActualizarMaestroCommandHandler : IRequestHandler<ActualizarMaestroCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly ServinContext _servinContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ModularBlockingService _blockingService;

    public ActualizarMaestroCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, ServinContext servinContext, IAuditoriaService auditoriaService,
        IHttpContextAccessor httpContextAccessor, ModularBlockingService blockingService)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _servinContext = servinContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _blockingService = blockingService;
    }

    public static void NormalizaStrings(object obj)
    {
        var props = obj.GetType().GetProperties()
            .Where(p => p.PropertyType == typeof(string) && p.CanWrite);

        foreach (var prop in props)
        {
            var val = (string?)prop.GetValue(obj);
            if (val != null && string.IsNullOrWhiteSpace(val))
                prop.SetValue(obj, null);
        }
    }

    public async Task<Result> Handle(ActualizarMaestroCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var ahora = DateTime.Now;
        var modulo = "CV_Maestro";

        _blockingService.Block(modulo, usuario, "Actualización de datos maestros en curso");

        try
        {
            var maestroOrigen = await _servinContext.MotivosBruto
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var maestroDtos = maestroOrigen.Select(m =>
            {
                var dto = TinyMapper.Map<MaestroDTO>(m);
                NormalizaStrings(dto);
                return dto;
            }).ToList();

            var maestroDestino = await _migracionSAPParteTecnicaContext.Maestro.ToListAsync(cancellationToken);

            int insertados = 0;
            int actualizados = 0;

            var compareLogic = new CompareLogic();
            compareLogic.Config.MembersToIgnore.Add("Id");

            foreach (var maestroDto in maestroDtos)
            {
                Entities.MigracionSAPParteTecnica.CV.Maestro? existente = null;

                // ----------- LITOGRAFIA -------------
                if (maestroDto.Tipo == "LITOGRAFIA")
                {
                    existente = maestroDestino.FirstOrDefault(m =>
                        m.Tipo == "LITOGRAFIA" &&
                        m.Motivo == maestroDto.Motivo
                    );
                }
                // ----------- BARNIZADO -------------
                else if (maestroDto.Tipo == "BARNIZADO")
                {
                    if (maestroDto.Cliente_SAP == 30006 || maestroDto.Cliente_SAP == 30004 || maestroDto.Cliente_SAP == 30076)
                    {
                        existente = maestroDestino.FirstOrDefault(m =>
                            m.Tipo == "BARNIZADO" &&
                            m.Cliente_SAP == maestroDto.Cliente_SAP &&
                            m.RefMotivoCliente == maestroDto.RefMotivoCliente
                        );
                    }
                    else
                    {
                        existente = maestroDestino.FirstOrDefault(m =>
                            m.Tipo == "BARNIZADO" &&
                            m.Cliente_SAP != 30006 &&
                            m.Cliente_SAP != 30004 &&
                            m.Cliente_SAP != 30076 &&
                            m.Cliente_IN2 == maestroDto.Cliente_IN2 &&
                            m.RefMotivoCliente == maestroDto.RefMotivoCliente &&
                            m.TipoProducto == maestroDto.TipoProducto &&
                            m.DiametroReal == maestroDto.DiametroReal &&
                            m.IdFormato == maestroDto.IdFormato &&
                            m.Desarrollo == maestroDto.Desarrollo &&
                            m.ResIzq == maestroDto.ResIzq &&
                            m.ResDcha == maestroDto.ResDcha &&
                            m.Embuticion == maestroDto.Embuticion &&
                            m.Tratamiento == maestroDto.Tratamiento
                        );
                    }
                }

                if (existente == null)
                {
                    var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Maestro>(maestroDto);
                    NormalizaStrings(nuevo); // Normalizamos el nuevo antes de añadir

                    _migracionSAPParteTecnicaContext.Maestro.Add(nuevo);
                    insertados++;
                }
                else
                {
                    var entidadParaComparar = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Maestro>(maestroDto);
                    NormalizaStrings(entidadParaComparar);
                    NormalizaStrings(existente); // Por si algún campo existente es string vacío (muy raro, pero 100% seguro)

                    var resultCompare = compareLogic.Compare(entidadParaComparar, existente);
                    if (!resultCompare.AreEqual)
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);

                        Console.WriteLine($"Diferencias para maestro {maestroDto.Motivo} ({maestroDto.Tipo}):");
                        Console.WriteLine(resultCompare.DifferencesString);

                        TinyMapper.Map(maestroDto, existente);
                        NormalizaStrings(existente); // Normaliza después del map, para dejar los campos limpios
                        actualizados++;
                    }
                }
            }

            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = ahora,
                Accion = "MASS UPDATE",
                Tabla = "CV_Maestro",
                IdRegistro = 0,
                DatosAntes = null,
                DatosDespues = null,
                Comentarios = $"Actualización masiva de maestro. Insertados: {insertados}, Actualizados: {actualizados}"
            }, cancellationToken);

            result.Success = true;
            result.Message = $"Maestro procesado correctamente. Insertados: {insertados}, Actualizados: {actualizados}";
        }
        catch (Microsoft.Data.SqlClient.SqlException ex)
        {
            if (ex.Message.Contains("A severe error occurred on the current command", StringComparison.OrdinalIgnoreCase) ||
                ex.Message.Contains("The results, if any, should be discarded", StringComparison.OrdinalIgnoreCase))
            {
                result.Success = false;
                result.Message = "La base de datos está en proceso de replicación/copia. Por favor, prueba de nuevo en unos segundos.";
            }
            else
            {
                result.Success = false;
                result.Message = "Error de base de datos: " + ex.Message;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error inesperado al actualizar maestro: {ex.Message}";
        }
        finally
        {
            _blockingService.Unblock(modulo);
        }

        return result;
    }
}