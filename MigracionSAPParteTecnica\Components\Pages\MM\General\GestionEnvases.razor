﻿@page "/GestionEnvases"

@attribute [Authorize(Roles = "materiales, admin, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var envases = (EnvasesDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(envases)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Nombre" Caption="Nombre" />
        <DxGridDataColumn FieldName="TipoUso" Caption="Tipo de uso" />
        <DxGridDataColumn FieldName="Descripcion" Caption="Descipción" />
        <DxGridDataColumn FieldName="PesoNeto" Caption="Peso Neto" />
        <DxGridDataColumn FieldName="PesoBruto" Caption="Peso Bruto" />
        <DxGridDataColumn FieldName="Largo" Caption="Largo" />
        <DxGridDataColumn FieldName="Ancho" Caption="Ancho" />
        <DxGridDataColumn FieldName="Altura" Caption="Altura" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.Envases"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/Envases"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditEnvasePopUp @ref="editEnvase" OnSave="GuardarCambios" TiposMaterialList="TipoUsoList" />

@code {
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<EnvasesDTO> Data = new();
    string GridSearchText = "";
    private EditEnvasePopUp? editEnvase;
    private EnvasesDTO? selectedEnvase { get; set; }
    private List<DropDownWrapper> TipoUsoList = new();

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        TipoUsoList = Enum.GetValues(typeof(Enums.TiposUsos))
        .Cast<Enums.TiposUsos>()
        .Select(t => new DropDownWrapper
        {
            ID = (int)t,
            Nombre = t.ToString()
        })
        .ToList();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (EnvasesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllEnvases();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<EnvasesDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedEnvase = dataItem as EnvasesDTO ?? new EnvasesDTO();
        editEnvase?.AbrirPopUp(selectedEnvase);
    }

    async Task GuardarCambios(EnvasesDTO updatedEnvase)
    {
        _isLoading = true;
        selectedEnvase = updatedEnvase;

        var result = await MigracionSAPParteTecnicaService.TratarEnvase(selectedEnvase);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (EnvasesDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteEnvase(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Envase eliminado correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}