﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.MM.Tintas;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Query;

public class GetAllRelacionesCompuestasQuery : IRequest<ListResult<RelacionCompuestasDTO>>
{
}

internal class GetAllRelacionesCompuestasQueryHandler : IRequestHandler<GetAllRelacionesCompuestasQuery, ListResult<RelacionCompuestasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllRelacionesCompuestasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<RelacionCompuestasDTO>> Handle(GetAllRelacionesCompuestasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<RelacionCompuestasDTO>
        {
            Data = new List<RelacionCompuestasDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listRelaciones = await _migracionSAPParteTecnicaContext.RelacionCompuestas
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<RelacionCompuestasDTO>>(listRelaciones).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllRelacionesCompuestasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}