﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;

public class DuplicarImposicionCommand : IRequest<SingleResult<ImposicionesDTO>>
{
    public int Id { get; set; }
    public DuplicarImposicionCommand(int id)
    {
        Id = id;
    }
}

public class DuplicarImposicionCommandHandler : IRequestHandler<DuplicarImposicionCommand, SingleResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public DuplicarImposicionCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }
    public async Task<SingleResult<ImposicionesDTO>> Handle(DuplicarImposicionCommand request, CancellationToken cancellationToken)
    {
        var original = await _migracionSAPParteTecnicaContext.Imposiciones
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (original == null)
            return SingleResult<ImposicionesDTO>.Error("Imposición no encontrada.");

        try
        {
            var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Imposiciones>(original);

            nuevo.Id = 0;
            //nuevo.FechaAlta = DateTime.UtcNow;

            _migracionSAPParteTecnicaContext.Imposiciones.Add(nuevo);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            var dto = TinyMapper.Map<ImposicionesDTO>(nuevo);

            return SingleResult<ImposicionesDTO>.Ok(dto);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: DuplicarImposicion - {e.InnerException?.Message ?? e.Message}";
            return SingleResult<ImposicionesDTO>.Error(errorText);
        }
    }
}