﻿using MediatR;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using MigracionSAPParteTecnica.Mediatr.CV.Maestro.Query;
using MigracionSAPParteTecnica.Mediatr.CV.Motivos.Command;
using MigracionSAPParteTecnica.Mediatr.CV.Motivos.Query;
using MigracionSAPParteTecnica.Mediatr.CV.TiposElemento.Command;
using MigracionSAPParteTecnica.Mediatr.CV.TiposElemento.Query;
using MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Command;
using MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;
using MigracionSAPParteTecnica.Mediatr.General.Auditorias.Query;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Command;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientes.Query;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientesDestinatarios.Command;
using MigracionSAPParteTecnica.Mediatr.General.SAPClientesDestinatarios.Query;
using MigracionSAPParteTecnica.Mediatr.General.SAPProveedores.Command;
using MigracionSAPParteTecnica.Mediatr.General.SAPProveedores.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Query;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Query;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Query;
using MigracionSAPParteTecnica.Mediatr.MM.General.DatosMaestros.Query;
using MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
using MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Query;
using MigracionSAPParteTecnica.Mediatr.Planos.Command;
using MigracionSAPParteTecnica.Mediatr.Planos.Query;
using MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;
using MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Query;
using MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Command;
using MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Query;
using MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;
using MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Query;
using MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Command;
using MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Query;

namespace MigracionSAPParteTecnica.Services;
public class MigracionSAPParteTecnicaService(MigracionSAPParteTecnicaContext MigracionSAPParteTecnicaContext, IMediator mediator)
    : IMigracionSAPParteTecnicaService
{
    public async Task<ListResult<TroquelesDTO>> GetAllTroqueles()
    {
        var model = await mediator.Send(new GetAllTroquelesQuery());
        return model;
    }

    public async Task<ListResult<PlanoDTO>> GetAllPlanos()
    {
        var model = await mediator.Send(new GetAllPlanosQuery());
        return model;
    }

    public async Task<SingleResult<bool>> DeletePlanoCommand(int id, string nombrePlano)
    {
        var result = await mediator.Send(new DeletePlanoCommand(id));
        return result;
    }

    public async Task<SingleResult<PlanoDTO>> TratarPlano(PlanoDTO planoDTO)
    {
        var result = await mediator.Send(new TratarPlanoCommand(planoDTO));
        return result;
    }

    public async Task<SingleResult<TroquelesDTO>> GetTroquelById(int id)
    {
        var result = await mediator.Send(new GetTroquelByIdQuery(id));
        return result;
    }

    public async Task<SingleResult<TroquelesDTO>> TratarTroquel(TroquelesDTO troquelDTO)
    {
        var result = await mediator.Send(new TratarTroquelCommand(troquelDTO));
        return result;
    }

    public async Task<ListResult<ImposicionesDTO>> GetAllImposiciones()
    {
        var model = await mediator.Send(new GetAllImposicionesQuery());
        return model;
    }

    public async Task<SingleResult<ImposicionesDTO>> GetImposicionById(int id)
    {
        var result = await mediator.Send(new GetImposicionByIdQuery(id));
        return result;
    }

    public async Task<SingleResult<ImposicionesDTO>> TratarImposicion(ImposicionesDTO imposicionDTO)
    {
        var result = await mediator.Send(new TratarImposicionCommand(imposicionDTO));
        return result;
    }

    public async Task<ListResult<TroquelesDTO>> GetTroquelesIncompletos()
    {
        var model = await mediator.Send(new GetTroquelesIncompletosQuery());
        return model;
    }

    public async Task<ListResult<TroquelesDTO>> GetTroquelesInactivos()
    {
        var model = await mediator.Send(new GetTroquelesInactivosQuery());
        return model;
    }

    public async Task<SingleResult<bool>> ActualizarPlanos()
    {
        var result = await mediator.Send(new ActualizarPlanosCommand());

        var singleResult = new SingleResult<bool>
        {
            Data = result.Success // puedes poner true/false como resultado
        };

        // Asignamos las propiedades heredadas
        singleResult.GetType().BaseType?.GetProperty("Success")?.SetValue(singleResult, result.Success);
        singleResult.GetType().BaseType?.GetProperty("Message")?.SetValue(singleResult, result.Message);

        return singleResult;
    }

    public async Task<ListResult<ImposicionesDTO>> GetImposicionesInactivas()
    {
        var model = await mediator.Send(new GetImposicionesInactivasQuery());
        return model;
    }

    public async Task<ListResult<ImposicionesDTO>> GetImposicionesIncompletas()
    {
        var model = await mediator.Send(new GetImposicionesIncompletasQuery());
        return model;
    }

    public async Task<ListResult<TiposCaracteristicasDTO>> GetAllTiposCaracteristicas()
    {
        var model = await mediator.Send(new GetAllTiposCaracteristicasQuery());
        return model;
    }

    public async Task<ListResult<ValoresTiposCaracteristicasDTO>> GetAllValoresTiposCaracteristicas()
    {
        var model = await mediator.Send(new GetAllValoresTiposCaracteristicasQuery());
        return model;
    }

    public async Task<SingleResult<TiposCaracteristicasDTO>> TratarTipoCaracteristica(TiposCaracteristicasDTO tipoCaracteristicaDTO)
    {

        var result = await mediator.Send(new TratarTipoCaracteristicaCommand(tipoCaracteristicaDTO));
        return result;
    }

    public async Task<SingleResult<ValoresTiposCaracteristicasDTO>> TratarValoresTipoCaracteristica(ValoresTiposCaracteristicasDTO valoresTiposCaracteristicasDTO)
    {
        var result = await mediator.Send(new TratarValoresTiposCaracteristicasCommand(valoresTiposCaracteristicasDTO));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteTipoCaracteristicaCommand(int id)
    {
        var result = await mediator.Send(new DeleteTipoCaracteristicaCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteValorTipoCaracteristicaCommand(int id)
    {
        var result = await mediator.Send(new DeleteValorTipoCaracteristicaCommand(id));
        return result;
    }

    public async Task<ListResult<MetodosInspeccionDTO>> GetAllMetodosInspeccion()
    {
        var model = await mediator.Send(new GetAllMetodosInspeccionQuery());
        return model;
    }

    public async Task<ListResult<CaracteristicasInspeccionDTO>> GetAllCaracteristicasInspeccion()
    {
        var model = await mediator.Send(new GetAllCaracteristicasInspeccionQuery());
        return model;
    }

    public async Task<SingleResult<MetodosInspeccionDTO>> TratarMetodoInspeccion(MetodosInspeccionDTO metodosInspeccionDTO)
    {
        var result = await mediator.Send(new TratarMetodosInspeccionCommand(metodosInspeccionDTO));
        return result;
    }

    public async Task<SingleResult<CaracteristicasInspeccionDTO>> TratarCaracteristicaInspeccion(CaracteristicasInspeccionDTO caracteristicasInspeccionDTO)
    {
        var result = await mediator.Send(new TratarCaracteristicaInspeccionCommand(caracteristicasInspeccionDTO));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteMetodoInspeccion(int id)
    {
        var result = await mediator.Send(new DeleteMetodoInspeccionCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteCaracteristicaInspeccion(int id)
    {
        var result = await mediator.Send(new DeleteCaracteristicaInspeccionCommand(id));
        return result;
    }

    //MM
    public async Task<ListResult<CodigoEnvasesDTO>> GetAllCodigosEnvases()
    {
        var model = await mediator.Send(new GetAllCodigoEnvasesQuery());
        return model;
    }

    public async Task<ListResult<ExcepcionesPreciosLotesDTO>> GetAllExcepcionesPrecios()
    {
        var model = await mediator.Send(new GetAllExcepcionesPreciosLotesQuery());
        return model;
    }

    public async Task<ListResult<FamiliasDTO>> GetAllFamilias()
    {
        var model = await mediator.Send(new GetAllFamiliasQuery());
        return model;
    }

    public async Task<ListResult<NaturalezaDTO>> GetAllNaturalezas()
    {
        var model = await mediator.Send(new GetAllNaturalezasQuery());
        return model;
    }

    public async Task<ListResult<NodrizasDTO>> GetAllNodrizas()
    {
        var model = await mediator.Send(new GetAllNodrizasQuery());
        return model;
    }

    public async Task<ListResult<NoIncluirRegComprasDTO>> GetAllNoIncluirRegCompras()
    {
        var model = await mediator.Send(new GetAllNoIncluirRegComprasQuery());
        return model;
    }

    public async Task<ListResult<ConsignaNestleDTO>> GetAllPaquetesConsignaNestle()
    {
        var model = await mediator.Send(new GetAllConsignaNestleQuery());
        return model;
    }

    public async Task<ListResult<DatosAuxMaterialesDTO>> GetAllDatosAux()
    {
        var model = await mediator.Send(new GetAllDatosAuxMaterialesQuery());
        return model;
    }

    public async Task<ListResult<EnvasesDTO>> GetAllEnvases()
    {
        var model = await mediator.Send(new GetAllEnvasesQuery());
        return model;
    }

    public async Task<ListResult<ColoresDTO>> GetAllColores()
    {
        var model = await mediator.Send(new GetAllColoresQuery());
        return model;
    }

    public async Task<ListResult<CorrespondenciasSkusDTO>> GetAllCorrespondenciasSKUs()
    {
        var model = await mediator.Send(new GetAllCorrespondenciasSKUsQuery());
        return model;
    }

    public async Task<ListResult<RelacionCompuestasDTO>> GetAllRelacionesCompuestas()
    {
        var model = await mediator.Send(new GetAllRelacionesCompuestasQuery());
        return model;
    }

    public async Task<SingleResult<CodigoEnvasesDTO>> TratarCodigoEnvase(CodigoEnvasesDTO codigoEnvasesDTO)
    {
        var result = await mediator.Send(new TratarCodigoEnvasesCommand(codigoEnvasesDTO));
        return result;
    }

    public async Task<SingleResult<ExcepcionesPreciosLotesDTO>> TratarExcepcionesPreciosLotes(ExcepcionesPreciosLotesDTO excepcionesPreciosLotesDTO)
    {
        var result = await mediator.Send(new TratarExcepcionesPreciosLotesCommand(excepcionesPreciosLotesDTO));
        return result;
    }

    public async Task<SingleResult<FamiliasDTO>> TratarFamilia(FamiliasDTO familiasDTO)
    {
        var result = await mediator.Send(new TratarFamiliaCommand(familiasDTO));
        return result;
    }

    public async Task<SingleResult<NaturalezaDTO>> TratarNaturaleza(NaturalezaDTO naturalezaDTO)
    {
        var result = await mediator.Send(new TratarNaturalezaCommand(naturalezaDTO));
        return result;
    }

    public async Task<SingleResult<NodrizasDTO>> TratarNodriza(NodrizasDTO nodrizasDTO)
    {
        var result = await mediator.Send(new TratarNodrizaCommand(nodrizasDTO));
        return result;
    }

    public async Task<SingleResult<NoIncluirRegComprasDTO>> TratarNoIncluirRegCompras(NoIncluirRegComprasDTO noIncluirRegComprasDTO)
    {
        var result = await mediator.Send(new TratarNoIncluirRegComprasCommand(noIncluirRegComprasDTO));
        return result;
    }

    public async Task<SingleResult<ConsignaNestleDTO>> TratarConsignaNestle(ConsignaNestleDTO consignaNestleDTO)
    {
        var result = await mediator.Send(new TratarConsignaNestleCommand(consignaNestleDTO));
        return result;
    }

    public async Task<SingleResult<DatosAuxMaterialesDTO>> TratarDatosAuxMateriales(DatosAuxMaterialesDTO datosAuxMaterialesDTO)
    {
        var result = await mediator.Send(new TratarDatosAuxMaterialesCommand(datosAuxMaterialesDTO));
        return result;
    }

    public async Task<SingleResult<EnvasesDTO>> TratarEnvase(EnvasesDTO envasesDTO)
    {
        var result = await mediator.Send(new TratarEnvaseCommand(envasesDTO));
        return result;
    }

    public async Task<SingleResult<ColoresDTO>> TratarColores(ColoresDTO coloresDTO)
    {
        var result = await mediator.Send(new TratarColorCommand(coloresDTO));
        return result;
    }

    public async Task<SingleResult<CorrespondenciasSkusDTO>> TratarCorrespondenciaSKUs(CorrespondenciasSkusDTO correspondenciasSkusDTO)
    {
        var result = await mediator.Send(new TratarCorrespondenciaSKUCommand(correspondenciasSkusDTO));
        return result;
    }

    public async Task<SingleResult<RelacionCompuestasDTO>> TratarRelacionCompuesta (RelacionCompuestasDTO relacionCompuestasDTO)
    {
        var result = await mediator.Send(new TratarRelacionCompuestaCommand(relacionCompuestasDTO));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteCodigoEnvase(int id)
    {
        var result = await mediator.Send(new DeleteCodigoEnvasesCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteExcepcionPrecioLote(int id)
    {
        var result = await mediator.Send(new DeleteExcepcionPrecioLoteCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteFamilia(int id)
    {
        var result = await mediator.Send(new DeleteFamiliaCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteNaturaleza(int id)
    {
        var result = await mediator.Send(new DeleteNaturalezaCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteBarnizNodriza(int id)
    {
        var result = await mediator.Send(new DeleteBarnicesNodrizasCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteNoIncluirRegCompras(int codigo)
    {
        var result = await mediator.Send(new DeleteNoIncluirRegComprasCommand(codigo));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteConsignaNestle(int id)
    {
        var result = await mediator.Send(new DeleteConsignaNestleCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteDatoAux(int id)
    {
        var result = await mediator.Send(new DeleteDatosAuxCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteEnvase(int id)
    {
        var result = await mediator.Send(new DeleteEnvaseCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteColor(int id)
    {
        var result = await mediator.Send(new DeleteColorCommand(id));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteCorrespondenciaSKU(int codigoLitalsa)
    {
        var result = await mediator.Send(new DeleteCorrespondenciaSKUCommand(codigoLitalsa));
        return result;
    }

    public async Task<SingleResult<bool>> DeleteRelacionCompuesta(int id)
    {
        var result = await mediator.Send(new DeleteRelacionCompuestaCommand(id));
        return result;
    }

    public async Task<ListResult<MaterialesDTO>> GetAllMateriales()
    {
        var model = await mediator.Send(new GetAllMaterialesQuery());
        return model;
    }

    public async Task<ListResult<MaterialesDTO>> GetMaterialesByTipo(string tipo)
    {
        var model = await mediator.Send(new GetMaterialesByTipoQuery(tipo));
        return model;
    }

    public async Task<ListResult<SAPClienteDTO>> GetAllSAPClientes()
    {
        var model = await mediator.Send(new GetAllSAPClientesQuery());
        return model;
    }

    public async Task<SingleResult<bool>> DeleteSAPCliente(int clienteId)
    {
        var result = await mediator.Send(new DeleteSAPClienteCommand(clienteId));
        return result;
    }

    public async Task<SingleResult<SAPClienteDTO>> TratarSAPCliente(SAPClienteDTO sAPClienteDTO)
    {
        var result = await mediator.Send(new TratarSAPClienteCommand(sAPClienteDTO));
        return result;
    }

    public async Task<ListResult<SAPClienteDestinatarioDTO>> GetAllSAPClientesDestinatarios()
    {
        var model = await mediator.Send(new GetAllSAPClientesDestinatariosQuery());
        return model;
    }

    public async Task<SingleResult<bool>> DeleteSAPClienteDestinatario(int id)
    {
        var result = await mediator.Send(new DeleteSAPClienteDestinatarioCommand(id));
        return result;
    }

    public async Task<SingleResult<SAPClienteDestinatarioDTO>> TratarSAPClienteDestinatario(SAPClienteDestinatarioDTO sAPClienteDestinatarioDTO)
    {
        var result = await mediator.Send(new TratarSAPClienteDestinatariosCommand(sAPClienteDestinatarioDTO));
        return result;
    }

    public async Task<ListResult<SAPProveedorDTO>> GetAllSAPProveedores()
    {
        var model = await mediator.Send(new GetAllSAPProveedoresQuery());
        return model;
    }

    public async Task<SingleResult<bool>> DeleteSAPProveedor(int id)
    {
        var result = await mediator.Send(new DeleteSAPProveedorCommand(id));
        return result;
    }

    public async Task<SingleResult<SAPProveedorDTO>> TratarSAPProveedor(SAPProveedorDTO sAPProveedorDTO)
    {
        var result = await mediator.Send(new TratarSAPProveedorCommand(sAPProveedorDTO));
        return result;
    }

    public async Task<ListResult<TipoElementoDTO>> GetAllTiposElemento()
    {
        var model = await mediator.Send(new GetAllTiposElementoQuery());
        return model;
    }

    public async Task<SingleResult<bool>> DeleteTipoElemento(int id)
    {
        var result = await mediator.Send(new DeleteTipoElementoCommand(id));
        return result;
    }

    public async Task<SingleResult<TipoElementoDTO>> TratarTipoElemento(TipoElementoDTO tipoElementoDTO)
    {
        var result = await mediator.Send(new TratarTipoElementoCommand(tipoElementoDTO));
        return result;
    }

    public async Task<ListResult<MaestroDTO>> GetAllMaestro()
    {
        var model = await mediator.Send(new GetAllMaestroQuery());
        return model;
    }

    public async Task<ListResult<MaestroDTO>> GetMaestroIncompletos()
    {
        var model = await mediator.Send(new GetMaestroIncompletosQuery());
        return model;
    }

    public async Task<Result> ActualizarTroqueles()
    {
        var result = await mediator.Send(new ActualizarTroquelesCommand());
        return result;
    }

    public async Task<Result> ActualizarImposiciones()
    {
        var result = await mediator.Send(new ActualizarImposicionesCommand());
        return result;
    }

    public async Task<ListResult<MotivosDTO>> GetAllMotivos()
    {
        var model = await mediator.Send(new GetAllMotivosQuery());
        return model;
    }

    public async Task<Result> ActualizarMotivos()
    {
        var result = await mediator.Send(new ActualizarMotivosCommand());
        return result;
    }

    public async Task<SingleResult<TroquelesDTO>> DuplicarTroquel(int id)
    {
        var model = await mediator.Send(new DuplicarTroquelCommand(id));
        return model;
    }

    public async Task<SingleResult<ImposicionesDTO>> DuplicarImposicion(int id)
    {
        var model = await mediator.Send(new DuplicarImposicionCommand(id));
        return model;
    }

    public async Task<ListResult<AuditoriaDTO>> GetAllAuditorias()
    {
        var model = await mediator.Send(new GetAllAuditoriasQuery());
        return model;
    }

    public async Task<SingleResult<AuditoriaDTO>> GetUltimaActualizacionByTabla(string tabla)
    {
        var model = await mediator.Send(new GetUltimaActualizacionByTablaQuery(tabla));
        return model;
    }

    public async Task<ListResult<AuditoriaDTO>> GetUltimosEventosQuery()
    {
        var model = await mediator.Send(new GetUltimosEventosQuery());
        return model;
    }
}