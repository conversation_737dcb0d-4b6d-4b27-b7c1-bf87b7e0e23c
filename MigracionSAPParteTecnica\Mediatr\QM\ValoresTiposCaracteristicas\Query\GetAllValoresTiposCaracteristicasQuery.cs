﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Query;
public class GetAllValoresTiposCaracteristicasQuery : IRequest<ListResult<ValoresTiposCaracteristicasDTO>>
{
}

internal class GetAllValoresTiposCaracteristicasQueryHandler : IRequestHandler<GetAllValoresTiposCaracteristicasQuery, ListResult<ValoresTiposCaracteristicasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllValoresTiposCaracteristicasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ValoresTiposCaracteristicasDTO>> Handle(GetAllValoresTiposCaracteristicasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ValoresTiposCaracteristicasDTO>
        {
            Data = new List<ValoresTiposCaracteristicasDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listValoresTiposCaracteristicas = await _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas
                .AsNoTracking()
                .Include(v => v.QmTiposCaracteristicas)
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ValoresTiposCaracteristicasDTO>>(listValoresTiposCaracteristicas).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllValoresTiposCaracteristicasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}