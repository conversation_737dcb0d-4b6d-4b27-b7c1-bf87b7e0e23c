﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Query;
public class GetAllExcepcionesPreciosLotesQuery : IRequest<ListResult<ExcepcionesPreciosLotesDTO>>
{
}

internal class GetAllExcepcionesPreciosLotesQueryHandler : IRequestHandler<GetAllExcepcionesPreciosLotesQuery, ListResult<ExcepcionesPreciosLotesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllExcepcionesPreciosLotesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<ExcepcionesPreciosLotesDTO>> Handle(GetAllExcepcionesPreciosLotesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ExcepcionesPreciosLotesDTO>
        {
            Data = new List<ExcepcionesPreciosLotesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listExcepcionesPreciosLotes = await _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<ExcepcionesPreciosLotesDTO>>(listExcepcionesPreciosLotes).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllCodigoEnvasesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}