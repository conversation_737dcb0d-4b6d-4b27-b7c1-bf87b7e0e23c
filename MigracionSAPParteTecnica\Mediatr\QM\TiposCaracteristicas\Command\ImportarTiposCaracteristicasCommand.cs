﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.QM;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;

public class ImportarTiposCaracteristicasCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarTiposCaracteristicasCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarTiposCaracteristicasCommandHandler : IRequestHandler<ImportarTiposCaracteristicasCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarTiposCaracteristicasCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarTiposCaracteristicasCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);

            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                var dto = new TiposCaracteristicasDTO
                {
                    CodigoTipo = worksheet.Cell(filaActual, 2).GetString().Trim(),  
                    Tipo = worksheet.Cell(filaActual, 3).GetString().Trim(),        
                    Descripcion = worksheet.Cell(filaActual, 4).GetString().Trim(), 
                    Borrado = false
                };

                var error = TipoCaracteristicaValidator.Validar(dto);
                if (error != null)
                {
                    errores.Add($"Fila {filaActual}: {error}");
                    descartados++;
                    filaActual++;
                    continue;
                }

                var existente = await _context.TiposCaracteristicas
                    .FirstOrDefaultAsync(t =>
                        t.CodigoTipo == dto.CodigoTipo &&
                        t.Tipo == dto.Tipo, cancellationToken);

                var clave = $"{dto.CodigoTipo}_{dto.Tipo}";
                var idAuditoria = clave.GetHashCode();

                if (existente is null)
                {
                    var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.TiposCaracteristicas>(dto);
                    await _context.TiposCaracteristicas.AddAsync(nuevo, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "TiposCaracteristicas",
                        IdRegistro = nuevo.Id,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(nuevo),
                        Comentarios = "Importado desde Excel"
                    }, cancellationToken);

                    insertados++;
                }
                else
                {
                    var datosAntes = JsonSerializer.Serialize(existente);
                    TinyMapper.Map(dto, existente);
                    await _context.SaveChangesAsync(cancellationToken);
                    var datosDespues = JsonSerializer.Serialize(existente);

                    if (datosAntes != datosDespues)
                    {
                        await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "TiposCaracteristicas",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = $"Descripción actualizada: '{existente.Descripcion}'"
                        }, cancellationToken);

                        actualizados++;
                    }
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar tipos de características: {ex.Message}";
        }

        return result;
    }
}
