html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

html, body {
    height: 100%;
    overflow: hidden;
}

a, .btn-link {
    color: #006bb7;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.content {
    padding-top: 1.1rem;
}

h1:focus {
    outline: none;
}


modified.valid:not(.dxbl-checkbox) {
    outline: 1px solid #26b050 !important;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

.iconInButton {
    padding: 0 0.75rem 0 0 !important;
    top: -2px !important;
}

.validation-message {
    color: #e50000;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.darker-border-checkbox.form-check-input {
    border-color: #929292;
}

.botonRojo {
    background-color: #a91c32 !important;
    --dxbl-btn-border-color: transparent !important;
}

.dxbl-checkbox.dxbl-checkbox-switch.dxbl-readonly > .dxbl-checkbox-check-element {
    -ms-opacity: 100;
    opacity: 100;
}

.dxbl-checkbox.dxbl-checkbox-switch.dxbl-checkbox-checked .dxbl-checkbox-check-element {
    background-color: green;
}

.dxbl-checkbox.dxbl-checkbox-switch.dxbl-checkbox-unchecked .dxbl-checkbox-check-element {
    background-color: red;
}

.dxbl-checkbox.dxbl-checkbox-switch .dxbl-checkbox-check-element {
    margin: 0;
}

.bold {
    font-weight: 600 !important;
}

    .bold::before {
        font-weight: 600 !important;
    }

.ellipsisRow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cw-480 {
    min-width: 200px;
}

.roller-container:first-of-type {
    display: none !important;
}

.filter-type-container {
    display: flex;
}

    .filter-type-container > div {
        width: 100%;
    }

.filter-type-button {
    margin-right: 5px;
}

.dxbl-drawer-panel {
    background-image: linear-gradient(180deg, rgba(163, 164, 167, 1) 0%, rgba(115, 110, 116, 1) 50%, rgba(163, 164, 167, 1) 70%, rgba(115, 110, 116, 1) 100%);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
}

.dxbl-drawer > .dxbl-drawer-panel > .dxbl-drawer-body {
    padding: 0 !important;
}

.fila-inactiva {
    background-color: rgba(255, 0, 0, 0.15);
}

.icon-flyout-button {
    all: unset; 
    cursor: pointer;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .icon-flyout-button i:hover {
        color: #0a58ca;
    }

.custom-drop-zone {
    padding: 0 !important;
    border-style: dashed;
    border-width: 2px !important;
    height: 230px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(183, 183, 183, 0.1);
}

    .custom-drop-zone.custom-drop-zone-hover {
        border-style: solid;
    }

    .custom-drop-zone .drop-file-icon {
        font-size: 2rem;
    }

    .custom-drop-zone > *:not(button) {
        pointer-events: none;
    }

.dxbs-toast-message {
    white-space: pre-wrap;
}

.drop-zone {
    border: 2px dashed #ccc;
    padding: 20px;
    width: 100%;
    text-align: center;
    cursor: pointer;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.input-file-hidden {
    display: none;
}

.input-error {
    border-color: red;
}

.drop-zone {
    border: 2px dashed #aaa;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.drop-zone-hover {
    background-color: #d3e6ff;
    border-color: #1a73e8;
}

.input-error {
    border-color: red;
}

.fila-plano-inactivo {
    background-color: orange !important;
}

/.fila-inactiva {
    background-color: #ffcccc !important; /* rojo claro */
}

.fila-inactiva,
.dx-fixed-left .fila-inactiva,
.dx-fixed-right .fila-inactiva {
    background-color: #ffcccc !important;
}

.fila-plano-inactivo,
.dx-fixed-left .fila-plano-inactivo,
.dx-fixed-right .fila-plano-inactivo {
    background-color: #ffc107 !important;
}

.fila-plano-inactivo {
    background-color: #ffc107 !important; 
}

.fila-inactiva td,
.dx-fixed-left .fila-inactiva td,
.dx-fixed-right .fila-inactiva td {
    background-color: #ffcccc !important;
}

.fila-plano-inactivo td,
.dx-fixed-left .fila-plano-inactivo td,
.dx-fixed-right .fila-plano-inactivo td {
    background-color: #ffc107 !important;
}

.fila-origen-modificado {
    background-color: #ffecb3 !important;
}

    .fila-origen-modificado td,
    .dx-fixed-left .fila-origen-modificado td,
    .dx-fixed-right .fila-origen-modificado td {
        background-color: #ffecb3 !important;
    }

.boton-azul-oscuro {
    background-color: #1261e0 !important;
    color: white !important;
    border-color: #1261e0 !important;
}

.alert-warning {
    font-size: 0.95em;
    padding: 8px 12px;
}