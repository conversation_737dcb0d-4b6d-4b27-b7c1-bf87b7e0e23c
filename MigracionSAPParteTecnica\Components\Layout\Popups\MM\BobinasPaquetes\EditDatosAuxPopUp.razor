﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editDatoAux?.Id > 0)
{
    <EntityLockManager EntityType="DatosAuxMateriales"
                       EntityId="editDatoAux.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Datos Auxiliares"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este dato auxiliar está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editDatoAux" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editDatoAux" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Medidas:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/medidas-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxTextBox @bind-Text="editDatoAux.Medidas" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tipo de material:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/tipo_material-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxComboBox Data="@TiposCaracteristica"
                                        Value="TipoSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((string? tipo) => OnTipoCaracteristicaSeleccionChanged(tipo))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Espesor real:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/espesor-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editDatoAux.EspesorReal"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Ancho real:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editDatoAux.AnchoReal"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Largo real:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows-vertical" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editDatoAux.LargoReal" ReadOnly="@(_lectura || SoloPermitirMedidas)"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false"/>
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Largo scroll:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item"  ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows-expand" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editDatoAux.LargoScroll" ReadOnly="@(_lectura || SoloPermitirMedidas)"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false"/>
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<DatosAuxMaterialesDTO> OnSave { get; set; }
    private DatosAuxMaterialesDTO editDatoAux = new();

    private string? TipoSeleccionado;
    private List<string> TiposCaracteristica = new() { "Paquete", "Bobina" };
    private bool SoloPermitirMedidas => editDatoAux.TipoMaterial == "Bobina";

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(DatosAuxMaterialesDTO datoAux)
    {
        editDatoAux = datoAux;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = DatosAuxMaterialesValidator.Validar(editDatoAux);
        if (!string.IsNullOrWhiteSpace(error))
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editDatoAux);
        IsPopupVisible = false;
        ToastService.MostrarOk("Dato auxiliar guardado correctamente.");
    }

    private void OnTipoCaracteristicaSeleccionChanged(string? tipo)
    {
        editDatoAux.TipoMaterial = tipo;

        if (tipo == "Bobina")
        {
            editDatoAux.LargoReal = null;
            editDatoAux.LargoScroll = null;
        }

        StateHasChanged();
    }
}