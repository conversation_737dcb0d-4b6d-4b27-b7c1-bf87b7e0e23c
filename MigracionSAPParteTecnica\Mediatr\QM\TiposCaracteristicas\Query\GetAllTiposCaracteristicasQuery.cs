﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Query;
public class GetAllTiposCaracteristicasQuery : IRequest<ListResult<TiposCaracteristicasDTO>>
{
}

internal class GetAllTiposCaracteristicasQueryHandler : IRequestHandler<GetAllTiposCaracteristicasQuery, ListResult<TiposCaracteristicasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllTiposCaracteristicasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<TiposCaracteristicasDTO>> Handle(GetAllTiposCaracteristicasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TiposCaracteristicasDTO>
        {
            Data = new List<TiposCaracteristicasDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var listTiposCaracteristicas = await _migracionSAPParteTecnicaContext.TiposCaracteristicas
                .AsNoTracking()
                //.Include(t => t.ValoresTiposCaracteristicas)
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<TiposCaracteristicasDTO>>(listTiposCaracteristicas).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllTiposCaracteristicasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}