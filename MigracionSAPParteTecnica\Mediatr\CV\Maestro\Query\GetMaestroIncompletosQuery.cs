﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Maestro.Query;

public class GetMaestroIncompletosQuery : IRequest<ListResult<MaestroDTO>>
{
}

internal class GetMotivosIncompletosQueryHandler : IRequestHandler<GetMaestroIncompletosQuery, ListResult<MaestroDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetMotivosIncompletosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MaestroDTO>> Handle(GetMaestroIncompletosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MaestroDTO>
        {
            Data = new List<MaestroDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var motivosIncompletos = await _migracionSAPParteTecnicaContext.Maestro
            .Where(m =>
                m.Cliente_SAP == null ||
                m.TipoProducto == null ||
                m.Embuticion == null ||
                m.HojaScroll == null ||
                m.Tratamiento == null
            )
            .ToListAsync();

            result.Data = TinyMapper.Map<List<MaestroDTO>>(motivosIncompletos).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetMotivosIncompletosQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}