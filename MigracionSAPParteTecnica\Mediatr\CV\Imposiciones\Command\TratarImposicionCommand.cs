﻿using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Hubs;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;
public class TratarImposicionCommand : IRequest<SingleResult<ImposicionesDTO>>
{
    public ImposicionesDTO ImposicionDTO { get; }
    
    public TratarImposicionCommand(ImposicionesDTO imposicionDTO)
    {
        ImposicionDTO = imposicionDTO;
    }
}

internal class TratarImposicionCommandHandler : IRequestHandler<TratarImposicionCommand, SingleResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHubContext<AppEventsHub> _hubContext;

    public TratarImposicionCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor,
        IHubContext<AppEventsHub> hubContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
        _hubContext = hubContext;
    }

    private void CopiarAOrigen(ImposicionesDTO i)
    {
        i.Cliente_SAP_Origen = i.Cliente_SAP;
        i.Cliente_IN2_Origen = i.Cliente_IN2;
        i.Plano_Origen = i.Plano;
        i.AnchoHoja_Origen = i.AnchoHoja;
        i.LargoHoja_Origen = i.LargoHoja;
        i.LargoScroll_Origen = i.LargoScroll;
        i.NumeroCuerpos_Origen = i.Cuerpos;
        i.IdTroquel_Origen = i.IdTroquel;
    }
    public async Task<SingleResult<ImposicionesDTO>> Handle(TratarImposicionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<ImposicionesDTO>
        {
            Data = new ImposicionesDTO(),
            Errors = new List<string>()
        };

        try
        {
            var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
            var ahora = DateTime.Now;

            if (request.ImposicionDTO.Id == 0)
            {
                CopiarAOrigen(request.ImposicionDTO);

                var nuevaImposicion = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Imposiciones>(request.ImposicionDTO);

                await _migracionSAPParteTecnicaContext.Imposiciones.AddAsync(nuevaImposicion, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                request.ImposicionDTO.Id = nuevaImposicion.Id;
                result.Data = request.ImposicionDTO;


                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = ahora,
                    Accion = "INSERT",
                    Tabla = "CV_Imposiciones",
                    IdRegistro = nuevaImposicion.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevaImposicion),
                    Comentarios = $"Alta de imposición con Id '{nuevaImposicion.Id}'"
                }, cancellationToken);

                await _hubContext.Clients.All.SendAsync(
                    "EventoAplicacion",
                    new
                    {
                        Tipo = "ImposicionModificada",
                        Id = result.Data.Id,
                        Plano = result.Data.Plano,
                        Cliente = result.Data.Cliente_SAP,
                        Usuario = usuario
                    }
                );
            }
            else
            {
                var currentImposicion = await _migracionSAPParteTecnicaContext.Imposiciones
                    .FirstOrDefaultAsync(i => i.Id == request.ImposicionDTO.Id, cancellationToken);


                if (currentImposicion != null)
                {
                    var datosAntes = JsonSerializer.Serialize(currentImposicion);

                    // Detección de cambios en campos _Origen
                    bool origenModificado =
                        currentImposicion.Cliente_SAP != currentImposicion.Cliente_SAP_Origen ||
                        currentImposicion.Cliente_IN2 != currentImposicion.Cliente_IN2_Origen ||
                        currentImposicion.Plano != currentImposicion.Plano_Origen ||
                        currentImposicion.AnchoHoja != currentImposicion.AnchoHoja_Origen ||
                        currentImposicion.LargoHoja != currentImposicion.LargoHoja_Origen ||
                        currentImposicion.LargoScroll != currentImposicion.LargoScroll_Origen ||
                        currentImposicion.Cuerpos != currentImposicion.NumeroCuerpos_Origen ||
                        currentImposicion.IdTroquel != currentImposicion.IdTroquel_Origen;

                    request.ImposicionDTO.OrigenModificado = origenModificado;

                    if (origenModificado)
                    {
                        await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = ahora,
                            Accion = "UPDATE-ORIGEN-MODIFICADO",
                            Tabla = "CV_Imposiciones",
                            IdRegistro = currentImposicion.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = JsonSerializer.Serialize(request.ImposicionDTO),
                            Comentarios = $"[ALERTA] Se han modificado campos *_Origen* en imposición ID {currentImposicion.Id}"
                        }, cancellationToken);
                    }

                    var updatedImposicion = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Imposiciones>(request.ImposicionDTO);

                    _migracionSAPParteTecnicaContext.Entry(currentImposicion).CurrentValues.SetValues(updatedImposicion);
                    await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                    result.Data = request.ImposicionDTO;

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = ahora,
                        Accion = "UPDATE",
                        Tabla = "CV_Imposiciones",
                        IdRegistro = updatedImposicion.Id,
                        DatosAntes = datosAntes,
                        DatosDespues = JsonSerializer.Serialize(updatedImposicion),
                        Comentarios = $"Modificación de imposición con id '{updatedImposicion.Id}'"
                    }, cancellationToken);

                    await _hubContext.Clients.All.SendAsync(
                        "EventoAplicacion",
                        new
                        {
                            Tipo = "ImposicionModificada",
                            Id = result.Data.Id,
                            Plano = result.Data.Plano,
                            Cliente = result.Data.Cliente_SAP, 
                            Usuario = usuario
                        }
                    );
                }
                else
                {
                    result.Errors.Add("Imposición no encontrada");
                }
            }
        }
        catch(Exception e)
        {
            var errorText = $"ERROR: TratarImposicion - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}