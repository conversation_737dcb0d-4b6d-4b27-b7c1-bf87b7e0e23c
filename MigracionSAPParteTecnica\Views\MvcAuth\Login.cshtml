﻿@using MigracionSAPParteTecnica.DTO.General
@model LoginRequestDTO
@{
    Layout = null;
    var showLogoutMessage = Context.Request.Query["logout"] == "1";
    var error = Context.Request.Query["error"];
}

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="utf-8" />
    <title>Login - Litalsa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="bootstrap/bootstrap-icons.min.css" />
    @* <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet"> *@
    <style>
        body {
            background: url('/images/fondo-login.jpg') no-repeat center center fixed;
            background-size: cover;
        }

        .overlay {
            background-color: rgba(0, 0, 0, 0.4);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .login-card {
            z-index: 10;
            padding: 2rem;
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
            margin: auto;
            position: relative;
        }

        .logo-login {
            max-width: 200px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="overlay"></div>
    <div class="d-flex align-items-center justify-content-center min-vh-100">
        <div class="login-card text-center">
            <img src="/images/litalsa-mch.png" alt="Litalsa" class="logo-login" />
            @if (showLogoutMessage)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    Sesión cerrada correctamente.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Cerrar"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(error))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @(
                                    error == "usuario" ? "Usuario no encontrado." :
                                    error == "credenciales" ? "Credenciales incorrectas." :
                                    error == "rol" ? "No tienes permisos para acceder al sistema." :
                                    "Se ha producido un error inesperado."
                                    )
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Cerrar"></button>
                </div>
            }
            <form method="post" action="/Login">
                <div class="form-group mb-3 text-start">
                    <label class="fw-bold">Usuario</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                        <input name="NombreUsuario" class="form-control" placeholder="Usuario" />
                    </div>
                </div>
                <div class="form-group mb-3 text-start">
                    <label class="fw-bold">Contraseña</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-unlock2"></i></span>
                        <input name="Contraseña" class="form-control" placeholder="Contraseña" type="password" />
                    </div>
                </div>
                <div class="form-check mb-3 text-start">
                    <input name="Recordarme" value="true" class="form-check-input" type="checkbox" />
                    <label class="form-check-label">Recordar sesión durante 7 días</label>
                </div>
                <button type="submit" class="btn btn-danger w-100">Iniciar sesión</button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Cierra automáticamente las alertas después de 4 segundos
        window.addEventListener('DOMContentLoaded', () => {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = bootstrap.Alert.getOrCreateInstance(alert);
                    bsAlert.close();
                }, 4000); // 4 segundos
            });
        });
    </script>
</body>
</html>
