﻿using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.General;

namespace MigracionSAPParteTecnica.Services;
public interface IAuditoriaService
{
    Task RegistrarAsync(AuditoriaDTO auditoria, CancellationToken cancellationToken = default);
}

public class AuditoriaService : IAuditoriaService
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AuditoriaService(MigracionSAPParteTecnicaContext context, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task RegistrarAsync(AuditoriaDTO dto, CancellationToken cancellationToken = default)
    {
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var entidad = new Auditoria
        {
            Tabla = dto.Tabla,
            IdRegistro = dto.IdRegistro,
            Accion = dto.Accion,
            Usuario = usuario,
            Fecha = DateTime.Now,
            Resultado = dto.Resultado,
            Comentarios = dto.Comentarios,
            DatosAntes = dto.DatosAntes,
            DatosDespues = dto.DatosDespues,
            Borrado = dto.Borrado,
            DuracionMs = dto.DuracionMs
        };

        await _context.Auditorias.AddAsync(entidad, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }
}