﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Command;
public class DeleteMetodoInspeccionCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteMetodoInspeccionCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteMetodoInspeccionCommandHandler : IRequestHandler<DeleteMetodoInspeccionCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteMetodoInspeccionCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteMetodoInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var metodoInspeccion = await _migracionSAPParteTecnicaContext.MetodosInspeccion
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if(metodoInspeccion == null)
            {
                result.Errors.Add("No se ha encontrado el método de inspección a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(metodoInspeccion);

            metodoInspeccion.Borrado = true;

            _migracionSAPParteTecnicaContext.MetodosInspeccion.Update(metodoInspeccion);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "QM_MetodosInspeccion",
                IdRegistro = metodoInspeccion.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica del método de inspección: Id='{metodoInspeccion.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteMetodoInspeccionCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}