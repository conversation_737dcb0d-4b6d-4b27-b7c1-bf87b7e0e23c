﻿using MigracionSAPParteTecnica.Entities.ViewModels;
namespace MigracionSAPParteTecnica.Helpers;
public static class CampoEditableHelper
{
    public static List<CampoEditable> GetCamposImposicion()
    {
        return new List<CampoEditable>
        {
            new() { Nombre = "SentidoLectura", TipoDato = typeof(string), Descripcion = "Texto libre." },
            new() { Nombre = "ArranquePinza", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 42" },
            new() { Nombre = "ArranqueEscuadra", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 42" },
            new() { Nombre = "NumeroDesarrollos", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 5" },
            new() { Nombre = "NumeroAlturas", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 3" },
            new() { Nombre = "PosicionEscuadraExt", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 10" },
            new() { Nombre = "PosicionEscuadraInv", TipoDato = typeof(int), Descripcion = "Número entero. Ej: 12" },
            new() { Nombre = "Nota", TipoDato = typeof(string), Descripcion = "Texto libre." },
            new() { Nombre = "Activo", TipoDato = typeof(bool), Descripcion = "Booleano. Ej: true / false" },
            new() { Nombre = "FechaCreacion", TipoDato = typeof(DateTime), Descripcion = "Fecha. Ej: 2025-06-12" }
        };
    }
}