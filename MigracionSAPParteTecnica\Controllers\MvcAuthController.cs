﻿using MigracionSAPParteTecnica.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MigracionSAPParteTecnica.DTO.General;

namespace MigracionSAPParteTecnica.Controllers;

public class MvcAuthController : Controller
{
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly UserManager<ApplicationUser> _userManager;

    public MvcAuthController(SignInManager<ApplicationUser> signInManager, UserManager<ApplicationUser> userManager)
    {
        _signInManager = signInManager;
        _userManager = userManager;
    }

    [HttpGet("/Login")]
    public IActionResult Login()
    {
        return View();
    }

    [HttpPost("/Login")]
    public async Task<IActionResult> Login(LoginRequestDTO model)
    {
        if (string.IsNullOrWhiteSpace(model.NombreUsuario))
        {
            return Redirect("/Login?error=usuario");
        }

        var user = await _userManager.FindByNameAsync(model.NombreUsuario);
        if (user == null)
            return Redirect("/Login?error=usuario");

        var result = await _signInManager.PasswordSignInAsync(user, model.Contraseña, model.Recordarme, false);
        if (!result.Succeeded)
            return Redirect("/Login?error=credenciales");

        var roles = await _userManager.GetRolesAsync(user);
        var rol = roles.FirstOrDefault();
        Console.WriteLine($"Rol detectado: {rol}");
        return rol switch
        {
            "calidad" => Redirect("/gestiontiposcaracteristicas"),
            _ => Redirect("/GestionSAPClientes")
        };
    }

    [HttpGet("/AccessDenied")]
    public IActionResult AccessDenied()
    {
        return View();
    }

    [HttpGet("/authentication/logout")]
    public async Task<IActionResult> Logout()
    {
        await _signInManager.SignOutAsync();
        return Redirect("/Login?logout=1");
    }
}
