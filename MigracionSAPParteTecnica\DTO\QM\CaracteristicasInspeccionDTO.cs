﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;

namespace MigracionSAPParteTecnica.DTO.QM;
public class CaracteristicasInspeccionDTO
{
    public int Id { get; set; }

    public string Nombre { get; set; }

    public bool CopiarReferencia { get; set; }

    public string CampoBusqueda { get; set; }

    public int MetodoIns { get; set; }

    public bool CopiaCatalogo { get; set; }

    public bool Cuantitativa { get; set; }

    public bool Cualitativa { get; set; }

    public bool ToleranciaInf { get; set; }

    public bool ToleranciaSup { get; set; }

    public bool Categoria { get; set; }

    public int? NumeroDecimales { get; set; }

    public decimal? LimiteToleranciaInf { get; set; }

    public decimal? LimiteToleranciaSup { get; set; }

    public string TextoBreve { get; set; }

    public string UnidadMedida { get; set; }

    public bool Borrado { get; set; }

    public virtual MetodosInspeccionDTO CodTipoNavigation { get; set; }
}