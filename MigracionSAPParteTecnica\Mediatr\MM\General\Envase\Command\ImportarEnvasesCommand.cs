﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
public class ImportarEnvasesCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarEnvasesCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarEnvasesCommandHandler : IRequestHandler<ImportarEnvasesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarEnvasesCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarEnvasesCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new EnvasesDTO
                    {
                        TipoUso = worksheet.Cell(filaActual, 2).GetString().Trim(),
                        Nombre = worksheet.Cell(filaActual, 3).GetString().Trim(),
                        Descripcion = worksheet.Cell(filaActual, 4).GetString().Trim(),
                        PesoNeto = worksheet.Cell(filaActual, 5).GetValue<decimal>(),
                        PesoBruto = worksheet.Cell(filaActual, 6).GetValue<decimal>(),
                        Largo = worksheet.Cell(filaActual, 7).GetValue<decimal>(),
                        Ancho = worksheet.Cell(filaActual, 8).GetValue<decimal>(),
                        Altura = worksheet.Cell(filaActual, 9).GetValue<decimal>(),
                        Borrado = false                                                
                    };

                    if (string.IsNullOrWhiteSpace(dto.Nombre))
                    {
                        errores.Add($"Fila {filaActual}: El nombre no puede estar vacío.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.Envases
                        .FirstOrDefaultAsync(e =>
                            e.Nombre == dto.Nombre &&
                            e.TipoUso == dto.TipoUso, cancellationToken);

                    var clave = $"{dto.TipoUso}_{dto.Nombre}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.General.Envases>(dto);
                        await _context.Envases.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "Envases",
                            IdRegistro = entidad.Id,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        TinyMapper.Map(dto, existente);

                        await _context.SaveChangesAsync(cancellationToken);

                        var datosDespues = JsonSerializer.Serialize(existente);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "Envases",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = $"Descripción actualizada en fila {filaActual}"
                        }, cancellationToken);

                        actualizados++;
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar envases: {ex.Message}";
        }

        return result;
    }
}
