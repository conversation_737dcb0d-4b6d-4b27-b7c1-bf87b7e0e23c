﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Query;
public class GetAllMetodosInspeccionQuery : IRequest<ListResult<MetodosInspeccionDTO>>
{
}

internal class GetAllMetodosInspeccionQueryHandler : IRequestHandler<GetAllMetodosInspeccionQuery, ListResult<MetodosInspeccionDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllMetodosInspeccionQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MetodosInspeccionDTO>> Handle(GetAllMetodosInspeccionQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MetodosInspeccionDTO>
        {
            Data = new List<MetodosInspeccionDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listMetodos = await _migracionSAPParteTecnicaContext.MetodosInspeccion
                .AsNoTracking()
                .Where(l => l.Borrado == false)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<MetodosInspeccionDTO>>(listMetodos).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllMetodosInspeccionQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}