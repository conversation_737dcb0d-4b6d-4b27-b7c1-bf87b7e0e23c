﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;
public class ImportarDatosAuxMaterialesCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarDatosAuxMaterialesCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarDatosAuxMaterialesCommandHandler : IRequestHandler<ImportarDatosAuxMaterialesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarDatosAuxMaterialesCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarDatosAuxMaterialesCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var tipo = worksheet.Cell(filaActual, 2).GetString().Trim();
                    var medidas = worksheet.Cell(filaActual, 3).GetString().Trim();

                    var espesor = worksheet.Cell(filaActual, 4).GetValue<decimal>();
                    var ancho = worksheet.Cell(filaActual, 5).GetValue<decimal>();
                    var largo = worksheet.Cell(filaActual, 6).GetValue<decimal?>(); 
                    var scroll = worksheet.Cell(filaActual, 7).GetValue<decimal?>();

                    var entidad = new Entities.MigracionSAPParteTecnica.MM.BobinasPaquetes.DatosAuxMateriales
                    {
                        TipoMaterial = tipo,
                        Medidas = medidas,
                        EspesorReal = espesor,
                        AnchoReal = ancho,
                        LargoReal = largo,
                        LargoScroll = scroll,
                        Borrado = false
                    };

                    if (string.IsNullOrWhiteSpace(tipo) || string.IsNullOrWhiteSpace(medidas))
                    {
                        errores.Add($"Fila {filaActual}: TipoMaterial y Medidas no pueden estar vacíos.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.DatosAuxMateriales
                        .FirstOrDefaultAsync(d =>
                            d.TipoMaterial == tipo &&
                            d.Medidas == medidas, cancellationToken);

                    var clave = $"{tipo}_{medidas}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        await _context.DatosAuxMateriales.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "DatosAuxMateriales",
                            IdRegistro = entidad.Id,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);

                        existente.EspesorReal = espesor;
                        existente.AnchoReal = ancho;
                        existente.LargoReal = largo;
                        existente.LargoScroll = scroll;

                        await _context.SaveChangesAsync(cancellationToken);

                        var datosDespues = JsonSerializer.Serialize(existente);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "DatosAuxMateriales",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = $"Actualizado desde Excel (fila {filaActual})"
                        }, cancellationToken);

                        actualizados++;
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar DatosAuxMateriales: {ex.Message}";
        }

        return result;
    }
}