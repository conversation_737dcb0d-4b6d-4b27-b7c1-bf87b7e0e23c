﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;

public class GetTroquelByIdQuery : IRequest<SingleResult<TroquelesDTO>>
{
    public int Id { get; set; }
    public GetTroquelByIdQuery(int id)
    {
        Id = id;
    }
}

internal class GetTroquelByIdQueryHandler : IRequestHandler<GetTroquelByIdQuery, SingleResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetTroquelByIdQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<SingleResult<TroquelesDTO>> Handle(GetTroquelByIdQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<TroquelesDTO>
        {
            Data = null,
            Errors = new List<string>()
        };

        try
        {
            var troquel = await _migracionSAPParteTecnicaContext.Troqueles
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);
            if(troquel == null)
            {
                result.Errors.Add($"ERROR: No se encontró el troquel con ID {request.Id}");
            }
            else
            {
                result.Data = TinyMapper.Map<TroquelesDTO>(troquel);
            }
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetTroquelByIdQuery - {e.InnerException?.Message ?? e.Message}");
        }

        return result;
    }
}