﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editExcepcionPrecioLote?.Id > 0)
{
    <EntityLockManager EntityType="ExcepcionesPreciosLotes"
                       EntityId="editExcepcionPrecioLote.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar excepción de precio"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Esta excepción de precio está siendo editada por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editExcepcionPrecioLote" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editExcepcionPrecioLote" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código de barniz:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/waterdrop-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxComboBox Data="@MaterialesList"
                                        Value="MaterialSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((MaterialComboItem? selected) => OnMaterialChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" 
                                        ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Fecha:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-calendar" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxDateEdit @bind-Date="editExcepcionPrecioLote.Fecha" TimeSectionVisible="true" CssClass="cw-320" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Precio:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-currency-euro" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editExcepcionPrecioLote.Precio" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<ExcepcionesPreciosLotesDTO> OnSave { get; set; }
    [Parameter] public List<MaterialComboItem> MaterialesList { get; set; } = new();
    [Parameter] public MaterialComboItem? MaterialSeleccionado { get; set; }
    private ExcepcionesPreciosLotesDTO editExcepcionPrecioLote = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(ExcepcionesPreciosLotesDTO excepcionPrecioLote)
    {
        editExcepcionPrecioLote = excepcionPrecioLote;

        if (MaterialesList != null && MaterialesList.Any())
        {
            MaterialSeleccionado = MaterialesList
                .FirstOrDefault(c => c.Codigo == editExcepcionPrecioLote.CodigoBarniz);
        }

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    private void OnMaterialChanged(MaterialComboItem? selected)
    {
        MaterialSeleccionado = selected;

        if (selected != null)
        {
            editExcepcionPrecioLote.CodigoBarniz = selected.Codigo;
        }
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = ExcepcionesPreciosLotesValidator.Validar(editExcepcionPrecioLote);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editExcepcionPrecioLote);
        IsPopupVisible = false;
        ToastService.MostrarOk("Excepción de precio guardada correctamente.");
    }
}