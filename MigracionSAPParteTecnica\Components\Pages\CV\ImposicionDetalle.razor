﻿@page "/imposicion-detalle/{Id:int?}"
@attribute [Authorize(Roles = "logistica,preprint")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject IJSRuntime JS
@inject AuthenticationStateProvider AuthStateProvider
@inject IEntityLockService EntityLockService

<PageTitle>Cargas Principal</PageTitle>
<EntityLockManager @key="Id"
                   EntityType="Imposicion"
                   EntityId="Id"
                   OnLockStateChanged="OnLockStateChanged" />
<CustomLoadingPanel @bind-Visible="_isLoading" PositionTarget="#div-body" />
<!-- Botón de edición avanzada con separador -->
<div class="mb-3" style="display: flex; align-items: center;">
    <!-- Botón de edición avanzada -->
    <DxButton Text="@(EdicionAvanzadaHabilitada ? "Bloquear edición avanzada" : "Desbloquear edición avanzada")"
              IconCssClass="@(EdicionAvanzadaHabilitada ? "bi bi-lock-fill" : "bi bi-unlock")"
              RenderStyle="ButtonRenderStyle.Secondary"
              CssClass="rounded"
              Click="() => EdicionAvanzadaHabilitada = !EdicionAvanzadaHabilitada"
              Style="min-width: 220px"
              Enabled="!_lectura" />
    @if (EdicionAvanzadaHabilitada)
    {
        <span class="text-warning ms-3">
            <b>¡Atención!</b> Puedes editar campos clave. Hazlo solo si es imprescindible.
        </span>
    }
    <!-- Separador flexible -->
    <div style="flex: 1"></div>
    <!-- Aviso solo lectura a la derecha -->
    @if (_lectura)
    {
        <span class="d-flex align-items-center px-3 py-2"
              style="border: 1px solid #d1d5db; border-radius: 7px; background: #fff; font-size: 1.08em; color: #333; box-shadow: 0 1px 6px #00000008;">
            <i class="bi bi-eye me-2" style="font-size: 1.3em; color: #4a5969;"></i>
            <span>
                <b>Modo solo lectura activado:</b>
                La imposición está bloqueado por <b>@_lockUsuario</b> desde <b>@_lockFecha.ToString("g")</b>.
            </span>
        </span>
    }
</div>
<hr class="mb-4" style="margin-top: -8px;" />
@if (Imposicion != null)
{
    <DxFormLayout>
        <DxFormLayoutGroup Caption="Datos generales" ColSpanMd="12">
            <DxFormLayoutItem Caption="Id Imposición:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-hash" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.Id" ReadOnly="true" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-person" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="Imposicion.Cliente_SAP"
                                    Data="@ClientesList"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" Enabled="EdicionAvanzadaHabilitada" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Plano:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-vr" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="Imposicion.Plano" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;">
                            <Buttons>
                                <DxEditorButton IconCssClass="bi bi-file-earmark-pdf"
                                                ToolTip="Abrir plano PDF"
                                                Click="AbrirPlanoPdf"/>
                            </Buttons>
                        </DxTextBox>
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Id Troquel asociado:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-union" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@Imposicion.IdTroquel.ToString()" ReadOnly="true" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;">
                            <Buttons>
                                <DxEditorButton IconCssClass="bi bi-search"
                                                ToolTip="Buscar troquel"
                                                Enabled="EdicionAvanzadaHabilitada"
                                                Click="() => PopupTroquelVisible = true" />
                            </Buttons>
                        </DxTextBox>
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Activa:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <DxCheckBox CheckType="CheckType.Switch"
                                @bind-Checked="Imposicion.Activo"
                                LabelPosition="LabelPosition.Left"
                                Alignment="CheckBoxContentAlignment.SpaceBetween">
                    </DxCheckBox>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutGroup Caption="Dimensiones y estructura de la hoja" ColSpanMd="12">
            <DxFormLayoutItem Caption="Largo hoja:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrows-expand" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.LargoHoja" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Ancho hoja:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrows-expand-vertical" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.AnchoHoja" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Hoja Scroll:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-file-earmark-ruled" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox Data="@HojaScrollOpciones"
                                    @bind-Value="HojaScrollSeleccion"
                                    CssClass="popup-demo-textbox full-width"
                                    Style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Largo scroll:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-sort-up" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.LargoScroll" ReadOnly="@(Imposicion?.HojaScroll != true)" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Número alturas:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-stack" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.NumeroAlturas" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Número desarrollos:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-diagram-3" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.NumeroDesarrollos" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Número cuerpos:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-grid-3x3-gap" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit Value="CuerposCalculados" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutGroup Caption="Posicionamiento y sentido" ColSpanMd="12">
            <DxFormLayoutItem Caption="Sentido de lectura:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-left-right" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="Imposicion.SentidoLectura"
                                    Data="@sentidosLectura"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;"/>
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Arranque pinza:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-chevron-bar-up" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.ArranquePinza" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Arranque escuadra:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-chevron-bar-left" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.ArranqueEscuadra" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Posición escuadra invertida:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-repeat" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox Data="@PosicionEscuadraInvertidaOpciones"
                                    @bind-Value="PosicionEscuadraInvertidaSeleccion"
                                    CssClass="popup-demo-textbox full-width"
                                    Style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Posición escuadra ext:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-box-arrow-in-left" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="Imposicion.PosicionEscuadraExt"
                                    Data="@PosicionEscuadraExtOpciones"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width"
                                    Style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Pinza:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-tablet-landscape" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Imposicion.Pinza" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutItem ColSpanMd="12">
            <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
                @if (!_lectura)
                {
                    <DxButton Text="Guardar imposición"
                              RenderStyle="ButtonRenderStyle.Primary"
                              Click="OnGuardarImposicion"
                              Enabled="!_isLoading && !_lectura"
                              style="background-color: #28a745; color: white; border-color: #28a745;" />
                    <DxButton Text="Cancelar"
                              RenderStyle="ButtonRenderStyle.Primary"
                              Click="OnCancelar"
                              Enabled="!_isLoading"
                    style="background-color: #dc3545; color: white; border-color: #dc3545;" />
                }
                else
                {
                    <DxButton Text="Volver"
                              RenderStyle="ButtonRenderStyle.Info"
                              Click="OnCancelar"
                              Enabled="!_isLoading"
                              CssClass="boton-azul-oscuro" />
                }      
            </div>
        </DxFormLayoutItem>
    </DxFormLayout>
}
<SelectorTroquel Visible="@PopupTroquelVisible"
                 VisibleChanged="@(v => PopupTroquelVisible = v)"
                 OnSeleccionado="OnTroquelSeleccionado" />

@code{
    [Parameter] public int Id { get; set; }

    bool _isLoading;
    private ImposicionesDTO Imposicion;
    List<DropDownWrapperString> sentidosLectura = new();
    private List<SAPClienteDTO> clientesList = new();
    private string clienteNombreConcatenado;
    private string ClienteNombreConcatenado => clienteNombreConcatenado;
    private string HojaScrollTexto => Imposicion?.HojaScroll == true ? "Sí" : "No";
    private List<string> HojaScrollOpciones => new() { "Sí", "No" };
    private List<string> PosicionEscuadraInvertidaOpciones => new() { "Sí", "No" };
    private string HojaScrollSeleccion
    {
        get => Imposicion?.HojaScroll == true ? "Sí" : "No";
        set
        {
            Imposicion.HojaScroll = value == "Sí";
            if (Imposicion.HojaScroll == false)
            {
                Imposicion.LargoScroll = null;
            }
        }
    }
    private string PosicionEscuadraInvertidaSeleccion
    {
        get => Imposicion?.PosicionEscuadraInv == true ? "Sí" : "No";
        set
        {
            Imposicion.PosicionEscuadraInv = value == "Sí";
        }
    }
    private int CuerposCalculados =>
        (Imposicion?.NumeroAlturas ?? 0) * (Imposicion?.NumeroDesarrollos ?? 0);
    private List<DropDownWrapper> PosicionEscuadraExtOpciones => new()
    {
        new DropDownWrapper { ID = 2, Nombre = "2 - Lado derecho" },
        new DropDownWrapper { ID = 3, Nombre = "3 - Lado izquierdo" }
    };
    bool _lectura = false;
    string _lockUsuario;
    DateTime _lockFecha;
    bool EdicionAvanzadaHabilitada = false;
    public List<DropDownWrapper> ClientesList { get; set; } = new();
    private List<SAPClienteDTO> _clientesFullList;
    private bool PopupTroquelVisible = false;
    private DotNetObjectReference<ImposicionDetalle>? dotNetRef;


    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarClientes();
        await CargarSentidosLectura();

        if (Id > 0)
            await CargarImposicionAsync(Id);
        else
            Imposicion = new ImposicionesDTO();

        _isLoading = false;
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            dotNetRef = DotNetObjectReference.Create(this);
            await JS.InvokeVoidAsync("signalRHelper.start", dotNetRef);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (dotNetRef != null)
        {
            await JS.InvokeVoidAsync("signalRHelper.stop");
            dotNetRef.Dispose();
        }
    }

    [JSInvokable("OnTroquelModificado")]
    public async Task OnTroquelModificado(string data)
    {
        var evento = JsonSerializer.Deserialize<ImposicionModificadaEvent>(data, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        if (evento != null)
        {
            ToastService.MostrarInfo($"El usuario {evento.Usuario} ha modificado el troquel {evento.Id} (cliente: {evento.Cliente})");
        }
    }

    [JSInvokable("OnImposicionModificada")]
    public async Task OnImposicionModificada(string data)
    {
        var evento = JsonSerializer.Deserialize<ImposicionModificadaEvent>(data, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        if (evento != null)
        {
            ToastService.MostrarInfo($"El usuario {evento.Usuario} ha modificado la imposición {evento.Id} (plano: {evento.Plano}, cliente: {evento.Cliente})");
        }
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _lockUsuario = estado.Usuario;
        _lockFecha = estado.Fecha;
        StateHasChanged();
    }

    private async Task CargarSentidosLectura()
    {
        var sentidos = EnumHelper.GetEnumNamesAndDescriptions<Enums.SentidosLectura>();
        sentidosLectura = sentidos.Select(x => new DropDownWrapperString
        {
            ID = x.Name,
            Nombre = $"{x.Name} - {x.Description}"
        }).ToList();
    }


    public async Task CargarClientes()
    {
        var resultado = await MigracionSAPParteTecnicaService.GetAllSAPClientes();

        if (resultado != null && resultado.Data != null)
        {
            _clientesFullList = resultado.Data.ToList();

            ClientesList = resultado.Data
                .OrderBy(c => c.Codigo_SAP)
                .Select(c => new DropDownWrapper
                {
                    ID = c.Codigo_SAP,
                    Nombre = $"{c.Codigo_SAP}, {c.Nombre}"
                })
                .ToList();
        }
    }

    private async Task CargarImposicionAsync(int id)
    {
        var result = await MigracionSAPParteTecnicaService.GetImposicionById(id);
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
            NavigationManager.NavigateTo("/GestionTroqueles");
        }
        else
        {
            Imposicion = result.Data;
            clienteNombreConcatenado = GetClienteNombreConcatenado();
        }
    }

    private async Task OnGuardarImposicion()
    {
        if (Imposicion == null)
            return;

        var error = ImposicionValidator.Validar(Imposicion);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        try
        {
            _isLoading = true;

            Imposicion.Cuerpos = CuerposCalculados;

            var result = await MigracionSAPParteTecnicaService.TratarImposicion(Imposicion);
            if (result.Errors.Any())
            {
                ToastService.MostrarError(result.Errors.First());
            }
            else
            {
                ToastService.MostrarOk("Imposición actualizada correctamente.");
                NavigationManager.NavigateTo("/GestionImposiciones");
            }
        }
        catch (Exception ex)
        {
            ToastService.MostrarError("Error al guardar imposiciones: " + ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private string GetClienteNombreConcatenado()
    {
        if (Imposicion?.Cliente_SAP == null || clientesList == null)
            return string.Empty;

        var cliente = clientesList.FirstOrDefault(c => c.Codigo_SAP == Imposicion.Cliente_SAP.Value);
        return cliente != null ? $"{cliente.Codigo_SAP} - {cliente.Nombre}" : string.Empty;
    }

    private async Task AbrirPlanoPdf()
    {
        if (Imposicion?.Cliente_SAP is null || string.IsNullOrEmpty(Imposicion.Plano))
        {
            ToastService.MostrarError("No se puede abrir el plano. Cliente o nombre del plano no válidos.");
            return;
        }

        var cliente = clientesList.FirstOrDefault(c => c.Codigo_SAP == Imposicion.Cliente_SAP.Value);

        if (cliente == null)
        {
            ToastService.MostrarError("Cliente no encontrado.");
            return;
        }

        var codigoIn2 = cliente.Codigo_IN2;
        var nombrePlano = Imposicion.Plano;
        var baseUrl = NavigationManager.BaseUri.TrimEnd('/');
        var url = $"{baseUrl}/api/planos/{codigoIn2}/{nombrePlano}";

        try
        {
            using var httpClient = new HttpClient();
            var headRequest = new HttpRequestMessage(HttpMethod.Head, url);
            var headResponse = await httpClient.SendAsync(headRequest);

            if (headResponse.IsSuccessStatusCode)
            {
                await JS.InvokeVoidAsync("open", url, "_blank");
            }
            else
            {
                ToastService.MostrarError("Plano no encontrado.");
            }
        }
        catch
        {
            ToastService.MostrarError("Error al comprobar el estado del plano.");
        }
    }

    private async Task OnCancelar()
    {
        var user = (await AuthStateProvider.GetAuthenticationStateAsync()).User;
        string usuario = user.Identity?.Name ?? "usuario-desconocido";
        await EntityLockService.UnlockAsync("Imposicion", Id, usuario);
        NavigationManager.NavigateTo("/GestionImposiciones");
    }

    private async Task OnTroquelSeleccionado(TroquelesDTO troquel)
    {
        if (troquel != null)
            Imposicion.IdTroquel = troquel.Id;
    }
}