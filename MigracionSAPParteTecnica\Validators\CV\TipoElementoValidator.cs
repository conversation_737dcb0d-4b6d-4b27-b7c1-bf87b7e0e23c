﻿using MigracionSAPParteTecnica.DTO.CV;

namespace MigracionSAPParteTecnica.Validators.CV;

public class TipoElementoValidator
{
    public static string? Validar(TipoElementoDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Codigo))
            return "El código no puede estar vacío.";

        if (dto.Codigo.Length > 3)
            return "El código no puede tener más de 3 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.Descripcion))
            return "La descripción no puede estar vacía.";

        if (dto.Descripcion.Length > 200)
            return "La descripción no puede tener más de 200 caracteres.";

        return null;
    }
}
