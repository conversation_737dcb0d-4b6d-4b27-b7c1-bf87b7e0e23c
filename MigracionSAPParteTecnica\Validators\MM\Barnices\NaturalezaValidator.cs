﻿using MigracionSAPParteTecnica.DTO.MM.Barnices;

namespace MigracionSAPParteTecnica.Validators.MM.Barnices;
public static class NaturalezaValidator
{
    public static string? Validar(NaturalezaDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.DatoIn2))
            return "Debe indicar el dato IN2.";

        if (string.IsNullOrWhiteSpace(dto.DatoSap))
            return "Debe indicar el dato SAP.";

        if (dto.DatoSap.Length > 3)
            return "El dato SAP no puede tener más de 3 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.DescripcionSap))
            return "Debe indicar la descripción SAP.";

        return null;
    }
}