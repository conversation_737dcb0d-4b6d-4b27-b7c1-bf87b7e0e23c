﻿@page "/GestionMaestro"
@attribute [Authorize(Roles = "preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject ModularBlockingService BlockingService

<CustomLoadingPanel @bind-Visible="_isLoading" Mensaje="@MensajeBloqueo()"/>
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" ColumnResizeMode="GridColumnResizeMode.NextColumn"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm" HighlightRowOnHover="true"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridDataColumn FieldName="Motivo" FixedPosition="GridColumnFixedPosition.Left" Caption="Motivo" Width="90px" />
        <DxGridDataColumn FieldName="PedidoEjemplo" FixedPosition="GridColumnFixedPosition.Left" Caption="Pedido" Width="90px" DisplayFormat="0" />
        <DxGridDataColumn FieldName="Cliente_IN2" Caption="Cliente_IN2" Width="130px" />
        <DxGridDataColumn FieldName="Cliente_SAP" Caption="Cliente_SAP" Width="130px"/>
        <DxGridDataColumn FieldName="RefMotivoCliente" Caption="Referencia motivo cliente" Width="220px" />
        <DxGridDataColumn FieldName="Marca" Caption="Marca" Width="150px" />
        <DxGridDataColumn FieldName="Descrip" Caption="Descrip" Width="300px" />
        <DxGridDataColumn FieldName="TipoProducto" Caption="Tipo Producto" Width="140px" />
        <DxGridDataColumn FieldName="Embuticion" Caption="EmbuticionTexto" Width="120px" />
        <DxGridDataColumn FieldName="Fotolito" Caption="Fotolito" Width="120px" />
        <DxGridDataColumn FieldName="PruebaFisica" Caption="Prueba Fisica" Width="140px" />
        <DxGridDataColumn FieldName="Gtin" Caption="Gtin" Width="130px" />
        <DxGridDataColumn FieldName="TipoFlejado" Caption="Tipo Flejado" Width="130px" />
        <DxGridDataColumn FieldName="Producto" Caption="Producto" Width="120px" />
        <DxGridDataColumn FieldName="Plano" Caption="Plano" Width="120px" />
        <DxGridDataColumn FieldName="NumeroCuerpos" Caption="Número de cuerpos" Width="190px" />
        <DxGridDataColumn FieldName="LargoHoja" Caption="Largo Hoja" Width="130px" />
        <DxGridDataColumn FieldName="AnchoHoja" Caption="Ancho Hoja" Width="130px" />
        <DxGridDataColumn FieldName="Tratamiento" Caption="Tratamiento" Width="450px" />
    </Columns>
    <ToolbarTemplate>
        <DxToolbar>
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar maestro" CssClass="ms-3 rounded"
                           IconCssClass="bi bi-arrow-clockwise" Click="ActualizarMaestro" Alignment="ToolbarItemAlignment.Right" />
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Warning" Text="Ver incompletos" Alignment="ToolbarItemAlignment.Right"
                           IconCssClass="bi bi-exclamation-triangle-fill" Click="OnVerIncompletos" CssClass="ms-3 rounded" />
        </DxToolbar>
    </ToolbarTemplate>
</DxGrid>

@code{
    bool _isLoading { get; set; }
    DxGrid? Grid;
    ObservableCollection<MaestroDTO> Data = new();
    string GridSearchText = "";
    private DotNetObjectReference<GestionMaestro>? dotNetRef;
    bool _bloqueado;
    string? _usuarioBloqueador;
    string? _motivoBloqueo;

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        InicializarBloqueoMaestro();
        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllMaestro();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<MaestroDTO>(result.Data);
            StateHasChanged();
        }
    }

    private void InicializarBloqueoMaestro()
    {
        BlockingService.OnModuleStateChanged += EstadoModuloActualizado;
        var estado = BlockingService.GetState("CV_Maestro");
        _bloqueado = estado.IsBlocked;
        _usuarioBloqueador = estado.Usuario;
        _motivoBloqueo = estado.Motivo;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (MaestroDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    private string MensajeBloqueo()
    {
        if (_bloqueado)
        {
            return $"El usuario {_usuarioBloqueador} está actualizando maestro: {_motivoBloqueo}";
        }
        return "Cargando...";
    }

    private void EstadoModuloActualizado(string modulo, ModularBlockingService.BlockingState estado)
    {
        if (modulo == "CV_Maestro")
        {
            _bloqueado = estado.IsBlocked;
            _usuarioBloqueador = estado.Usuario;
            _motivoBloqueo = estado.Motivo;
            _isLoading = _bloqueado;
            InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        _isLoading = false;
        _bloqueado = false;
        BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;
    }

    public async Task ActualizarMaestro()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await ServinService.ActualizarMaestro();

        if (result.Success)
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
        }
        else
        {
            ToastService.MostrarError($"Error al actualizar maestro: {result.Message}");
        }

        _isLoading = false;
    }

    private async Task OnVerIncompletos()
    {
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.GetMaestroIncompletos();
        if (result.Errors.Any())
        {
            ToastService.MostrarError("Error al obtener maestro incompleto.");
        }
        else
        {
            Data = new ObservableCollection<MaestroDTO>(result.Data);
            ToastService.MostrarInfo($"Se han cargado {Data.Count} maestro incompletos.");
        }

        _isLoading = false;
    }
}