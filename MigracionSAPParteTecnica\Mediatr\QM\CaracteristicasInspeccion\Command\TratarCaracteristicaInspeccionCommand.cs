﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;

public class TratarCaracteristicaInspeccionCommand : IRequest<SingleResult<CaracteristicasInspeccionDTO>>
{
    public CaracteristicasInspeccionDTO CaracteristicasInspeccionDTO { get; set; }

    public TratarCaracteristicaInspeccionCommand(CaracteristicasInspeccionDTO caracteristicasInspeccionDTO)
    {
        CaracteristicasInspeccionDTO = caracteristicasInspeccionDTO;
    }
}

internal class TratarCaracteristicaInspeccionCommandHandler : IRequestHandler<TratarCaracteristicaInspeccionCommand, SingleResult<CaracteristicasInspeccionDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarCaracteristicaInspeccionCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<CaracteristicasInspeccionDTO>> Handle(TratarCaracteristicaInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<CaracteristicasInspeccionDTO>
        {
            Data = new CaracteristicasInspeccionDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.CaracteristicasInspeccionDTO;

            var existing = await _context.CaracteristicasInspeccion
                .FirstOrDefaultAsync(c => c.Nombre == dto.Nombre, cancellationToken);

            if (existing == null)
            {
                // Alta
                var nueva = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.CaracteristicasInspeccion>(dto);

                nueva.Borrado = false;

                await _context.CaracteristicasInspeccion.AddAsync(nueva, cancellationToken);
                await _context.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<CaracteristicasInspeccionDTO>(nueva);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "QM_CaracteristicasInspeccion",
                    IdRegistro = nueva.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nueva),
                    Comentarios = $"Alta de característica de inspección: Id = '{nueva.Id}'"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _context.CaracteristicasInspeccion.Update(existing);
                await _context.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<CaracteristicasInspeccionDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "QM_CaracteristicasInspeccion",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de característica de inspección: Id = '{existing.Id}'"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarCaracteristicaInspeccion - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}