﻿@page "/troquel-detalle/{Id:int?}"
@attribute [Authorize(Roles = "logistica,preprint")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject IJSRuntime JS
@inject AuthenticationStateProvider AuthStateProvider
@inject IEntityLockService EntityLockService

<PageTitle>Cargas Principal</PageTitle>
<EntityLockManager @key="Id"
                   EntityType="Troquel"
                   EntityId="Id"
                   OnLockStateChanged="OnLockStateChanged" />
<CustomLoadingPanel @bind-Visible="_isLoading" PositionTarget="#div-body" />
<!-- Botón de edición avanzada con separador -->
<div class="mb-3" style="display: flex; align-items: center;">
    <!-- Botón de edición avanzada -->
    <DxButton Text="@(EdicionAvanzadaHabilitada ? "Bloquear edición avanzada" : "Desbloquear edición avanzada")"
              IconCssClass="@(EdicionAvanzadaHabilitada ? "bi bi-lock-fill" : "bi bi-unlock")"
              RenderStyle="ButtonRenderStyle.Secondary"
              CssClass="rounded"
              Click="() => EdicionAvanzadaHabilitada = !EdicionAvanzadaHabilitada"
              Style="min-width: 220px"
              Enabled="!_lectura" />
    @if (EdicionAvanzadaHabilitada)
    {
        <span class="text-warning ms-3">
            <b>¡Atención!</b> Puedes editar campos clave. Hazlo solo si es imprescindible.
        </span>
    }
    <!-- Separador flexible -->
    <div style="flex: 1"></div>
    <!-- Aviso solo lectura a la derecha -->
    @if (_lectura)
    {
        <span class="d-flex align-items-center px-3 py-2"
              style="border: 1px solid #d1d5db; border-radius: 7px; background: #fff; font-size: 1.08em; color: #333; box-shadow: 0 1px 6px #00000008;">
            <i class="bi bi-eye me-2" style="font-size: 1.3em; color: #4a5969;"></i>
            <span>
                <b>Modo solo lectura activado:</b>
                El troquel está bloqueado por <b>@_lockUsuario</b> desde <b>@_lockFecha.ToString("g")</b>.
            </span>
        </span>
    }
</div>
<hr class="mb-4" style="margin-top: -8px;" />
@if (Troquel != null)
{
    <DxFormLayout>
        <DxFormLayoutGroup Caption="Datos generales" ColSpanMd="12" CssClass="custom-group">
            <DxFormLayoutItem Caption="Id Troquel:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-hash" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.Id" ReadOnly="true" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-person" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="Troquel.Cliente_SAP"
                                    Data="@ClientesList"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" Enabled="EdicionAvanzadaHabilitada" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Fecha:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-calendar-date" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@(Troquel.FechaAlta?.ToString("dd/MM/yyyy") ?? "")" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Activo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <DxCheckBox CheckType="CheckType.Switch"
                                @bind-Checked="Troquel.Activo"
                                LabelPosition="LabelPosition.Left"
                                Alignment="CheckBoxContentAlignment.SpaceBetween">
                    </DxCheckBox>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Tipo de elemento:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-box-seam" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                        <DxComboBox @bind-Value="Troquel.TipoElemento"
                                    Data="@tiposElementoList"
                                    ValueFieldName="ID"
                                    TextFieldName="Nombre"
                                    CssClass="popup-demo-textbox full-width" style="flex-grow: 1;"
                                    SelectedDataItemChanged="@(async (SelectedDataItemChangedEventArgs<DropDownWrapperString> args) => await OnTipoElementoChanged(args))" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Familia:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-diagram-2" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="Troquel.Familia" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Subfamilia:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-diagram-3" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="Troquel.Subfamilia" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutGroup Caption="Formatos y descripción" ColSpanMd="8">
            <DxFormLayoutItem Caption="Formato IN2:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="Troquel.IdFormatoIn2" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Formato envase cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-textarea" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@Troquel.FormatoEnvase" CssClass="cw-480" style="flex-grow: 1;" TextChanged="@(async (string newText) => { await OnFormatoEnvaseChanged(newText); })" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Descripción:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi-chat-left-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@DescripcionExtendida" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Observaciones:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-chat-right" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxMemo @bind-Text="Troquel.Observaciones" Rows="3" CssClass="cw-480" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutGroup Caption="Ayuda de búsqueda" ColSpanMd="4">
            <DxFormLayoutItem Caption="Pedido ejemplo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-card-checklist" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@(Troquel.PedidoEjemplo.ToString())" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Plano ejemplo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-file-earmark-pdf" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@Troquel.PlanoEjemplo" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;">
                            <Buttons>
                                <DxEditorButton IconCssClass="bi bi-file-earmark-pdf"
                                                ToolTip="Abrir plano PDF"
                                                Click="AbrirPlanoPdf" />
                            </Buttons>
                        </DxTextBox>
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Tipo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                <Template Context="ItemContext">
                    <div style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-tags" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox Text="@Troquel.Tipo" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutGroup Caption="Medidas y reservas" ColSpanMd="12">
            <DxFormLayoutItem Caption="Embutición:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-funnel-fill" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxTextBox @bind-Text="EmbuticionTexto" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Desarrollo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrows-expand-vertical" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.Desarrollo" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Diámetro real:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-repeat" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.DiametroReal" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Diámetro envase cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-dash-circle-dotted" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit Value="Troquel.DiametroEnvase" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ValueChanged="EventCallback.Factory.Create<decimal?>(this, OnDiametroEnvaseChanged)" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Altura elemento:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrows-expand" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.AlturaElemento" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="!EdicionAvanzadaHabilitada" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Altura envase cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrows-vertical" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit Value="Troquel.AlturaEnvase" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ValueChanged="EventCallback.Factory.Create<decimal?>(this, OnAlturaEnvaseChanged)" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Reservas izquierda:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-left" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.ReservasIzquierda" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Reservas derecha:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-right" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.ReservasDerecha" ReadOnly="!EdicionAvanzadaHabilitada" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Reservas superiores:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-up" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.ReservasSuperiores" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Reservas inferiores:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-arrow-down" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.ReservasInferiores" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Eje mayor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-gear-wide-connected" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.EjeMayor" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
            <DxFormLayoutItem Caption="Eje menor:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                <Template Context="ItemContext">
                    <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                        <i class="bi bi-gear-wide" style="margin-right: 10px; font-size: 1.2em;"></i>
                        <DxSpinEdit @bind-Value="Troquel.EjeMenor" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                    </div>
                </Template>
            </DxFormLayoutItem>
        </DxFormLayoutGroup>
        <DxFormLayoutItem ColSpanMd="12">
            <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
                @if (!_lectura)
                {
                    <DxButton Text="Guardar troquel"
                    RenderStyle="ButtonRenderStyle.Primary"
                    Click="OnGuardarTroquel"
                    Enabled="!_isLoading"
                    style="background-color: #28a745; color: white; border-color: #28a745;" />
                    <DxButton Text="Cancelar"
                    RenderStyle="ButtonRenderStyle.Primary"
                    Click="OnCancelar"
                    Enabled="!_isLoading"
                    style="background-color: #dc3545; color: white; border-color: #dc3545;" />
                }
                else
                {
                    <DxButton Text="Volver"
                              RenderStyle="ButtonRenderStyle.Info"
                              Click="OnCancelar"
                              Enabled="!_isLoading"
                              CssClass="boton-azul-oscuro" />
                }
            </div>
        </DxFormLayoutItem>
</DxFormLayout>
}

@code {
    [Parameter] public int Id { get; set; }
    bool _isLoading;
    private TroquelesDTO Troquel;
    List<DropDownWrapperString> tiposElementoList = new();
    private DropDownWrapperString? SelectedTipoElemento;
    private string EmbuticionTexto
    {
        get => Troquel?.Embuticion == true ? "Sí" : "No";
        set
        {
            if (Troquel != null)
                Troquel.Embuticion = value?.ToLower() == "sí";
        }
    }
    private string clienteNombreConcatenado;
    private string ClienteNombreConcatenado => clienteNombreConcatenado;
    private string DescripcionExtendida => _descripcionExtendida;
    private string _descripcionExtendida = string.Empty;
    bool _lectura = false;
    string _lockUsuario;
    DateTime _lockFecha;
    bool EdicionAvanzadaHabilitada = false;
    private EditContext _editContext;
    public List<DropDownWrapper> ClientesList { get; set; } = new();
    private List<SAPClienteDTO> _clientesFullList;
    private DotNetObjectReference<TroquelDetalle>? dotNetRef;

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarTiposElementoAsync();
        await CargarClientes();

        if (Id > 0)
            await CargarTroquelAsync(Id);
        else
            Troquel = new TroquelesDTO();

        _isLoading = false;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            dotNetRef = DotNetObjectReference.Create(this);
            await JS.InvokeVoidAsync("signalRHelper.start", dotNetRef);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (dotNetRef != null)
        {
            await JS.InvokeVoidAsync("signalRHelper.stop");
            dotNetRef.Dispose();
        }
    }

    [JSInvokable("OnTroquelModificado")]
    public async Task OnTroquelModificado(string data)
    {
        var evento = JsonSerializer.Deserialize<ImposicionModificadaEvent>(data, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        if (evento != null)
        {
            ToastService.MostrarInfo($"El usuario {evento.Usuario} ha modificado el troquel {evento.Id} (cliente: {evento.Cliente})");
        }
    }

    [JSInvokable("OnImposicionModificada")]
    public async Task OnImposicionModificada(string data)
    {
        var evento = JsonSerializer.Deserialize<ImposicionModificadaEvent>(data, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        if (evento != null)
        {
            ToastService.MostrarInfo($"El usuario {evento.Usuario} ha modificado la imposición {evento.Id} (plano: {evento.Plano}, cliente: {evento.Cliente})");
        }
    }


    private void OnLockStateChanged(LockResult estado)
    {
        if (estado == null)
        {
            _lectura = false;
            _lockUsuario = "desconocido";
            _lockFecha = DateTime.MinValue;
            StateHasChanged();
            return;
        }

        _lectura = estado.Lectura;
        _lockUsuario = estado.Usuario;
        _lockFecha = estado.Fecha;
        StateHasChanged();
    }

    private async Task CargarTiposElementoAsync()
    {
        var tipos = EnumHelper.GetEnumNamesAndDescriptions<Enums.TiposElemento>();
        tiposElementoList = tipos.Select(x => new DropDownWrapperString
        {
            ID = x.Name,
            Nombre = $"{x.Name} - {x.Description}"
        }).ToList();
    }

    private async Task CargarTroquelAsync(int id)
    {
        var result = await MigracionSAPParteTecnicaService.GetTroquelById(id);
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
            NavigationManager.NavigateTo("/GestionTroqueles");
        }
        else
        {
            Troquel = result.Data;
            ActualizarDescripcionExtendida();
        }
    }

    private async Task OnTipoElementoChanged(SelectedDataItemChangedEventArgs<DropDownWrapperString> args)
    {
        if (args.ChangeSource == SelectionChangeSource.UserAction)
        {
            var selectedItem = args.DataItem;
            if (selectedItem != null)
            {
                Troquel ??= new TroquelesDTO();

                Troquel.TipoElemento = selectedItem.ID;
                Troquel.Familia = selectedItem.ID.Substring(0, 1);
                Troquel.Subfamilia = selectedItem.ID.Length > 1
                    ? selectedItem.ID.Substring(1)
                    : string.Empty;

                ActualizarDescripcionExtendida();
                StateHasChanged();
            }
        }
        await Task.CompletedTask;
    }

    private async Task OnGuardarTroquel()
    {
        if (Troquel == null)
            return;

        var error = TroquelValidator.Validar(Troquel);
        if (!string.IsNullOrEmpty(error))
        {
            ToastService.MostrarError(error);
            return;
        }

        try
        {
            _isLoading = true;
            var result = await MigracionSAPParteTecnicaService.TratarTroquel(Troquel);

            if (result.Errors.Any())
            {
                ToastService.MostrarError(result.Errors.First());
            }
            else
            {
                ToastService.MostrarOk("Troquel actualizado correctamente.");
                NavigationManager.NavigateTo("/GestionTroqueles");
            }
        }
        catch (Exception ex)
        {
            ToastService.MostrarError("Error al guardar troquel: " + ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    public async Task CargarClientes()
    {
        var resultado = await MigracionSAPParteTecnicaService.GetAllSAPClientes();

        if (resultado != null && resultado.Data != null)
        {
            _clientesFullList = resultado.Data.ToList();

            ClientesList = resultado.Data
                .OrderBy(c => c.Codigo_SAP)
                .Select(c => new DropDownWrapper
                {
                    ID = c.Codigo_SAP,
                    Nombre = $"{c.Codigo_SAP}, {c.Nombre}"
                })
                .ToList();
        }
    }

    private void OnDatosDescripcionChanged(decimal? value)
    {
        StateHasChanged();
    }

    private string GetDescripcionExtendida()
    {
        if (Troquel == null)
            return string.Empty;

        var tipoElementoDesc = tiposElementoList
            .FirstOrDefault(t => t.ID == Troquel.TipoElemento)?.Nombre ?? Troquel.TipoElemento;

        var diametroEnvase = Troquel.DiametroEnvase.HasValue && Troquel.DiametroEnvase.Value != 0
            ? Troquel.DiametroEnvase.Value.ToString("0.##")
            : "";

        var alturaEnvase = Troquel.AlturaEnvase.HasValue && Troquel.AlturaEnvase.Value != 0
            ? Troquel.AlturaEnvase.Value.ToString("0.##")
            : "";

        var formatoEnvase = !string.IsNullOrWhiteSpace(Troquel.FormatoEnvase) &&
                           Troquel.FormatoEnvase.ToUpper().Contains("CUERPO")
            ? ""
            : Troquel.FormatoEnvase ?? "";

        if (!string.IsNullOrEmpty(alturaEnvase))
        {
            return $"{tipoElementoDesc} {diametroEnvase} x {alturaEnvase} {formatoEnvase}".Trim();
        }
        else
        {
            return $"{tipoElementoDesc} {diametroEnvase} {formatoEnvase}".Trim();
        }
    }

    private Task OnFormatoEnvaseChanged(string newValue)
    {
        ActualizarCamposYDescripcion(formatoEnvase: newValue);
        return Task.CompletedTask;
    }

    private Task OnDiametroEnvaseChanged(decimal? newValue)
    {
        ActualizarCamposYDescripcion(diametroEnvase: newValue);
        return Task.CompletedTask;
    }

    private Task OnAlturaEnvaseChanged(decimal? newValue)
    {
        ActualizarCamposYDescripcion(alturaEnvase: newValue);
        return Task.CompletedTask;
    }

    private void ActualizarCamposYDescripcion(string? formatoEnvase = null, decimal? diametroEnvase = null, decimal? alturaEnvase = null)
    {
        if (formatoEnvase != null)
            Troquel.FormatoEnvase = formatoEnvase;

        if (diametroEnvase.HasValue)
            Troquel.DiametroEnvase = diametroEnvase;

        if (alturaEnvase.HasValue)
            Troquel.AlturaEnvase = alturaEnvase;

        ActualizarDescripcionExtendida();
        StateHasChanged();
    }

    private async Task OnCancelar()
    {
        var user = (await AuthStateProvider.GetAuthenticationStateAsync()).User;
        string usuario = user.Identity?.Name ?? "usuario-desconocido";
        await EntityLockService.UnlockAsync("Imposicion", Id, usuario);
        NavigationManager.NavigateTo("/GestionTroqueles");
    }

    private void ActualizarDescripcionExtendida()
    {
        if (Troquel == null)
        {
            _descripcionExtendida = string.Empty;
            return;
        }

        var tipoElementoDesc = tiposElementoList
            .FirstOrDefault(t => t.ID == Troquel.TipoElemento)?.Nombre ?? Troquel.TipoElemento;

        var diametroEnvase = Troquel.DiametroEnvase.HasValue && Troquel.DiametroEnvase.Value != 0
            ? Troquel.DiametroEnvase.Value.ToString("0.##")
            : "";

        var alturaEnvase = Troquel.AlturaEnvase.HasValue && Troquel.AlturaEnvase.Value != 0
            ? Troquel.AlturaEnvase.Value.ToString("0.##")
            : "";

        var formatoEnvase = !string.IsNullOrWhiteSpace(Troquel.FormatoEnvase) &&
                           Troquel.FormatoEnvase.ToUpper().Contains("CUERPO")
            ? ""
            : Troquel.FormatoEnvase ?? "";

        if (!string.IsNullOrEmpty(alturaEnvase))
        {
            _descripcionExtendida = $"{tipoElementoDesc} {diametroEnvase} x {alturaEnvase} {formatoEnvase}".Trim();
        }
        else
        {
            _descripcionExtendida = $"{tipoElementoDesc} {diametroEnvase} {formatoEnvase}".Trim();
        }
    }

    private async Task AbrirPlanoPdf()
    {
        if (Troquel?.Cliente_SAP is null || string.IsNullOrEmpty(Troquel.PlanoEjemplo))
        {
            ToastService.MostrarError("No se puede abrir el plano. Cliente o nombre del plano no válidos.");
            return;
        }

        var cliente = _clientesFullList.FirstOrDefault(c => c.Codigo_SAP == Troquel.Cliente_SAP.Value);

        if (cliente == null)
        {
            ToastService.MostrarError("Cliente no encontrado.");
            return;
        }

        var codigoIn2 = cliente?.Codigo_IN2;
        var nombrePlano = Troquel.PlanoEjemplo;
        var baseUrl = NavigationManager.BaseUri.TrimEnd('/');
        var url = $"{baseUrl}/api/planos/{codigoIn2}/{nombrePlano}";

        try
        {
            using var httpClient = new HttpClient();
            var headRequest = new HttpRequestMessage(HttpMethod.Head, url);
            var response = await httpClient.SendAsync(headRequest);

            if (response.IsSuccessStatusCode)
            {
                await JS.InvokeVoidAsync("open", url, "_blank");
            }
            else
            {
                ToastService.MostrarError("Plano no encontrado.");
            }
        }
        catch
        {
            ToastService.MostrarError("Error al comprobar el estado del plano.");
        }
    }
}