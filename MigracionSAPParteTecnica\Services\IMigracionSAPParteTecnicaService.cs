﻿using MediatR;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.MM.Tintas;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Query;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Query;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Query;
using MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
using MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Query;

namespace MigracionSAPParteTecnica.Services;
public interface IMigracionSAPParteTecnicaService
{
    Task<ListResult<TroquelesDTO>> GetAllTroqueles();

    Task<ListResult<PlanoDTO>> GetAllPlanos();

    Task<SingleResult<bool>> DeletePlanoCommand(int cliente, string nombrePlano);

    Task<SingleResult<PlanoDTO>> TratarPlano(PlanoDTO planoDTO);

    Task<SingleResult<TroquelesDTO>> GetTroquelById(int idTroquel);

    Task<SingleResult<TroquelesDTO>> TratarTroquel(TroquelesDTO troquelDTO);

    Task<ListResult<ImposicionesDTO>> GetAllImposiciones();

    Task<SingleResult<ImposicionesDTO>> GetImposicionById(int id);

    Task<SingleResult<ImposicionesDTO>> TratarImposicion(ImposicionesDTO imposicionDTO);

    Task<ListResult<TroquelesDTO>> GetTroquelesIncompletos();
    
    Task<ListResult<TroquelesDTO>> GetTroquelesInactivos();
    
    Task<SingleResult<bool>> ActualizarPlanos();
    
    Task<ListResult<ImposicionesDTO>> GetImposicionesInactivas();
    
    Task<ListResult<ImposicionesDTO>> GetImposicionesIncompletas();
    
    Task<ListResult<TiposCaracteristicasDTO>> GetAllTiposCaracteristicas();
    
    Task<ListResult<ValoresTiposCaracteristicasDTO>> GetAllValoresTiposCaracteristicas();
    
    Task<SingleResult<TiposCaracteristicasDTO>> TratarTipoCaracteristica(TiposCaracteristicasDTO tipoCaracteristicaDTO);
    
    Task<SingleResult<ValoresTiposCaracteristicasDTO>> TratarValoresTipoCaracteristica(ValoresTiposCaracteristicasDTO valoresTiposCaracteristicasDTO);
    
    Task<SingleResult<bool>> DeleteTipoCaracteristicaCommand(int id);
    
    Task<SingleResult<bool>> DeleteValorTipoCaracteristicaCommand(int id);
    
    Task<ListResult<MetodosInspeccionDTO>> GetAllMetodosInspeccion();
    
    Task<ListResult<CaracteristicasInspeccionDTO>> GetAllCaracteristicasInspeccion();
    
    Task<SingleResult<MetodosInspeccionDTO>> TratarMetodoInspeccion(MetodosInspeccionDTO metodosInspeccionDTO);
    
    Task<SingleResult<CaracteristicasInspeccionDTO>> TratarCaracteristicaInspeccion(CaracteristicasInspeccionDTO caracteristicasInspeccionDTO);
    
    Task<SingleResult<bool>> DeleteMetodoInspeccion(int id);
    
    Task<SingleResult<bool>> DeleteCaracteristicaInspeccion(int id);
    
    Task<ListResult<CodigoEnvasesDTO>> GetAllCodigosEnvases();

    Task<ListResult<ExcepcionesPreciosLotesDTO>> GetAllExcepcionesPrecios();

    Task<ListResult<FamiliasDTO>> GetAllFamilias();

    Task<ListResult<NaturalezaDTO>> GetAllNaturalezas();

    Task<ListResult<NodrizasDTO>> GetAllNodrizas();

    Task<ListResult<NoIncluirRegComprasDTO>> GetAllNoIncluirRegCompras();

    Task<ListResult<ConsignaNestleDTO>> GetAllPaquetesConsignaNestle();

    Task<ListResult<DatosAuxMaterialesDTO>> GetAllDatosAux();

    Task<ListResult<EnvasesDTO>> GetAllEnvases();

    Task<ListResult<ColoresDTO>> GetAllColores();

    Task<ListResult<CorrespondenciasSkusDTO>> GetAllCorrespondenciasSKUs();

    Task<ListResult<RelacionCompuestasDTO>> GetAllRelacionesCompuestas();

    Task<SingleResult<CodigoEnvasesDTO>> TratarCodigoEnvase(CodigoEnvasesDTO codigoEnvasesDTO);

    Task<SingleResult<ExcepcionesPreciosLotesDTO>> TratarExcepcionesPreciosLotes(ExcepcionesPreciosLotesDTO excepcionesPreciosLotesDTO);

    Task<SingleResult<FamiliasDTO>> TratarFamilia(FamiliasDTO familiasDTO);

    Task<SingleResult<NaturalezaDTO>> TratarNaturaleza(NaturalezaDTO naturalezaDTO);

    Task<SingleResult<NodrizasDTO>> TratarNodriza(NodrizasDTO nodrizasDTO);

    Task<SingleResult<NoIncluirRegComprasDTO>> TratarNoIncluirRegCompras(NoIncluirRegComprasDTO noIncluirRegComprasDTO);

    Task<SingleResult<ConsignaNestleDTO>> TratarConsignaNestle(ConsignaNestleDTO consignaNestleDTO);

    Task<SingleResult<DatosAuxMaterialesDTO>> TratarDatosAuxMateriales(DatosAuxMaterialesDTO datosAuxMaterialesDTO);

    Task<SingleResult<EnvasesDTO>> TratarEnvase(EnvasesDTO envasesDTO);

    Task<SingleResult<ColoresDTO>> TratarColores(ColoresDTO coloresDTO);

    Task<SingleResult<CorrespondenciasSkusDTO>> TratarCorrespondenciaSKUs(CorrespondenciasSkusDTO correspondenciasSkusDTO);

    Task<SingleResult<RelacionCompuestasDTO>> TratarRelacionCompuesta(RelacionCompuestasDTO relacionCompuestasDTO);

    Task<SingleResult<bool>> DeleteCodigoEnvase(int id);

    Task<SingleResult<bool>> DeleteExcepcionPrecioLote(int id);

    Task<SingleResult<bool>> DeleteFamilia(int id);

    Task<SingleResult<bool>> DeleteNaturaleza(int id);

    Task<SingleResult<bool>> DeleteBarnizNodriza(int id);

    Task<SingleResult<bool>> DeleteNoIncluirRegCompras(int codigo);
    
    Task<SingleResult<bool>> DeleteConsignaNestle(int id);

    Task<SingleResult<bool>> DeleteDatoAux(int id);

    Task<SingleResult<bool>> DeleteEnvase(int id);

    Task<SingleResult<bool>> DeleteColor(int id);

    Task<SingleResult<bool>> DeleteCorrespondenciaSKU(int codigoLitalsa);

    Task<SingleResult<bool>> DeleteRelacionCompuesta(int id);

    Task<ListResult<MaterialesDTO>> GetAllMateriales();

    Task<ListResult<MaterialesDTO>> GetMaterialesByTipo(string tipo);
    
    Task<ListResult<SAPClienteDTO>> GetAllSAPClientes();
    
    Task<SingleResult<bool>> DeleteSAPCliente(int clienteId);
    
    Task<SingleResult<SAPClienteDTO>> TratarSAPCliente(SAPClienteDTO sAPClienteDTO);
    
    Task<ListResult<SAPClienteDestinatarioDTO>> GetAllSAPClientesDestinatarios();
    
    Task<SingleResult<bool>> DeleteSAPClienteDestinatario(int id);
    
    Task<SingleResult<SAPClienteDestinatarioDTO>> TratarSAPClienteDestinatario(SAPClienteDestinatarioDTO sAPClienteDestinatarioDTO);
    
    Task<ListResult<SAPProveedorDTO>> GetAllSAPProveedores();
    
    Task<SingleResult<bool>> DeleteSAPProveedor(int id);
    
    Task<SingleResult<SAPProveedorDTO>> TratarSAPProveedor(SAPProveedorDTO sAPProveedorDTO);
    
    Task<ListResult<TipoElementoDTO>> GetAllTiposElemento();
    
    Task<SingleResult<bool>> DeleteTipoElemento(int id);
    
    Task<SingleResult<TipoElementoDTO>> TratarTipoElemento(TipoElementoDTO tipoElementoDTO);
    
    Task<ListResult<MaestroDTO>> GetAllMaestro();
    
    Task<ListResult<MaestroDTO>> GetMaestroIncompletos();
    
    Task<Result> ActualizarTroqueles();
    
    Task<Result> ActualizarImposiciones();

    Task<ListResult<MotivosDTO>> GetAllMotivos();

    Task<Result> ActualizarMotivos();

    Task<SingleResult<TroquelesDTO>> DuplicarTroquel(int id);

    Task<SingleResult<ImposicionesDTO>> DuplicarImposicion(int id);

    Task<ListResult<AuditoriaDTO>> GetAllAuditorias();

    Task<SingleResult<AuditoriaDTO>> GetUltimaActualizacionByTabla(string tabla);

    Task<ListResult<AuditoriaDTO>> GetUltimosEventosQuery();
}