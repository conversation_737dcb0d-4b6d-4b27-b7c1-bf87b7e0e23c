﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;
public class ImportarExcepcionesPreciosLotesCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarExcepcionesPreciosLotesCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarExcepcionesPreciosLotesCommandHandler : IRequestHandler<ImportarExcepcionesPreciosLotesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarExcepcionesPreciosLotesCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarExcepcionesPreciosLotesCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new ExcepcionesPreciosLotesDTO
                    {
                        CodigoBarniz = worksheet.Cell(filaActual, 2).GetValue<int>(),              
                        Fecha = worksheet.Cell(filaActual, 3).GetDateTime(),                    
                        Precio = worksheet.Cell(filaActual, 4).GetValue<decimal>(),               
                        Borrado = false                                                          
                    };

                    if (dto.CodigoBarniz <= 0 || dto.Precio <= 0)
                    {
                        errores.Add($"Fila {filaActual}: El ID del barniz y el precio deben ser mayores que 0.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.ExcepcionesPreciosLotes
                        .FirstOrDefaultAsync(e =>
                            e.CodigoBarniz == dto.CodigoBarniz &&
                            e.Fecha.Date == dto.Fecha.Date, cancellationToken);

                    var clave = $"{dto.CodigoBarniz}_{dto.Fecha:yyyyMMdd}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.ExcepcionesPreciosLotes>(dto);
                        await _context.ExcepcionesPreciosLotes.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "MM_Barnices_ExcepcionesPreciosLotes",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var precioAnterior = existente.Precio;
                        TinyMapper.Map(dto, existente);

                        if (precioAnterior != existente.Precio)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_ExcepcionesPreciosLotes",
                                IdRegistro = idAuditoria,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Precio actualizado: {precioAnterior} → {existente.Precio}"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar excepciones de precios: {ex.Message}";
        }

        return result;
    }
}