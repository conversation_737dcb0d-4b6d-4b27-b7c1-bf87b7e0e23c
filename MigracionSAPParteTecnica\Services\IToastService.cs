﻿using DevExpress.Blazor;
using DevExpress.Blazor.Internal;

namespace MigracionSAPParteTecnica.Services;

public interface IToastService : IToastNotificationService
{
    void MostrarError(string mensaje);
    void MostrarOk(string mensaje);
    void MostrarInfo(string mensaje);
}

public class ToastService : ToastNotificationService, IToastService
{
    public ToastService(IToastManageService toastManageService)
        : base(toastManageService)
    {
    }

    public void MostrarError(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje.Replace("<br>", "\n"), // salto de línea real
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Danger,
            DisplayTime = TimeSpan.FromSeconds(5)
        });
    }

    public void MostrarOk(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje,
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Success,
            DisplayTime = TimeSpan.FromSeconds(5)
        });
    }

    public void MostrarInfo(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje,
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Info,
            DisplayTime = TimeSpan.FromSeconds(5)
        });
    }
}