﻿@page "/GestionCaracteristicasInspeccion"
@attribute [Authorize(Roles = "calidad, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var tipoCaracteristicas = (CaracteristicasInspeccionDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(tipoCaracteristicas)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Nombre" Caption="Nombre" />
        <DxGridDataColumn FieldName="CampoBusqueda" Caption="Campo de búsqueda" />
        <DxGridDataColumn FieldName="MetodoIns" Caption="Método Inspección">
            <CellDisplayTemplate Context="context">
                @{
                    var metodoId = ((CaracteristicasInspeccionDTO)context.DataItem).MetodoIns;
                    var metodoNombre = MetodosInspeccionList.FirstOrDefault(x => x.Id == metodoId)?.Nombre ?? "";
                }
                <span>@metodoNombre</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="NumeroDecimales" Caption="Número de decimales" />
        <DxGridDataColumn FieldName="LimiteToleranciaInf" Caption="Límite de toleracia inferior" />
        <DxGridDataColumn FieldName="LimiteToleranciaSup" Caption="Límite de toleracia superior" />
        <DxGridDataColumn FieldName="TextoBreve" Caption="Texto Breve" />
        <DxGridDataColumn FieldName="UnidadMedida" Caption="Unidad de medida" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.CaracteristicasInspeccion"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/CaracteristicasInspeccion"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditCaracteristicasInspeccionPopUp @ref="editCaracteristicaInspeccion" OnSave="GuardarCambios" MetodosInspeccionList="MetodosInspeccionList"/>

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<CaracteristicasInspeccionDTO> Data = new();
    string GridSearchText = "";
    private EditCaracteristicasInspeccionPopUp? editCaracteristicaInspeccion;
    private CaracteristicasInspeccionDTO? selectedCaracteristicaInspeccion { get; set; }
    private List<MetodoInspeccionComboItem> MetodosInspeccionList = new();
    private MetodoInspeccionComboItem? MetodoSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMetodosInspeccion();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (CaracteristicasInspeccionDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllCaracteristicasInspeccion();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<CaracteristicasInspeccionDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedCaracteristicaInspeccion = dataItem as CaracteristicasInspeccionDTO ?? new CaracteristicasInspeccionDTO();

        editCaracteristicaInspeccion?.AbrirPopUp(selectedCaracteristicaInspeccion);
    }

    async Task GuardarCambios(CaracteristicasInspeccionDTO updatedCaracteristica)
    {
        _isLoading = true;
        selectedCaracteristicaInspeccion = updatedCaracteristica;

        var result = await MigracionSAPParteTecnicaService.TratarCaracteristicaInspeccion(selectedCaracteristicaInspeccion);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    private async Task CargarMetodosInspeccion()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllMetodosInspeccion();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MetodosInspeccionList = result.Data.Select(m => new MetodoInspeccionComboItem
            {
                Id = m.Id,
                Nombre = m.Nombre,
                Descripcion = m.Descripcion ?? ""
            }).ToList();
        }
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (CaracteristicasInspeccionDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteCaracteristicaInspeccion(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Característica de inspección eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}