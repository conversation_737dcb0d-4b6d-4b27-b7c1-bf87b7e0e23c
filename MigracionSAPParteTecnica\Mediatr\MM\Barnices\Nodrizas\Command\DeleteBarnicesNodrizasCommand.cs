﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;
public class DeleteBarnicesNodrizasCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteBarnicesNodrizasCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteBarnicesNodrizasCommandHandler : IRequestHandler<DeleteBarnicesNodrizasCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteBarnicesNodrizasCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteBarnicesNodrizasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var barnicesNodriza = await _migracionSAPParteTecnicaContext.Nodrizas
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (barnicesNodriza == null)
            {
                result.Errors.Add("No se ha encontrado el barniz en la nodriza a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(barnicesNodriza);

            barnicesNodriza.Borrado = true;

            _migracionSAPParteTecnicaContext.Nodrizas.Update(barnicesNodriza);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_Nodrizas",
                IdRegistro = barnicesNodriza.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de el barniz en la nodriza: Id='{barnicesNodriza.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteBarnicesNodrizasCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}