﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;

namespace MigracionSAPParteTecnica.DTO.QM;
public class TiposCaracteristicasDTO
{
    public int Id { get; set; }

    public string CodigoTipo { get; set; }

    public string Tipo { get; set; }

    public string Descripcion { get; set; }

    public virtual ICollection<ValoresTiposCaracteristicasDTO> ValoresTiposCaracteristicas { get; set; } = new List<ValoresTiposCaracteristicasDTO>();
    public bool Borrado { get; set; }
}