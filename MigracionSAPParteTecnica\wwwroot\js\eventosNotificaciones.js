﻿window.hacerBotonArrastrable = function (id) {
    const btn = document.getElementById(id);
    const panel = document.getElementById("eventosPanel");
    let offsetX = 0, offsetY = 0, isDragging = false;

    if (!btn || !panel) return;

    const savedPos = localStorage.getItem("btnNotifPos");
    if (savedPos) {
        const pos = JSON.parse(savedPos);
        btn.style.left = pos.left + "px";
        btn.style.top = pos.top + "px";
    }

    btn.style.position = "fixed";

    btn.addEventListener("mousedown", function (e) {
        isDragging = true;
        offsetX = e.clientX - btn.offsetLeft;
        offsetY = e.clientY - btn.offsetTop;
        e.preventDefault();
    });

    document.addEventListener("mousemove", function (e) {
        if (!isDragging) return;
        e.preventDefault();

        let newLeft = e.clientX - offsetX;
        let newTop = e.clientY - offsetY;

        newLeft = Math.max(0, Math.min(window.innerWidth - btn.offsetWidth, newLeft));
        newTop = Math.max(0, Math.min(window.innerHeight - btn.offsetHeight, newTop));

        btn.style.left = newLeft + "px";
        btn.style.top = newTop + "px";
    });

    document.addEventListener("mouseup", function () {
        if (isDragging) {
            isDragging = false;

            const btnW = btn.offsetWidth;
            const btnH = btn.offsetHeight;
            const winW = window.innerWidth;
            const winH = window.innerHeight;

            const left = btn.offsetLeft;
            const top = btn.offsetTop;

            // Decide a qué borde anclar horizontalmente
            let finalLeft = left < winW / 2 ? 10 : winW - btnW - 10;

            // Decide a qué borde anclar verticalmente
            let finalTop = top < winH / 2 ? 10 : winH - btnH - 10;

            // Aplica
            btn.style.left = finalLeft + "px";
            btn.style.top = finalTop + "px";

            localStorage.setItem("btnNotifPos", JSON.stringify({
                left: finalLeft,
                top: finalTop
            }));

            window.posicionarPanelEventos();
        }
    });
};

window.posicionarPanelEventos = function () {
    const btn = document.getElementById("btnNotificaciones");
    const panel = document.getElementById("eventosPanel");
    if (!btn || !panel) return;

    const panelOffset = 8;
    const panelHeight = panel.offsetHeight || 300;
    const panelWidth = panel.offsetWidth || 320;

    const espacioAbajo = window.innerHeight - (btn.offsetTop + btn.offsetHeight);
    const espacioArriba = btn.offsetTop;
    const espacioDerecha = window.innerWidth - (btn.offsetLeft + btn.offsetWidth);
    const espacioIzquierda = btn.offsetLeft;

    // Dirección vertical
    let top = 0;
    if (espacioAbajo >= panelHeight + 20) {
        top = btn.offsetTop + btn.offsetHeight + panelOffset; // debajo
    } else if (espacioArriba >= panelHeight + 20) {
        top = btn.offsetTop - panelHeight - panelOffset; // encima
    } else {
        // Centrado si no hay espacio suficiente
        top = Math.max(10, (window.innerHeight - panelHeight) / 2);
    }

    // Dirección horizontal
    let left = 0;
    if (espacioDerecha >= panelWidth) {
        left = btn.offsetLeft; // alineado a la izquierda del botón
    } else if (espacioIzquierda >= panelWidth) {
        left = btn.offsetLeft + btn.offsetWidth - panelWidth; // alineado a la derecha del botón
    } else {
        // Centrado si no hay espacio suficiente
        left = Math.max(10, (window.innerWidth - panelWidth) / 2);
    }

    panel.style.left = left + "px";
    panel.style.top = top + "px";
};
