﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Command;
public class DeleteRelacionCompuestaCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteRelacionCompuestaCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteRelacionCompuestaCommandHandler : IRequestHandler<DeleteRelacionCompuestaCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteRelacionCompuestaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteRelacionCompuestaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var relacion = await _migracionSAPParteTecnicaContext.RelacionCompuestas
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (relacion == null)
            {
                result.Errors.Add("No se ha encontrado el color a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(relacion);

            relacion.Borrado = true;

            _migracionSAPParteTecnicaContext.RelacionCompuestas.Update(relacion);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Tintas_RelacionCompuestas",
                IdRegistro = relacion.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de la relación compuesta: Codigo='{relacion.Id}'"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteRelacionCompuestaCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}