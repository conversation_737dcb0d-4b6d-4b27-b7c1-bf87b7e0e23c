﻿using MigracionSAPParteTecnica.DTO.MM.Tintas;

namespace MigracionSAPParteTecnica.Validators.MM.Tintas;
public static class CorrespondenciasSkusValidator
{
    public static string? Validar(CorrespondenciasSkusDTO dto)
    {
        if (dto.CodigoTinta == 0)
            return "El código Litalsa no puede ser 0.";

        if (dto.CodigoProveedor == 0)
            return "El código del proveedor no puede ser 0.";

        if (string.IsNullOrWhiteSpace(dto.Pl) || dto.Pl.Trim() == "0")
            return "El campo PL no puede estar vacío ni ser 0.";

        if (dto.PesoNeto <= 0)
            return "El peso neto debe ser mayor que 0.";

        return null;
    }
}