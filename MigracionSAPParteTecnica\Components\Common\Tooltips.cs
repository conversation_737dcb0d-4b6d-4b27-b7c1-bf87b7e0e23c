﻿namespace MigracionSAPParteTecnica.Components.Common;
public class Tooltips
{
    public const string TipoCodTipo = "Categoría principal por la que se clasifican los grupos de códigos y los códigos según su contenido, admitidos para QM son: 1, 2, 5, 8, 9, D, <PERSON>. \r\nTipos admitidos para PM son: 0, 2, 5, A, B, C, D. \r\n0 Motivos de la medida 1 Atributos de la característica 2 Tareas 5 Causas 8 Actividades (QM) 9 Tipos de defecto A Actividades (PM) B Partes del objeto C Resumen del daño D Codificación E Ubicaciones del defecto";
    public const string TipoCaracteristica = "Grupo de códigos*  Clave para el grupo de códigos.  Longitud máxima: 8";
    public const string TextoBreveCaracteristica =  "Descripción breve de grupo de códigos*  Descripción breve del grupo de códigos. Longitud máxima: 40";
    public const string ValorValoresTipos = "Representa el nivel más bajo dentro de la jerarquía de clase de catálogo, grupo de códigos y código. \r\nUn código se refiere a la codificación real para el atributo. Por ejemplo, en un grupo de códigos de colores, el código AZ puede representar el color azul, VE puede representar verde, etc. \r\nEl código puede identificarse unívocamente solo con la clase de catálogo y el grupo de códigos. Los caracteres especiales (como \"%\",\"_\",\"*\") no se admiten. Longitud máxima: 4";
    public const string DescripcionValorValoresTipo = "Texto breve para código*  Texto breve para código QM/PM Longitud: 40";
    public const string NombreMetodoInspeccion = "Nombre que identifica unívocamente un método de inspección dentro de un centro.  Un método de inspección describe cómo inspeccionar una característica de inspección. \r\nPuede asignar un método de inspección a una característica de inspección maestra o directamente a una característica de inspección en un plan de inspección.  \r\nEl carácter debe estar en mayúsculas. Longitud: 80";
    public const string TextoBreveMetodoInspeccion = "Para cada método de inspección, el sistema separará la línea de texto en varias líneas automáticamente en los textos explicativos para cada idioma. Longitud.: 200";
    public const string NombreCaracteristicasInspeccion = "Nombre que identifica unívocamente una característica de inspección maestra dentro de un centro. Una característica de inspección describe lo que se debe inspeccionar, es decir, los requisitos de inspección para materiales, piezas y productos.Longitud: 8";
}