﻿@page "/GestionTintasRelacionCompuestas"
@attribute [Authorize(Roles = "materiales, admin, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var relacionCompuesta = (RelacionCompuestasDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(relacionCompuesta)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false"/>
        <DxGridDataColumn FieldName="Ingrediente" Caption="Ingrediente" />
        <DxGridDataColumn FieldName="CodigoDescripcion" Caption="Tinta" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.RelacionCompuestas"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/RelacionCompuestas"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>

<EditRelacionTintaPopUp @ref="editRelacionTintaPopUp" OnSave="GuardarCambios" MaterialesList="MaterialesList" MaterialSeleccionado="MaterialSeleccionado" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<RelacionCompuestasDTO> Data = new();
    string GridSearchText = "";
    private EditRelacionTintaPopUp? editRelacionTintaPopUp;
    private RelacionCompuestasDTO? selectedRelacion { get; set; }
    private List<MaterialComboItem> MaterialesList = new();
    private MaterialComboItem? MaterialSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMateriales();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (RelacionCompuestasDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllRelacionesCompuestas();

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<RelacionCompuestasDTO>(result.Data);

            foreach (var item in Data)
            {
                item.CodigoDescripcion = MaterialesList
                    .FirstOrDefault(m => m.Codigo == item.CodigoTinta)?.Display;
            }

            StateHasChanged();
        }
    }

    private async Task CargarMateriales()
    {
        var result = await MigracionSAPParteTecnicaService.GetMaterialesByTipo("TINTA");
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MaterialesList = result.Data.Select(m => new MaterialComboItem
            {
                Codigo = m.Codigo,
                Nombre = m.Descripcion ?? ""
            }).ToList();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedRelacion = dataItem as RelacionCompuestasDTO ?? new RelacionCompuestasDTO();

        MaterialSeleccionado = MaterialesList
         .FirstOrDefault(c => c.Codigo == selectedRelacion.CodigoTinta);

        editRelacionTintaPopUp?.AbrirPopUp(selectedRelacion);
    }

    async Task GuardarCambios(RelacionCompuestasDTO updatedRelacionCompuestas)
    {
        _isLoading = true;
        selectedRelacion = updatedRelacionCompuestas;

        var result = await MigracionSAPParteTecnicaService.TratarRelacionCompuesta(selectedRelacion);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (RelacionCompuestasDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteRelacionCompuesta(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Relacion compuesta eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}