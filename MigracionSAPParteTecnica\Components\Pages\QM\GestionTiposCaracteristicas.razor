﻿@page "/GestionTiposCaracteristicas"
@attribute [Authorize(Roles = "calidad, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var tipoCaracteristicas = (TiposCaracteristicasDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(tipoCaracteristicas)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false"/>
        <DxGridDataColumn FieldName="CodigoTipo" Caption="Código tipo" />
        <DxGridDataColumn FieldName="Tipo" Caption="Tipo" />
        <DxGridDataColumn FieldName="Descripcion" Caption="Descripcion" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.TiposCaracteristicas"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/TiposCaracteristicas"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>

</DxGrid>
<EditTipoCaracteristicaPopUp @ref="editTipoCaracteristica" OnSave="GuardarCambios" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<TiposCaracteristicasDTO> Data = new();
    string GridSearchText = "";
    List<DropDownWrapper> TiposCaracteristicasList = new();
    private EditTipoCaracteristicaPopUp? editTipoCaracteristica;
    private TiposCaracteristicasDTO? selectedTipoCaracteristica { get; set; }
    

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (TiposCaracteristicasDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllTiposCaracteristicas();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<TiposCaracteristicasDTO>(result.Data);
            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedTipoCaracteristica = dataItem as TiposCaracteristicasDTO ?? new TiposCaracteristicasDTO();
        editTipoCaracteristica?.AbrirPopUp(selectedTipoCaracteristica);
    }

    async Task GuardarCambios(TiposCaracteristicasDTO updatedCaracteristica)
    {
        _isLoading = true;
        selectedTipoCaracteristica = updatedCaracteristica;

        var result = await MigracionSAPParteTecnicaService.TratarTipoCaracteristica(selectedTipoCaracteristica);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (TiposCaracteristicasDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteTipoCaracteristicaCommand(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Tipo de característica eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}