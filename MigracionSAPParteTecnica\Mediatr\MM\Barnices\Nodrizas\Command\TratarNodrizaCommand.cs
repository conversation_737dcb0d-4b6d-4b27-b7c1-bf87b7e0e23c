﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;

public class TratarNodrizaCommand : IRequest<SingleResult<NodrizasDTO>>
{
    public NodrizasDTO NodrizaDTO { get; set; }

    public TratarNodrizaCommand(NodrizasDTO nodrizaDTO)
    {
        NodrizaDTO = nodrizaDTO;
    }
}

internal class TratarNodrizaCommandHandler : IRequestHandler<TratarNodrizaCommand, SingleResult<NodrizasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarNodrizaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<NodrizasDTO>> Handle(TratarNodrizaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<NodrizasDTO>
        {
            Data = new NodrizasDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var dto = request.NodrizaDTO;

            var existing = await _migracionSAPParteTecnicaContext.Nodrizas
                .FirstOrDefaultAsync(m => m.Id == dto.Id, cancellationToken);

            if (existing == null)
            {
                var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.Nodrizas>(dto);

                nuevo.Borrado = false;

                await _migracionSAPParteTecnicaContext.Nodrizas.AddAsync(nuevo, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<NodrizasDTO>(nuevo);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "MM_Barnices_Nodrizas",
                    IdRegistro = nuevo.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevo),
                    Comentarios = $"Alta de nodriza: Id = {nuevo.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existing);

                TinyMapper.Map(dto, existing);
                _migracionSAPParteTecnicaContext.Nodrizas.Update(existing);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = TinyMapper.Map<NodrizasDTO>(existing);

                var datosDespues = JsonSerializer.Serialize(existing);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "MM_Barnices_Nodrizas",
                    IdRegistro = existing.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de nodriza: Id = {existing.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarNodrizaCommandHandler - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}