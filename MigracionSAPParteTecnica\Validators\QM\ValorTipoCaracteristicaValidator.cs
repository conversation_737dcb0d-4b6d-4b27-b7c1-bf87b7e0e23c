﻿using MigracionSAPParteTecnica.DTO.QM;
using System.Text.RegularExpressions;

namespace MigracionSAPParteTecnica.Validators.QM;

public static class ValorTipoCaracteristicaValidator
{
    private static readonly Regex CaracteresNoPermitidos = new(@"[%_*]", RegexOptions.Compiled);

    public static string? Validar(ValoresTiposCaracteristicasDTO dto)
    {
        if (dto.TipoCaracteristicaId <= 0)
            return "Debe seleccionar un tipo de característica.";

        if (string.IsNullOrWhiteSpace(dto.Valor))
            return "Debe ingresar un valor.";

        if (dto.Valor.Length > 4)
            return "El valor no puede superar los 4 caracteres.";

        if (CaracteresNoPermitidos.IsMatch(dto.Valor))
            return "El valor no puede contener %, _ ni *.";

        if (string.IsNullOrWhiteSpace(dto.TextoBreve))
            return "Debe ingresar un texto breve para el valor.";

        if (dto.TextoBreve.Length > 40)
            return "El texto breve no puede superar los 40 caracteres.";

        return null;
    }
}