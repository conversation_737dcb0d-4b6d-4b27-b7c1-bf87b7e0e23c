﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.Nodrizas.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.NoIncluirRegCompras.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Command;
using MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.DatosAuxMateriales.Command;
using MigracionSAPParteTecnica.Mediatr.MM.General.Envase.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.Colores.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.CorrespondenciasSKUs.Command;
using MigracionSAPParteTecnica.Mediatr.MM.Tintas.RelacionCompuestas.Command;
using MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;
using MigracionSAPParteTecnica.Mediatr.QM.MetodosInspeccion.Command;
using MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;
using MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Command;

namespace MigracionSAPParteTecnica.Services;

[Route("api/[controller]")]
[ApiController]
public class UploadController : ControllerBase
{
    private readonly IMediator _mediator;

    public UploadController(IMediator mediator)
    {
        _mediator = mediator;
    }

    private async Task<IActionResult> ProcesarArchivo<TCommand>(IFormFile excelFile, Func<Stream, TCommand> crearCommand)
        where TCommand : IRequest<DTO.ResponseModels.Result>
    {
        if (excelFile == null || excelFile.Length == 0)
            return BadRequest("Archivo inválido.");

        try
        {
            using var stream = excelFile.OpenReadStream();
            var result = await _mediator.Send(crearCommand(stream));

            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error interno al procesar el archivo: {ex.Message}");
        }
    }

    [HttpPost("TiposCaracteristicas")]
    public Task<IActionResult> TiposCaracteristicas(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarTiposCaracteristicasCommand(s));

    //[HttpPost("ValoresTiposCaracteristicas")]
    //public Task<IActionResult> ValoresTiposCaracteristicas(IFormFile excelFile) =>
    //    ProcesarArchivo(excelFile, s => new ImportarValoresTiposCaracteristicasCommand(s));

    [HttpPost("MetodosInspeccion")]
    public Task<IActionResult> MetodosInspeccion(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarMetodosInspeccionCommand(s));

    [HttpPost("CaracteristicasInspeccion")]
    public Task<IActionResult> CaracteristicasInspeccion(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarCaracteristicasInspeccionCommand(s));

    [HttpPost("CodigoEnvases")]
    public Task<IActionResult> CodigoEnvases(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarCodigoEnvaseCommand(s));

    [HttpPost("ExcepcionesPreciosLotes")]
    public Task<IActionResult> ExcepcionesPreciosLotes(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarExcepcionesPreciosLotesCommand(s));

    [HttpPost("Familias")]
    public Task<IActionResult> Familias(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarFamiliasCommand(s));

    [HttpPost("Naturaleza")]
    public Task<IActionResult> Naturaleza(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarNaturalezaCommand(s));

    [HttpPost("Nodrizas")]
    public Task<IActionResult> Nodrizas(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarNodrizasCommand(s));

    [HttpPost("NoIncluirRegCompras")]
    public Task<IActionResult> NoIncluirRegCompras(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarNoIncluirRegComprasCommand(s));

    [HttpPost("DatosAuxMateriales")]
    public Task<IActionResult> DatosAuxMateriales(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarDatosAuxMaterialesCommand(s));

    [HttpPost("ConsignaNestle")]
    public Task<IActionResult> ConsignaNestle(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarConsignaNestleCommand(s));

    [HttpPost("Envases")]
    public Task<IActionResult> Envases(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarEnvasesCommand(s));

    [HttpPost("Colores")]
    public Task<IActionResult> Colores(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarColoresCommand(s));

    [HttpPost("CorrespondenciasSkus")]
    public Task<IActionResult> CorrespondenciasSkus(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarCorrespondenciasSkusCommand(s));

    [HttpPost("RelacionCompuestas")]
    public Task<IActionResult> RelacionCompuestas(IFormFile excelFile) =>
        ProcesarArchivo(excelFile, s => new ImportarRelacionCompuestasCommand(s));
}
