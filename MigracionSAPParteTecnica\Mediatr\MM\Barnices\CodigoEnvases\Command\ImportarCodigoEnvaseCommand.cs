﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.MM.Barnices;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
public class ImportarCodigoEnvaseCommand : IRequest<Result>
{
    public Stream ExcelStream { get; set; }

    public ImportarCodigoEnvaseCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarCodigoEnvasesCommandHandler : IRequestHandler<ImportarCodigoEnvaseCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarCodigoEnvasesCommandHandler(
        MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarCodigoEnvaseCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);

            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                var dto = new CodigoEnvasesDTO
                {
                    CodigoBarniz = worksheet.Cell(filaActual, 2).GetValue<int>(),
                    PesoNeto = worksheet.Cell(filaActual, 3).GetValue<int>(),
                    Borrado = false
                };

                if (dto.CodigoBarniz <= 0 || dto.PesoNeto <= 0)
                {
                    errores.Add($"Fila {filaActual}: Código y PesoNeto deben ser mayores que cero.");
                    descartados++;
                    filaActual++;
                    continue;
                }

                var existente = await _context.CodigoEnvases
                    .FirstOrDefaultAsync(c => c.CodigoBarniz == dto.CodigoBarniz, cancellationToken);

                if (existente is null)
                {
                    var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.CodigoEnvases>(dto);
                    await _context.CodigoEnvases.AddAsync(entidad, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);

                    await _auditoria.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "MM_Barnices_CodigoEnvases",
                        IdRegistro = entidad.Id,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(entidad),
                        Comentarios = "Importado desde Excel"
                    }, cancellationToken);

                    insertados++;
                }
                else
                {
                    var datosAntes = JsonSerializer.Serialize(existente);
                    var originalPeso = existente.PesoNeto;
                    TinyMapper.Map(dto, existente);

                    if (originalPeso != existente.PesoNeto)
                    {
                        await _context.SaveChangesAsync(cancellationToken);

                        var datosDespues = JsonSerializer.Serialize(existente);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "MM_Barnices_CodigoEnvases",
                            IdRegistro = existente.Id,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = $"Peso neto actualizado: '{originalPeso}' → '{existente.PesoNeto}'"
                        }, cancellationToken);

                        actualizados++;
                    }
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar códigos de envase: {ex.Message}";
        }

        return result;
    }
}