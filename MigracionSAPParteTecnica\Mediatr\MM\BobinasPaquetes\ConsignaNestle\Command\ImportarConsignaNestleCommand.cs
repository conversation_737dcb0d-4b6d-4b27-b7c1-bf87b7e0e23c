﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.BobinasPaquetes.ConsignaNestle.Command;

public class ImportarConsignaNestleCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarConsignaNestleCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarConsignaNestleCommandHandler : IRequestHandler<ImportarConsignaNestleCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarConsignaNestleCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarConsignaNestleCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new ConsignaNestleDTO
                    {
                        CodigoPaquete = worksheet.Cell(filaActual, 2).GetString().Trim(),
                        Borrado = false
                    };

                    if (string.IsNullOrWhiteSpace(dto.CodigoPaquete))
                    {
                        errores.Add($"Fila {filaActual}: El código del paquete no puede estar vacío.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.ConsignaNestle
                        .FirstOrDefaultAsync(c => c.CodigoPaquete == dto.CodigoPaquete, cancellationToken);

                    var idAuditoria = dto.CodigoPaquete.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.BobinasPaquetes.ConsignaNestle>(dto);
                        await _context.ConsignaNestle.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "ConsignaNestle",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var estadoAnterior = existente.Borrado;
                        TinyMapper.Map(dto, existente);

                        if (estadoAnterior != existente.Borrado)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_NoIncluirRegCompras",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Estado borrado actualizado: {estadoAnterior} → {existente.Borrado}"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar ConsignaNestle: {ex.Message}";
        }

        return result;
    }
}