﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using MigracionSAPParteTecnica.Components.Pages;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Query;
public class GetTroquelesIncompletosQuery : IRequest<ListResult<TroquelesDTO>>
{
}

internal class GetTroquelesIncompletosQueryHandler : IRequestHandler<GetTroquelesIncompletosQuery, ListResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetTroquelesIncompletosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<TroquelesDTO>> Handle(GetTroquelesIncompletosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TroquelesDTO>
        {
            Data = new List<TroquelesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listTroqueles = await _migracionSAPParteTecnicaContext.Troqueles
                .AsNoTracking()
                .Where(t =>
                    string.IsNullOrEmpty(t.TipoElemento) ||
                    t.DiametroEnvase == null ||
                    t.AlturaEnvase == null ||
                    t.ReservasSuperiores == null ||
                    t.ReservasInferiores == null ||
                    t.EjeMayor == null ||
                    t.EjeMenor == null
                )
                .AsSplitQuery()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<TroquelesDTO>>(listTroqueles).ToList();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetTroquelesIncompletosQuery - {e.InnerException?.Message ?? e.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}