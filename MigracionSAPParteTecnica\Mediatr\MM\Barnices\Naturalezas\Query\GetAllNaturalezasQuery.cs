﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Naturalezas.Query;
public class GetAllNaturalezasQuery : IRequest<ListResult<NaturalezaDTO>>
{
}

internal class GetAllNaturalezasQueryHandler : IRequestHandler<GetAllNaturalezasQuery, ListResult<NaturalezaDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllNaturalezasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<NaturalezaDTO>> Handle(GetAllNaturalezasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<NaturalezaDTO>
        {
            Data = new List<NaturalezaDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listNaturalezas = await _migracionSAPParteTecnicaContext.Naturaleza
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<NaturalezaDTO>>(listNaturalezas).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllNaturalezasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}