﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.ValoresTiposCaracteristicas.Command;
public class TratarValoresTiposCaracteristicasCommand : IRequest<SingleResult<ValoresTiposCaracteristicasDTO>>
{
    public ValoresTiposCaracteristicasDTO ValorTiposCaracteristicasDTO { get; set; }
    public TratarValoresTiposCaracteristicasCommand(ValoresTiposCaracteristicasDTO valoresTiposCaracteristicasDTO)
    {
        ValorTiposCaracteristicasDTO = valoresTiposCaracteristicasDTO;
    }
}

internal class TratarValoresTiposCaracteristicasCommandHandler : IRequestHandler<TratarValoresTiposCaracteristicasCommand, SingleResult<ValoresTiposCaracteristicasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarValoresTiposCaracteristicasCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnica, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnica;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<ValoresTiposCaracteristicasDTO>> Handle(TratarValoresTiposCaracteristicasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<ValoresTiposCaracteristicasDTO>
        {
            Data = new ValoresTiposCaracteristicasDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var existingValorTipoCaracteristica = await _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas
                .FirstOrDefaultAsync(t => t.Id == request.ValorTiposCaracteristicasDTO.Id, cancellationToken);


            if (existingValorTipoCaracteristica == null)
            {
                var nuevoValorTipoCaracterística = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.ValoresTiposCaracteristicas>(request.ValorTiposCaracteristicasDTO);

                await _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas.AddAsync(nuevoValorTipoCaracterística, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = request.ValorTiposCaracteristicasDTO;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "QM_ValoresTiposCaracteristicas",
                    IdRegistro = nuevoValorTipoCaracterística.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevoValorTipoCaracterística),
                    Comentarios = $"Alta de valor tipo característica: Id={nuevoValorTipoCaracterística.Id}"
                }, cancellationToken);
            }
            else
            {
                var datosAntes = JsonSerializer.Serialize(existingValorTipoCaracteristica);
                TinyMapper.Map(request.ValorTiposCaracteristicasDTO, existingValorTipoCaracteristica);

                _migracionSAPParteTecnicaContext.ValoresTiposCaracteristicas.Update(existingValorTipoCaracteristica);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = request.ValorTiposCaracteristicasDTO;

                var datosDespues = JsonSerializer.Serialize(existingValorTipoCaracteristica);

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "QM_ValoresTiposCaracteristicas",
                    IdRegistro = existingValorTipoCaracteristica.Id,
                    DatosAntes = datosAntes,
                    DatosDespues = datosDespues,
                    Comentarios = $"Modificación de valor tipo característica: Id={existingValorTipoCaracteristica.Id}"
                }, cancellationToken);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarValorTipoCaracteristica - {(e.InnerException?.Message ?? e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}