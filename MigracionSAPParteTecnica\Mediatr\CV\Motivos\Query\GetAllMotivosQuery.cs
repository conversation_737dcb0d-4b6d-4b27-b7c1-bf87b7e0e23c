﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Motivos.Query;
public class GetAllMotivosQuery : IRequest<ListResult<MotivosDTO>>
{
}

internal class GetAllMotivosQueryHandler : IRequestHandler<GetAllMotivosQuery, ListResult<MotivosDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllMotivosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MotivosDTO>> Handle(GetAllMotivosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MotivosDTO>
        {
            Data = new List<MotivosDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listMotivos = await _migracionSAPParteTecnicaContext.Motivos
                .AsNoTracking()
                .ToListAsync();

            result.Data = TinyMapper.Map<List<MotivosDTO>>(listMotivos).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllMotivos - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}