﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;
public class DeleteTipoCaracteristicaCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }

    public DeleteTipoCaracteristicaCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteTipoCaracteristicaCommandHandler : IRequestHandler<DeleteTipoCaracteristicaCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteTipoCaracteristicaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteTipoCaracteristicaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var tipoCaracteristica = await _migracionSAPParteTecnicaContext.TiposCaracteristicas
                .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

            if(tipoCaracteristica == null)
            {
                result.Errors.Add("No se ha encontrado el tipo de característica a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(tipoCaracteristica);

            tipoCaracteristica.Borrado = true;

            _migracionSAPParteTecnicaContext.TiposCaracteristicas.Update(tipoCaracteristica);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "QM_TiposCaracteristicas",
                IdRegistro = tipoCaracteristica.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de tipo característica: Id={tipoCaracteristica.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteTipoCaracteristicaCommand - {ex.InnerException?.Message ?? ex.Message}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}