﻿namespace MigracionSAPParteTecnica.Services;
public class PlanoService
{
    private readonly string _rootPath = @"\\192.48.48.124\Planos";

    public string? BuscarRutaPlano(int codigoIn2, string nombrePlano)
    {
        var codigoFormateado = codigoIn2.ToString().PadLeft(5, '0');

        var carpetaCliente = Directory.GetDirectories(_rootPath)
            .FirstOrDefault(dir => Path.GetFileName(dir).StartsWith(codigoFormateado));

        if (carpetaCliente == null)
            return null;

        var rutaPlano = Path.Combine(carpetaCliente, $"{nombrePlano}.pdf");
        return File.Exists(rutaPlano) ? rutaPlano : null;
    }

    public byte[] LeerPlano(string ruta)
    {
        return File.ReadAllBytes(ruta);
    }
}