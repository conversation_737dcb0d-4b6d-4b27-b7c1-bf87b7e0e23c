﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Command;
public class ImportarFamiliasCommand : IRequest<Result>
{
    public Stream ExcelStream { get; }

    public ImportarFamiliasCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarFamiliasCommandHandler : IRequestHandler<ImportarFamiliasCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoria;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarFamiliasCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoria, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoria = auditoria;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarFamiliasCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();
        int insertados = 0, actualizados = 0, descartados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);

            int filaActual = 6;

            while (!worksheet.Cell(filaActual, 2).IsEmpty())
            {
                try
                {
                    var dto = new FamiliasDTO
                    {
                        DatoIn2 = worksheet.Cell(filaActual, 2).GetString().Trim(),     
                        DatoSap = worksheet.Cell(filaActual, 3).GetString().Trim(),      
                        DescripcionSap = worksheet.Cell(filaActual, 4).GetString().Trim(), 
                        Borrado = false                                                
                    };

                    if (string.IsNullOrWhiteSpace(dto.DatoIn2) || string.IsNullOrWhiteSpace(dto.DatoSap))
                    {
                        errores.Add($"Fila {filaActual}: Los campos 'DatoIn2' y 'DatoSap' son obligatorios.");
                        descartados++;
                        filaActual++;
                        continue;
                    }

                    var existente = await _context.Familias
                        .FirstOrDefaultAsync(f =>
                            f.DatoIn2 == dto.DatoIn2 &&
                            f.DatoSap == dto.DatoSap, cancellationToken);

                    var clave = $"{dto.DatoIn2}_{dto.DatoSap}";
                    var idAuditoria = clave.GetHashCode();

                    if (existente is null)
                    {
                        var entidad = TinyMapper.Map<Entities.MigracionSAPParteTecnica.MM.Barnices.Familias>(dto);
                        await _context.Familias.AddAsync(entidad, cancellationToken);
                        await _context.SaveChangesAsync(cancellationToken);

                        await _auditoria.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "INSERT",
                            Tabla = "MM_Barnices_Familias",
                            IdRegistro = idAuditoria,
                            DatosAntes = null,
                            DatosDespues = JsonSerializer.Serialize(entidad),
                            Comentarios = "Importado desde Excel"
                        }, cancellationToken);

                        insertados++;
                    }
                    else
                    {
                        var datosAntes = JsonSerializer.Serialize(existente);
                        var descripcionAnterior = existente.DescripcionSap;
                        TinyMapper.Map(dto, existente);

                        if (descripcionAnterior != existente.DescripcionSap)
                        {
                            await _context.SaveChangesAsync(cancellationToken);

                            var datosDespues = JsonSerializer.Serialize(existente);

                            await _auditoria.RegistrarAsync(new AuditoriaDTO
                            {
                                Usuario = usuario,
                                Fecha = DateTime.Now,
                                Accion = "UPDATE",
                                Tabla = "MM_Barnices_Familias",
                                IdRegistro = existente.Id,
                                DatosAntes = datosAntes,
                                DatosDespues = datosDespues,
                                Comentarios = $"Descripción actualizada: '{descripcionAnterior}' → '{existente.DescripcionSap}'"
                            }, cancellationToken);

                            actualizados++;
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    errores.Add($"Fila {filaActual}: Error al procesar → {innerEx.Message}");
                    descartados++;
                }

                filaActual++;
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar familias: {ex.Message}";
        }

        return result;
    }
}