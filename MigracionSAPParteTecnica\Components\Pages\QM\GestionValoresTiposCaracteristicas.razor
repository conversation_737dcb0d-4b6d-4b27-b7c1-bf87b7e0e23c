﻿@page "/GestionValoresTiposCaracteristicas"
@attribute [Authorize(Roles = "calidad, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" 
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var tipoCaracteristicas = (ValoresTiposCaracteristicasDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(tipoCaracteristicas)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false"/>
        <DxGridDataColumn FieldName="TipoCaracteristicaDescripcion" Caption="Tipo característica" />
        <DxGridDataColumn FieldName="TipoCaracteristicaId" Caption="TipoCaracteristicaId" Visible="false" />
        <DxGridDataColumn FieldName="Valor" Caption="Valor" />
        <DxGridDataColumn FieldName="TextoBreve" Caption="Texto Breve" />
    </Columns>
   @*  <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.ValoresTiposCaracteristicas"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/ValoresTiposCaracteristicas"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate> *@
</DxGrid>
<EditValorTipoCaracteristicaPopUp @ref="editValorTipoCaracteristica" OnSave="GuardarCambios" TiposCaracteristicasList="TiposCaracteristicasList" TipoCaracteristicaSeleccionada="TiposCaracteristicaSeleccionada" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<ValoresTiposCaracteristicasDTO> Data = new();
    string GridSearchText = "";
    private EditValorTipoCaracteristicaPopUp? editValorTipoCaracteristica;
    private ValoresTiposCaracteristicasDTO? selectedValorTipoCaracteristica { get; set; }
    private List<TipoCaracteristicaComboItem> TiposCaracteristicasList = new();
    private TipoCaracteristicaComboItem TiposCaracteristicaSeleccionada = new();

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarTiposCaracteristicas();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (ValoresTiposCaracteristicasDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllValoresTiposCaracteristicas();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<ValoresTiposCaracteristicasDTO>(result.Data);

            foreach (var item in Data)
            {
                item.TipoCaracteristicaDescripcion = TiposCaracteristicasList
                    .FirstOrDefault(m => m.Id == item.TipoCaracteristicaId)?.Display;
            }

            StateHasChanged();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedValorTipoCaracteristica = dataItem as ValoresTiposCaracteristicasDTO ?? new ValoresTiposCaracteristicasDTO();

        TiposCaracteristicaSeleccionada = TiposCaracteristicasList
         .FirstOrDefault(c => c.Id == selectedValorTipoCaracteristica.TipoCaracteristicaId);

        editValorTipoCaracteristica?.AbrirPopUp(selectedValorTipoCaracteristica);
    }

    async Task GuardarCambios(ValoresTiposCaracteristicasDTO updatedValor)
    {
        _isLoading = true;
        selectedValorTipoCaracteristica = updatedValor;

        var result = await MigracionSAPParteTecnicaService.TratarValoresTipoCaracteristica(selectedValorTipoCaracteristica);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    private async Task CargarTiposCaracteristicas()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllTiposCaracteristicas();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            TiposCaracteristicasList = result.Data.Select(t => new TipoCaracteristicaComboItem
            {
                Id = t.Id,
                CodigoTipo = t.CodigoTipo,
                Tipo = t.Tipo
            }).ToList();
        }
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (ValoresTiposCaracteristicasDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteValorTipoCaracteristicaCommand(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Valor de tipo de característica eliminada correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}