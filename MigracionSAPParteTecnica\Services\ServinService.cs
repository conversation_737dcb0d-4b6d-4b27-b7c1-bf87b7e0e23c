﻿using MediatR;
using MigracionSAPParteTecnica.Data.Servin;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;
using MigracionSAPParteTecnica.Mediatr.CV.Maestro.Command;
using MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Command;
using MigracionSAPParteTecnica.Mediatr.MM.General.DatosMaestros.Command;

namespace MigracionSAPParteTecnica.Services;
public class ServinService(ServinContext servinContext, IMediator mediator)
    : IServinService
{
    public async Task<Result> ActualizarMateriales()
    {
        var result = await mediator.Send(new ActualizarMaterialesCommand());
        return result;
    }
    public async Task<Result> ActualizarMaestro()
    {
        var result = await mediator.Send(new ActualizarMaestroCommand());
        return result;
    }
}