﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Mediatr\General\Auditorias\Command\" />
    <Folder Include="Migrations\" />
    <Folder Include="Validators\MM\General\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="ClosedXML" Version="0.105.0" />
		<PackageReference Include="CompareNETObjects" Version="4.83.0" />
		<PackageReference Include="DevExpress.Blazor" Version="24.2.6" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.16" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.16" />
		<!--<ProjectReference Include="..\MigracionSAPParteTecnica.Client\MigracionSAPParteTecnica.Client.csproj" />-->
		<PackageReference Include="Blazr.RenderState.Server" Version="1.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="TinyMapper" Version="3.0.3" />
	</ItemGroup>	
</Project>
