﻿using MigracionSAPParteTecnica.DTO.MM.Barnices;

namespace MigracionSAPParteTecnica.Validators.MM.Barnices;
public static class FamiliasValidator
{
    public static string? Validar(FamiliasDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.DatoIn2))
            return "El campo 'DatoIn2' no puede estar vacío.";

        if (string.IsNullOrWhiteSpace(dto.DatoSap))
            return "El campo 'DatoSap' no puede estar vacío.";

        if (dto.DatoSap.Length > 2)
            return "El campo 'DatoSap' no puede superar los 2 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.DescripcionSap))
            return "El campo 'Descripción SAP' no puede estar vacío.";

        return null;
    }
}