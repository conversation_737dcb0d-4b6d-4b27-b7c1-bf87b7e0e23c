﻿using MigracionSAPParteTecnica.DTO.MM.Barnices;

namespace MigracionSAPParteTecnica.Validators.MM.Barnices;
public static class ExcepcionesPreciosLotesValidator
{
    public static string? Validar(ExcepcionesPreciosLotesDTO dto)
    {
        if (dto.CodigoBarniz == 0)
            return "Debe seleccionar un barniz.";

        if (dto.Precio <= 0)
            return "El precio debe ser mayor que cero.";

        if (dto.Fecha == default || dto.Fecha == DateTime.MinValue)
            return "Debe introducir una fecha válida.";

        return null;
    }
}