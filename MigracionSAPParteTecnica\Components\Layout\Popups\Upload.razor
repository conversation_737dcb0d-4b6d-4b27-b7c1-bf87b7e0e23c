﻿@page "/upload"

@inject IJSRuntime JS

<div id="drop-zone" style="border: 2px dashed gray; padding: 20px; text-align:center; cursor:pointer;">
    <p>Arrastra un archivo aquí o haz clic para seleccionar</p>
</div>

<InputFile id="inputExcelFile" style="display:none" OnChange="OnFileSelected" />

<p>Archivo seleccionado: @ArchivoSeleccionadoNombre</p>

@code {
    private string? ArchivoSeleccionadoNombre;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("blazorFileDrop.init", "drop-zone", "inputExcelFile", DotNetObjectReference.Create(this));
        }
    }

    private void OnFileSelected(InputFileChangeEventArgs e)
    {
        if (e.FileCount > 0)
        {
            ArchivoSeleccionadoNombre = e.File.Name;
            StateHasChanged();
        }
    }

    [JSInvokable]
    public Task SetFileFromDrop(string fileName)
    {
        ArchivoSeleccionadoNombre = fileName;
        StateHasChanged();
        return Task.CompletedTask;
    }
}