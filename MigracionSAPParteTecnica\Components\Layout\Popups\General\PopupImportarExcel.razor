﻿
@inject IJSRuntime JS
@inject IToastService ToastService
@inject HttpClient Http
@inject NavigationManager NavigationManager

<DxPopup @bind-Visible="IsVisible"
         HeaderText="Importar desde Excel"
         Width="500px"
         ShowFooter="false"
         ShowCloseButton="true"
         CloseOnEscape="true"
         CloseOnOutsideClick="true"
         CssClass="custom-popup">
    <BodyContentTemplate>
        <div class="d-flex flex-column align-items-center p-3">

            <div id="drop-zone"
                 class="drop-zone @(ArchivoSeleccionado is null && intentoSinArchivo ? "input-error" : "")"
                 style="display:flex; flex-direction: column; align-items: center; justify-content: center; height: 100px; border: 2px dashed #ccc; border-radius: 8px; cursor: pointer;">

                @if (ArchivoSeleccionadoNombre is null)
                {
                    <div style="color: #666; text-align:center;">
                        <i class="bi bi-cloud-arrow-up" style="font-size: 2.5rem; margin-bottom: 8px;"></i>
                        <div>Arrastra el archivo aquí o haz clic para seleccionar</div>
                    </div>
                }
                else
                {
                    <div style="color: #333; display: flex; align-items: center; gap: 10px;">
                        <i class="bi bi-file-earmark-excel" style="font-size: 2rem; color: green;"></i>
                        <span style="font-weight: 600;">@ArchivoSeleccionadoNombre</span>

                        <button class="btn btn-sm btn-outline-danger ms-3" @onclick="QuitarArchivo" style="height: 30px;">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    </div>
                }
            </div>

            <InputFile id="inputExcelFile"
                       style="display:none"
                       accept=".xlsx"
                       OnChange="OnFileSelected" />

            <DxButton Text="Importar"
                      CssClass="mt-3"
                      Enabled="@(!IsUploading && ArchivoSeleccionadoNombre is not null)"
                      Click="ProcesarArchivo" />
        </div>
    </BodyContentTemplate>
</DxPopup>

@code {
    private bool _isVisible;

    [Parameter]
    public bool IsVisible
    {
        get => _isVisible;
        set
        {
            if (_isVisible != value)
            {
                _isVisible = value;
                _ = OnIsVisibleChangedAsync(value);
            }
        }
    }

    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public string ImportEndpoint { get; set; } = string.Empty;
    [Parameter] public EventCallback<string> OnArchivoSubido { get; set; }
    [Parameter] public EventCallback<string> OnImportacionFinalizada { get; set; }

    private IBrowserFile? ArchivoSeleccionado;
    private string? ArchivoSeleccionadoNombre;
    private bool intentoSinArchivo = false;
    private bool IsUploading = false;

    private DotNetObjectReference<PopupImportarExcel>? dotNetRef;

    private async Task OnIsVisibleChangedAsync(bool visible)
    {
        if (visible)
        {
            ArchivoSeleccionado = null;
            ArchivoSeleccionadoNombre = null;
            intentoSinArchivo = false;

            if (dotNetRef == null)
                dotNetRef = DotNetObjectReference.Create(this);

            await Task.Delay(50);

            await JS.InvokeVoidAsync("blazorFileDrop.init", "drop-zone", "inputExcelFile", dotNetRef);
        }

        await IsVisibleChanged.InvokeAsync(visible);
        StateHasChanged();
    }

    private void OnFileSelected(InputFileChangeEventArgs e)
    {
        intentoSinArchivo = false;

        if (e.FileCount > 0)
        {
            var archivo = e.File;

            if (archivo.Name.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                ArchivoSeleccionado = archivo;
                ArchivoSeleccionadoNombre = archivo.Name;
            }
            else
            {
                ArchivoSeleccionado = null;
                ArchivoSeleccionadoNombre = null;
                ToastService.MostrarError("El archivo debe tener extensión .xlsx");
            }
        }
        else
        {
            ArchivoSeleccionado = null;
            ArchivoSeleccionadoNombre = null;
        }
    }

    private async Task ProcesarArchivo()
    {
        intentoSinArchivo = true;

        if (ArchivoSeleccionado is null)
        {
            ToastService.MostrarError("Debes seleccionar un archivo.");
            return;
        }

        IsUploading = true;

        try

        {
            var content = new MultipartFormDataContent();
            var stream = ArchivoSeleccionado.OpenReadStream(15_000_000);
            var fileContent = new StreamContent(stream)
            {
                Headers = { ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") }
            };

            content.Add(fileContent, "excelFile", ArchivoSeleccionado.Name);

            var response = await Http.PostAsync(NavigationManager.ToAbsoluteUri(ImportEndpoint), content);
            var json = await response.Content.ReadAsStringAsync();

            if (string.IsNullOrWhiteSpace(json))
            {
                ToastService.MostrarError("La respuesta del servidor está vacía o no es válida.");
                await OnImportacionFinalizada.InvokeAsync("La respuesta del servidor está vacía.");
                return;
            }

            Result? result;
            try
            {
                result = JsonSerializer.Deserialize<Result>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (JsonException jsonEx)
            {
                ToastService.MostrarError($"Error al interpretar la respuesta del servidor: {jsonEx.Message}");
                await OnImportacionFinalizada.InvokeAsync($"Error JSON: {jsonEx.Message}");
                return;
            }

            if (response.IsSuccessStatusCode && result?.Success == true)
            {
                ToastService.MostrarOk(result.Message ?? "Importación completada correctamente.");
                await OnArchivoSubido.InvokeAsync(ArchivoSeleccionado.Name);
                await OnImportacionFinalizada.InvokeAsync(result.Message ?? "Importación completada.");
                IsVisible = false;
            }
            else
            {
                var errores = result?.Errors?.Any() == true
                  ? string.Join("<br>", result.Errors!)
                  : "Error durante la importación.";

                ToastService.MostrarError($"{result?.Message}<br>{errores}");
                await OnImportacionFinalizada.InvokeAsync($"{result?.Message}<br>{errores}");
            }
        }
        catch (Exception ex)
        {
            ToastService.MostrarError($"Error inesperado: {ex.Message}");
            await OnImportacionFinalizada.InvokeAsync($"Error inesperado: {ex.Message}");
        }
        finally
        {
            IsUploading = false;
            ArchivoSeleccionado = null;
            ArchivoSeleccionadoNombre = null;
            intentoSinArchivo = false;
        }
    }

    [JSInvokable]
    public Task SetFileFromDrop(string fileName)
    {
        ArchivoSeleccionadoNombre = fileName;
        StateHasChanged();
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        dotNetRef?.Dispose();
    }

    private void QuitarArchivo()
    {
        ArchivoSeleccionado = null;
        ArchivoSeleccionadoNombre = null;
        StateHasChanged();
    }
}