﻿@page "/GestionCodigoEnvases"
@attribute [Authorize(Roles = "materiales, admin, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm" DataItemDeleting="Grid_DataItemDeleting"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var codigoEnvase = (CodigoEnvasesDTO)context.DataItem;
                    <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(codigoEnvase)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                }
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="CodigoDescripcion" Caption="Barniz" />
        <DxGridDataColumn FieldName="PesoNeto" Caption="Peso Neto" />
    </Columns>
    <ToolbarTemplate>
        <ToolbarExcelImportExport Config="ExcelImportConfig.CodigoEnvases"
                                  OnArchivoSubido="CargarGrid"
                                  ImportEndpoint="api/Upload/CodigoEnvases"
                                  OnImportacionFinalizada="OnImportacionFinalizada" />
    </ToolbarTemplate>
</DxGrid>
<EditCodigoEnvasePopUp @ref="editCodigoEnvasePopUp" OnSave="GuardarCambios" MaterialesList="MaterialesList" MaterialSeleccionado="MaterialSeleccionado" />

@code{
    bool _isLoading { get; set; }
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<CodigoEnvasesDTO> Data = new();
    string GridSearchText = "";
    private EditCodigoEnvasePopUp? editCodigoEnvasePopUp;
    private CodigoEnvasesDTO? selectedCodigoEnvase { get; set; }
    private List<MaterialComboItem> MaterialesList = new();
    private MaterialComboItem? MaterialSeleccionado { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarMateriales();
        await CargarGrid();

        _isLoading = false;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (CodigoEnvasesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllCodigosEnvases();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<CodigoEnvasesDTO>(result.Data);

            foreach (var item in Data)
            {
                var material = MaterialesList.FirstOrDefault(m => m.Codigo == item.CodigoBarniz);
                item.CodigoDescripcion = material?.Display ?? item.CodigoBarniz.ToString();
            }

            StateHasChanged();
        }
    }

    private async Task CargarMateriales()
    {
        var result = await MigracionSAPParteTecnicaService.GetMaterialesByTipo("BARNIZ");
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            MaterialesList = result.Data.Select(m => new MaterialComboItem
            {
                Codigo = m.Codigo,
                Nombre = m.Descripcion ?? ""
            }).ToList();
        }
    }

    void AbrirPopUp(object dataItem)
    {
        selectedCodigoEnvase = dataItem as CodigoEnvasesDTO ?? new CodigoEnvasesDTO();

        MaterialSeleccionado = MaterialesList
         .FirstOrDefault(c => c.Codigo == selectedCodigoEnvase.CodigoBarniz);

        editCodigoEnvasePopUp?.AbrirPopUp(selectedCodigoEnvase);
    }

    async Task GuardarCambios(CodigoEnvasesDTO updatedCodigoEnvases)
    {
        _isLoading = true;
        selectedCodigoEnvase = updatedCodigoEnvases;

        var result = await MigracionSAPParteTecnicaService.TratarCodigoEnvase(selectedCodigoEnvase);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (CodigoEnvasesDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteCodigoEnvase(item.Id);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Código envase eliminado correctamente.");
        }

        _isLoading = false;
    }

    private async Task OnImportacionFinalizada(string mensaje)
    {
        await CargarGrid();
    }
}