﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editNodriza?.Id > 0)
{
    <EntityLockManager EntityType="Nodrizas"
                       EntityId="editNodriza.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Nodriza"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Esta nodriza está siendo editada por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editNodriza" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editNodriza" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Número de nodriza:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/nodriza_ico_black.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editNodriza.Nodriza" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Fecha:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-calendar" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxDateEdit @bind-Date="editNodriza.Fecha" TimeSectionVisible="true" CssClass="cw-320" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Código de barniz:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/waterdrop-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxComboBox Data="@MaterialesList"
                                        Value="MaterialSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((MaterialComboItem? selected) => OnMaterialChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" 
                                        ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Lote:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-archive" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editNodriza.Lote" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Cantidad:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/kilograms-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editNodriza.Cantidad" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<NodrizasDTO> OnSave { get; set; }
    [Parameter] public List<MaterialComboItem> MaterialesList { get; set; } = new();
    [Parameter] public MaterialComboItem? MaterialSeleccionado { get; set; }
    private NodrizasDTO editNodriza = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(NodrizasDTO nodriza)
    {
        editNodriza = nodriza;


        if (MaterialesList != null && MaterialesList.Any())
        {
            MaterialSeleccionado = MaterialesList
                .FirstOrDefault(c => c.Codigo == editNodriza.CodigoBarniz);
        }

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    private void OnMaterialChanged(MaterialComboItem? selected)
    {
        MaterialSeleccionado = selected;

        if (selected != null)
        {
            editNodriza.CodigoBarniz = selected.Codigo;
        }
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = NodrizasValidator.Validar(editNodriza);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editNodriza);
        IsPopupVisible = false;
        ToastService.MostrarOk("Nodriza guardada correctamente.");
    }
}