﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
public class GetImposicionByIdQuery : IRequest<SingleResult<ImposicionesDTO>>
{
    public int Id { get; set; }
    public GetImposicionByIdQuery(int id)
    {
        Id = id;
    }
}

internal class GetImposicionByIdQueryHandler : IRequestHandler<GetImposicionByIdQuery, SingleResult<ImposicionesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetImposicionByIdQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<SingleResult<ImposicionesDTO>> Handle(GetImposicionByIdQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<ImposicionesDTO>
        {
            Data = null,
            Errors = new List<string>()
        };

        try
        {
            var imposicion = await _migracionSAPParteTecnicaContext.Imposiciones
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);
            if (imposicion == null)
            {
                result.Errors.Add($"ERROR: No se encontró la imposición con ID {request.Id}");
            }
            else
            {
                result.Data = TinyMapper.Map<ImposicionesDTO>(imposicion);
            }
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetImposicionByIdQuery - {e.InnerException?.Message ?? e.Message}");
        }

        return result;
    }
}