﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.CV.Troqueles.Command;
public class DuplicarTroquelCommand : IRequest<SingleResult<TroquelesDTO>>
{
    public int Id { get; set; }
    public DuplicarTroquelCommand(int id)
    {
        Id = id;
    }
}

public class DuplicarTroquelCommandHandler : IRequestHandler<DuplicarTroquelCommand, SingleResult<TroquelesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public DuplicarTroquelCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }
    public async Task<SingleResult<TroquelesDTO>> Handle(DuplicarTroquelCommand request, CancellationToken cancellationToken)
    {
        var original = await _migracionSAPParteTecnicaContext.Troqueles
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (original == null)
            return SingleResult<TroquelesDTO>.Error("Troquel no encontrado.");

        try
        {
            var nuevo = TinyMapper.Map<Entities.MigracionSAPParteTecnica.CV.Troqueles>(original);

            nuevo.Id = 0;
            nuevo.FechaAlta = DateTime.UtcNow;

            _migracionSAPParteTecnicaContext.Troqueles.Add(nuevo);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            var dto = TinyMapper.Map<TroquelesDTO>(nuevo);

            return SingleResult<TroquelesDTO>.Ok(dto);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: DuplicarTroquel - {e.InnerException?.Message ?? e.Message}";
            return SingleResult<TroquelesDTO>.Error(errorText);
        }
    }
}