﻿@inherits LayoutComponentBase

<DxToastProvider FreezeOnClick="true"
				 HorizontalAlignment="HorizontalAlignment.Right"
				 VerticalAlignment="VerticalEdge.Top"
				 ShowCloseButton="true"
				 DisplayTime="@TimeSpan.FromSeconds(3)"
				 SizeMode="SizeMode.Medium" />
<DxLayoutBreakpoint DeviceSize="DeviceSize.XSmall" IsActive="IsXSmallScreen" IsActiveChanged="IsActiveChanged" />
<div class="card border-0 h-100">
	<div class="d-flex justify-content-start text-white navbar-header">
		<div class="contenedor-icono-hamburger px-3 border-end d-flex align-items-center justify-content-center">
			<button class="navbar-toggler" @onclick="ToggleDrawer">
				<span class="navbar-toggler-icon"></span>
			</button>
		</div>

		<div class="px-3 d-flex justify-content-between w-100">
			<div class="fw-bold d-flex align-items-center">
				<NavLink class="nav-link" href="Login" Match="NavLinkMatch.All">
					<img class="img-fluid" style="max-height: 45px;" src="/images/logo-blanco.png" />
				</NavLink>
			</div>

			<div class="d-flex align-items-center">
				<NavLink class="nav-link" href="" Match="NavLinkMatch.All">
					<span class="fw-bold" style="font-size: 18px;">Gestión SAP</span>
				</NavLink>
			</div>
		</div>
	</div>

	<DxDrawer @bind-IsOpen="IsOpen"
			  Mode="Mode"
			  PanelWidth="290px"
			  MiniModeEnabled="true"
			  MiniPanelWidth="65px"
			  ApplyBackgroundShading="true">
		<BodyTemplate>
			<div class="flex-column h-100 w-100">
				<AuthorizeView Roles="admin">
					
				</AuthorizeView>
				<AuthorizeView Roles="admin,almacen,logistica, preprint, calidad, barnices, mmcesar">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionSAPClientes" Match="NavLinkMatch.All"
								 title="Gestión clientes">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-person icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Clientes</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionSAPDestinatarios" Match="NavLinkMatch.All"
								 title="Gestión destinatarios">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-people icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Destinatarios</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionSAPProveedores" Match="NavLinkMatch.All"
								 title="Gestión destinatarios">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-person-down icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Proveedores</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView Roles="admin,calidad">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionTiposCaracteristicas" Match="NavLinkMatch.All"
								 title="Gestión tipos característica">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/tipos_caracteristicas-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Tipos característica</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionValoresTiposCaracteristicas" Match="NavLinkMatch.All"
								 title="Gestión valores de tipos característica">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/valores_tipos_caracteristica-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Valores de tipos</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionMetodosInspeccion" Match="NavLinkMatch.All"
								 title="Gestión métodos de inspección">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/metodos_inspeccion-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Métodos de inspección</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionCaracteristicasInspeccion" Match="NavLinkMatch.All"
								 title="Gestión características de inspección">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/valores_metodos_inspeccion-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Características de inspección</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView Roles="preprint, admin">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionMaestro" Match="NavLinkMatch.All"
								 title="Gestión Tabla Maestro">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-archive icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Tabla Maestro</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionMotivos" Match="NavLinkMatch.All"
								 title="Gestión Tabla Motivos">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-folder2-open icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Tabla Motivos</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionTiposElemento" Match="NavLinkMatch.All"
								 title="Gestión Tipos de Elemento">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/imposicion_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Tipos de elemento</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionPlanos" Match="NavLinkMatch.All"
								 title="Gestión planos">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/plano_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Planos</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionTroqueles" Match="NavLinkMatch.All"
								 title="Gestión troqueles">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/troquel_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Troqueles</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionImposiciones" Match="NavLinkMatch.All"
								 title="Gestión imposiciones">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/imposicion_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Imposiciones</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView Roles="barnices, admin, mmcesar">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionCodigoEnvases" Match="NavLinkMatch.All"
								 title="Gestión código envases">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-upc-scan icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Códigos envase</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionExcepcionesPreciosLotes" Match="NavLinkMatch.All"
								 title="Gestión excepciones precio por lote">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-cash-coin icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Excepciones precio</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionFamilias" Match="NavLinkMatch.All"
								 title="Gestión familias">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/familia_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Familias</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionNaturalezas" Match="NavLinkMatch.All"
								 title="Gestión Naturalezas">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-layers icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Naturalezas</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionNodrizas" Match="NavLinkMatch.All"
								 title="Gestión Nodrizas">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/nodriza_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Nodrizas</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionNoIncluirRegCompras" Match="NavLinkMatch.All"
								 title="Gestión No Incluir Registro de compras">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-slash-circle icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">No registro</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView Roles="materiales, admin">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionDatosAux" Match="NavLinkMatch.All"
								 title="Gestión Datos Auxiliares">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-tools icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Datos auxiliares</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionPaquetesConsignaNestle" Match="NavLinkMatch.All"
								 title="Gestión Paquetes Consigna Nestle">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/pajaro_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Consigna Nestle</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView Roles="materiales, admin, mmcesar">
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionEnvases" Match="NavLinkMatch.All"
								 title="Gestión Envases">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/envase-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Envases</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionMateriales" Match="NavLinkMatch.All"
								 title="Gestión Materiales">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/materiales_ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Materiales</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionColores" Match="NavLinkMatch.All"
								 title="Gestión Colores">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<span class="bi bi-palette icono-sidebar"></span>
							</div>
							<span class="ms-3 text-nowrap">Colores</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionTintasCorrespondenciasSKUs" Match="NavLinkMatch.All"
								 title="Gestión Correspondencias">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/correspondencia-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Correspondencias</span>
						</NavLink>
					</div>
					<div class="nav-item">
						<NavLink class="nav-link mx-2" href="GestionTintasRelacionCompuestas" Match="NavLinkMatch.All"
								 title="Gestión Tintas Relación Compuestas">
							<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								<img src="Images/relacion-ico.svg" alt="Icono Nodriza" style="width: 24px; height: 24px;" />
							</div>
							<span class="ms-3 text-nowrap">Relación Compuestas</span>
						</NavLink>
					</div>
				</AuthorizeView>
				<AuthorizeView>
					<Authorized>
						<div class="nav-item">
							<NavLink class="nav-link mx-2" href="/authentication/logout" Match="NavLinkMatch.All"
									 title="Cerrar sesión">
								<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
									<span class="bi bi-box-arrow-right icono-sidebar"></span>
								</div>
								<span class="ms-3 text-nowrap">Cerrar sesión</span>
							</NavLink>
						</div>
					</Authorized>
				</AuthorizeView>
			</div>
		</BodyTemplate>
		<TargetContent>
			<div id="div-body" class="w-100 h-100 m-0 p-4 overflow-auto d-flex flex-column">
				@Body
			</div>
		</TargetContent>
	</DxDrawer>
</div>

<div id="blazor-error-ui">
	An unhandled error has occurred.
	<a href="" class="reload">Reload</a>
	<a class="dismiss">🗙</a>
</div>

@code {
	// https://docs.devexpress.com/Blazor/DevExpress.Blazor.DxMenu
	// https://demos.devexpress.com/blazor/Drawer#Position
	private bool IsXSmallScreen { get; set; }
	private bool? _isOpen = false; // por defecto cerrado en vista completa
	private bool? _isOpenBigScreen = true; // estado guardado para pantallas grandes

	private bool IsOpen
	{
		get => _isOpen ?? !IsXSmallScreen;
		set => _isOpen = value;
	}

	private DrawerMode Mode => IsXSmallScreen ? DrawerMode.Overlap : DrawerMode.Shrink;

	private void IsActiveChanged(bool isXSmall)
	{
		bool previousIsXSmall = IsXSmallScreen;
		IsXSmallScreen = isXSmall;

		if (isXSmall)
		{
			if (!previousIsXSmall)
				_isOpenBigScreen = _isOpen;
			_isOpen = false;
		}
		else
		{
			if (previousIsXSmall && _isOpen == true) 
				_isOpen = true;
			else
				_isOpen = _isOpenBigScreen;
		}
	}

	private void ToggleDrawer()
	{
		IsOpen = !IsOpen;

		if (!IsXSmallScreen)
			_isOpenBigScreen = IsOpen;
	}
}