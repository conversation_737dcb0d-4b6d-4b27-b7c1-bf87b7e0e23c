﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.Planos.Query;
public class GetAllPlanosQuery : IRequest<ListResult<PlanoDTO>>
{
}

internal class GetAllPlanosQueryHandler : IRequestHandler<GetAllPlanosQuery, ListResult<PlanoDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllPlanosQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<PlanoDTO>> Handle(GetAllPlanosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<PlanoDTO>
        {
            Data = new List<PlanoDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listPlanos = await _migracionSAPParteTecnicaContext.Plano
                .AsNoTracking()
                .ToListAsync();

            result.Data = TinyMapper.Map<List<PlanoDTO>>(listPlanos).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllPlanos - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}