﻿using DocumentFormat.OpenXml.InkML;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Data.Servin;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.DatosMaestros.Command;

public class ActualizarMaterialesCommand : IRequest<Result>
{
}

internal class ActualizarMaterialesCommandHandler : IRequestHandler<ActualizarMaterialesCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly ServinContext _servinContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ActualizarMaterialesCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, ServinContext servinContext, IAuditoriaService auditoriaService,
        IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _servinContext = servinContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ActualizarMaterialesCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        int insertados = 0;
        int actualizados = 0;
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var externos = await _servinContext.MaterialesBruto.ToListAsync(cancellationToken);
            var locales = await _migracionSAPParteTecnicaContext.Materiales.ToListAsync(cancellationToken);

            foreach (var externo in externos)
            {
                var existente = locales.FirstOrDefault(m => m.Codigo == externo.Codigo);

                if (existente == null)
                {
                    var nuevo = new Entities.MigracionSAPParteTecnica.MM.General.Materiales
                    {
                        Codigo = externo.Codigo,
                        Descripcion = externo.Descripcion,
                        Tipo = externo.Tipo
                    };
                    _migracionSAPParteTecnicaContext.Materiales.Add(nuevo);
                    insertados++;

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "MM_Materiales",
                        IdRegistro = nuevo.Codigo,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(nuevo),
                        Comentarios = "Material creado por sincronización"
                    }, cancellationToken);
                }
                else
                {
                    if (existente.Descripcion != externo.Descripcion || existente.Tipo != externo.Tipo)
                    {
                        var datosAntes = JsonSerializer.Serialize(new
                        {
                            existente.Codigo,
                            existente.Descripcion,
                            existente.Tipo
                        });

                        existente.Descripcion = externo.Descripcion;
                        existente.Tipo = externo.Tipo;
                        actualizados++;

                        var datosDespues = JsonSerializer.Serialize(new
                        {
                            existente.Codigo,
                            existente.Descripcion,
                            existente.Tipo
                        });

                        await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                        {
                            Usuario = usuario,
                            Fecha = DateTime.Now,
                            Accion = "UPDATE",
                            Tabla = "MM_Materiales",
                            IdRegistro = existente.Codigo,
                            DatosAntes = datosAntes,
                            DatosDespues = datosDespues,
                            Comentarios = "Material modificado por sincronización"
                        }, cancellationToken);
                    }
                }
            }
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Success = true;
            result.Message = $"Materiales actualizados correctamente. Insertados: {insertados}, Actualizados: {actualizados}";
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al actualizar materiales: {ex.Message}";
        }

        return result;
    }
}