﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using DevExpress.Blazor
@using DevExpress.Blazor.Internal
@using Blazr.RenderState
@using System.Collections.ObjectModel
@using System.Net.Http.Headers
@using System.Text.Json
@using System.Diagnostics

@using MigracionSAPParteTecnica.Entities.ViewModels;
@using MigracionSAPParteTecnica.Entities.Eventos;
@using MigracionSAPParteTecnica.Components.Layout.Popups.CV
@using MigracionSAPParteTecnica.Components.Layout.Popups.QM
@using MigracionSAPParteTecnica.Components.Layout.Popups.MM.Barnices
@using MigracionSAPParteTecnica.Components.Layout.Popups.MM.BobinasPaquetes
@using MigracionSAPParteTecnica.Components.Layout.Popups.MM.General
@using MigracionSAPParteTecnica.Components.Layout.Popups.MM.Tintas
@using MigracionSAPParteTecnica.Components.Layout.Popups.General
@using MigracionSAPParteTecnica
@using MigracionSAPParteTecnica.Components
@using MigracionSAPParteTecnica.Components.Layout
@using MigracionSAPParteTecnica.DTO.CV
@using MigracionSAPParteTecnica.DTO.QM
@using MigracionSAPParteTecnica.DTO.MM.Barnices
@using MigracionSAPParteTecnica.DTO.MM.BobinasPaquetes
@using MigracionSAPParteTecnica.DTO.MM.General
@using MigracionSAPParteTecnica.DTO.MM.Tintas
@using MigracionSAPParteTecnica.DTO.General
@using MigracionSAPParteTecnica.DTO.ResponseModels
@using MigracionSAPParteTecnica.Components.Common
@using MigracionSAPParteTecnica.Validators.QM
@using MigracionSAPParteTecnica.Validators.CV
@using MigracionSAPParteTecnica.Validators.MM.Barnices
@using MigracionSAPParteTecnica.Validators.MM.BobinasPaquetes
@using MigracionSAPParteTecnica.Validators.MM.Tintas
@using MigracionSAPParteTecnica.Validators.General

@using MigracionSAPParteTecnica.Services
@using MigracionSAPParteTecnica.Seeders
@using MigracionSAPParteTecnica.Entities

@using MigracionSAPParteTecnica.Helpers
@using System.Threading;

@using static MigracionSAPParteTecnica.Enums