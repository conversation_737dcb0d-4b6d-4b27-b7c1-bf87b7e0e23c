﻿using DocumentFormat.OpenXml.InkML;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;
using MigracionSAPParteTecnica.Services;
using System.Globalization;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.Planos.Command;

public class ActualizarPlanosCommand : IRequest<Result> { }

internal class ActualizarPlanosCommandHandler : IRequestHandler<ActualizarPlanosCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly string _rutaRaizPlanos = @"\\192.48.48.124\Planos";

    public ActualizarPlanosCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ActualizarPlanosCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";
        var ahora = DateTime.Now;

        try
        {
            var clientes = await _migracionSAPParteTecnicaContext.SAP_Clientes
                .ToDictionaryAsync(c => c.Codigo_IN2, c => c.Codigo_SAP, cancellationToken);

            var planosEncontrados = new HashSet<(int, string)>();
            int insertados = 0, actualizados = 0, desactivados = 0;

            foreach (var carpetaCliente in Directory.GetDirectories(_rutaRaizPlanos))
            {
                var nombreCarpeta = Path.GetFileName(carpetaCliente);
                if (nombreCarpeta.Length < 5 || !int.TryParse(nombreCarpeta[..5], out int codIn2))
                    continue;

                if (!clientes.TryGetValue(codIn2, out int codSap))
                    continue;

                var archivos = Directory.GetFiles(carpetaCliente, "*.pdf", SearchOption.AllDirectories);
                foreach (var archivo in archivos)
                {
                    var rutaRelativa = archivo[(carpetaCliente.Length + 1)..];
                    var nombrePlano = Path.GetFileNameWithoutExtension(archivo);
                    bool esRetirado = rutaRelativa.ToLower().Contains("retirado");

                    string nombrePlanoUpper = nombrePlano.ToUpperInvariant();
                    var clave = (codSap, nombrePlanoUpper);

                    if (planosEncontrados.Add(clave))
                    {
                        var planoExistente = await _migracionSAPParteTecnicaContext.Plano
                            .FirstOrDefaultAsync(p =>
                                p.Cliente == codSap &&
                                p.NombrePlano.ToUpper() == nombrePlanoUpper,
                                cancellationToken);

                        if (planoExistente != null)
                        {
                            if (planoExistente.Activo == esRetirado)
                            {
                                var datosAntes = JsonSerializer.Serialize(planoExistente);
                                planoExistente.Activo = !esRetirado;

                                actualizados++;
                            }
                        }
                        else
                        {
                            var nuevoPlano = new Plano
                            {
                                Cliente = codSap,
                                NombrePlano = nombrePlano,
                                Activo = !esRetirado,
                                Borrado = false
                            };
                            _migracionSAPParteTecnicaContext.Plano.Add(nuevoPlano);
                            insertados++;
                        }
                    }
                }
            }

            var planosActivos = await _migracionSAPParteTecnicaContext.Plano
                .Where(p => p.Activo)
                .ToListAsync(cancellationToken);

            foreach (var plano in planosActivos)
            {
                if (!planosEncontrados.Contains((plano.Cliente, plano.NombrePlano.ToUpperInvariant())))
                {
                    plano.Activo = false;
                    desactivados++;
                }
            }

            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = ahora,
                Accion = "MASS UPDATE",
                Tabla = "CV_Plano",
                IdRegistro = 0,
                DatosAntes = null,
                DatosDespues = null,
                Comentarios = $"Actualización masiva de planos. Insertados: {insertados}, Actualizados: {actualizados}, Desactivados: {desactivados}"
            }, cancellationToken);

            result.Success = true;
            result.Message = "Sincronización de planos completada correctamente.";
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al sincronizar planos: {ex.Message}";

            if (ex.InnerException != null)
            {
                result.Message += $" | InnerException: {ex.InnerException.Message}";
            }

            result.Errors.Add(result.Message);
        }

        return result;
    }
}