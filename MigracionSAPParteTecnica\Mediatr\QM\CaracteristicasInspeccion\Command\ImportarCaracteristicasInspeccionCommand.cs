﻿using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Services;
using MigracionSAPParteTecnica.Validators.QM;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.CaracteristicasInspeccion.Command;

public class ImportarCaracteristicasInspeccionCommand : IRequest<Result>
{
    public Stream ExcelStream { get; set; }

    public ImportarCaracteristicasInspeccionCommand(Stream excelStream)
    {
        ExcelStream = excelStream;
    }
}

internal class ImportarCaracteristicasInspeccionCommandHandler : IRequestHandler<ImportarCaracteristicasInspeccionCommand, Result>
{
    private readonly MigracionSAPParteTecnicaContext _context;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ImportarCaracteristicasInspeccionCommandHandler(MigracionSAPParteTecnicaContext context, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _context = context;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Result> Handle(ImportarCaracteristicasInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new Result();
        var errores = new List<string>();

        try
        {
            using var workbook = new XLWorkbook(request.ExcelStream);
            var worksheet = workbook.Worksheet(1);
            var rows = worksheet.RowsUsed().Skip(1);

            int insertados = 0, actualizados = 0, descartados = 0;
            var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

            foreach (var row in rows)
            {
                var metodoNombre = row.Cell(4).GetString().Trim();
                var metodo = await _context.MetodosInspeccion
                    .FirstOrDefaultAsync(m => m.Nombre == metodoNombre, cancellationToken);

                if (metodo == null)
                {
                    errores.Add($"Fila {row.RowNumber()}: El método de inspección '{metodoNombre}' no existe.");
                    descartados++;
                    continue;
                }

                var dto = new CaracteristicasInspeccionDTO
                {
                    Nombre = row.Cell(1).GetString().Trim(),
                    CopiarReferencia = row.Cell(2).GetBoolean(),
                    CampoBusqueda = row.Cell(3).GetString().Trim(),
                    MetodoIns = metodo.Id,
                    CopiaCatalogo = row.Cell(5).GetBoolean(),
                    Cuantitativa = row.Cell(6).GetBoolean(),
                    Cualitativa = row.Cell(7).GetBoolean(),
                    ToleranciaInf = row.Cell(8).GetBoolean(),
                    ToleranciaSup = row.Cell(9).GetBoolean(),
                    Categoria = row.Cell(10).GetBoolean(),
                    NumeroDecimales = row.Cell(11).GetValue<int?>(),
                    LimiteToleranciaInf = row.Cell(12).GetValue<decimal?>(),
                    LimiteToleranciaSup = row.Cell(13).GetValue<decimal?>(),
                    TextoBreve = row.Cell(14).GetString().Trim(),
                    UnidadMedida = row.Cell(15).GetString().Trim(),
                    Borrado = false
                };

                var error = CaracteristicasInspeccionValidator.Validar(dto);
                if (error != null)
                {
                    errores.Add($"Fila {row.RowNumber()}: {error}");
                    descartados++;
                    continue;
                }

                var existente = await _context.CaracteristicasInspeccion
                    .FirstOrDefaultAsync(c => c.Nombre == dto.Nombre, cancellationToken);

                var idAuditoria = dto.Nombre.GetHashCode();

                if (existente == null)
                {
                    var nueva = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.CaracteristicasInspeccion>(dto);
                    await _context.CaracteristicasInspeccion.AddAsync(nueva, cancellationToken);
                    await _context.SaveChangesAsync(cancellationToken);

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "INSERT",
                        Tabla = "CaracteristicasInspeccion",
                        IdRegistro = nueva.Id,
                        DatosAntes = null,
                        DatosDespues = JsonSerializer.Serialize(nueva),
                        Comentarios = "Importado desde Excel"
                    }, cancellationToken);

                    insertados++;
                }
                else
                {
                    var datosAntes = JsonSerializer.Serialize(existente);

                    TinyMapper.Map(dto, existente);
                    await _context.SaveChangesAsync(cancellationToken);

                    var datosDespues = JsonSerializer.Serialize(existente);

                    await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                    {
                        Usuario = usuario,
                        Fecha = DateTime.Now,
                        Accion = "UPDATE",
                        Tabla = "CaracteristicasInspeccion",
                        IdRegistro = existente.Id,
                        DatosAntes = datosAntes,
                        DatosDespues = datosDespues,
                        Comentarios = $"Actualizado desde Excel. TextoBreve: '{existente.TextoBreve}'"
                    }, cancellationToken);

                    actualizados++;
                }
            }

            result.Success = true;
            result.Message = $"Importación completada. Insertados: {insertados}, Actualizados: {actualizados}, Descartados: {descartados}.";
            result.Errors = errores;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Error al importar características de inspección: {ex.Message}";
        }

        return result;
    }
}
