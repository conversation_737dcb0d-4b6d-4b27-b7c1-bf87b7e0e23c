﻿using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica.CV;

namespace MigracionSAPParteTecnica.DTO.CV;
public class ImposicionesDTO
{
    public int Id { get; set; }

    public int? Cliente_SAP { get; set; }

    public int? Cliente_IN2 { get; set; }

    public string Plano { get; set; }

    public decimal? AnchoHoja { get; set; }

    public decimal? LargoHoja { get; set; }

    public int IdTroquel { get; set; }

    public int? Cuerpos { get; set; }

    public string SentidoLectura { get; set; }

    public bool? HojaScroll { get; set; }

    public decimal? LargoScroll { get; set; }

    public decimal? ArranquePinza { get; set; }

    public decimal? ArranqueEscuadra { get; set; }

    public int? NumeroAlturas { get; set; }

    public int? NumeroDesarrollos { get; set; }

    public int? PosicionEscuadraExt { get; set; }

    public bool? PosicionEscuadraInv { get; set; }

    public decimal? Pinza { get; set; }

    public string Tipo { get; set; }

    public string Nota { get; set; }

    public bool? Activo { get; set; }

    public DateTime? FechaRegistro { get; set; }

    public int? PedidoEjemplo { get; set; }

    public int? Cliente_IN2_Origen { get; set; }

    public int? Cliente_SAP_Origen { get; set; }

    public string Plano_Origen { get; set; }

    public decimal? AnchoHoja_Origen { get; set; }

    public decimal? LargoHoja_Origen { get; set; }

    public decimal? LargoScroll_Origen { get; set; }

    public int? NumeroCuerpos_Origen { get; set; }

    public int? IdTroquel_Origen { get; set; }

    public bool OrigenModificado { get; set; }

    public bool EsAutomatico { get; set; }

    public virtual TroquelesDTO IdTroquelNavigation { get; set; }

    public string? HojaScrollTexto => (HojaScroll ?? false) ? "Si" : "No";

    public string? PosicionEscuadraInvTexto => (PosicionEscuadraInv ?? false) ? "Si" : "No";

    public string ClienteNombreConcatenado { get; set; } = "";
}