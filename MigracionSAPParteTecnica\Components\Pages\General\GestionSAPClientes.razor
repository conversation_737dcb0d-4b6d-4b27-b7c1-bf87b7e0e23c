﻿@page "/GestionSAPClientes"
@attribute [Authorize(Roles = "admin,almacen,logistica,preprint, barnices, mmcesar")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService

<CustomLoadingPanel @bind-Visible="_isLoading" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="true"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridCommandColumn Width="60px">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirPopUp(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var cliente = (SAPClienteDTO)context.DataItem;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirPopUp(cliente)" style="text-decoration: none; padding-right: 15px; color: #c75fff;" />
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);" />
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id" Visible="false" />
        <DxGridDataColumn FieldName="Codigo_IN2" Caption="Código IN2" />
        <DxGridDataColumn FieldName="Codigo_SAP" Caption="Código SAP">
            <CellDisplayTemplate Context="context">
                @{
                    var destinatario = ((SAPClienteDTO)context.DataItem)?.Codigo_SAP;
                }
                <span>@(destinatario?.ToString() ?? "")</span>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Nombre" Caption="Nombre" />
    </Columns>
</DxGrid>
<EditSAPClientePopUp @ref="editSAPClientePopUp"  OnSave="GuardarCambios" />

@code{
    bool _isLoading;
    bool IsPopupVisible { get; set; }
    DxGrid? Grid;
    ObservableCollection<SAPClienteDTO> Data = new();
    string GridSearchText = "";
    private EditSAPClientePopUp? editSAPClientePopUp;
    private SAPClienteDTO? selectedSAPCliente { get; set; }

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllSAPClientes();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<SAPClienteDTO>(result.Data);
            StateHasChanged();
        }
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (SAPClienteDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    void AbrirPopUp(object dataItem)
    {
        selectedSAPCliente = dataItem as SAPClienteDTO ?? new SAPClienteDTO();
        editSAPClientePopUp?.AbrirPopUp(selectedSAPCliente);
    }

    async Task GuardarCambios(SAPClienteDTO updatedSAPCliente)
    {
        _isLoading = true;
        selectedSAPCliente = updatedSAPCliente;

        var result = await MigracionSAPParteTecnicaService.TratarSAPCliente(selectedSAPCliente);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            IsPopupVisible = false;
            await CargarGrid();
        }

        _isLoading = false;
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        _isLoading = true;
        var item = (SAPClienteDTO)e.DataItem;
        var result = await MigracionSAPParteTecnicaService.DeleteSAPCliente(item.Codigo_IN2);

        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            await CargarGrid();
            ToastService.MostrarOk("Cliente eliminado correctamente.");
        }

        _isLoading = false;
    }
}