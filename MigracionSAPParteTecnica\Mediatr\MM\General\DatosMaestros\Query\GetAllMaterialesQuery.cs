﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.MM.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.Planos.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.General.DatosMaestros.Query;
public class GetAllMaterialesQuery : IRequest<ListResult<MaterialesDTO>>
{
}

internal class GetAllMaterialesQueryHandler : IRequestHandler<GetAllMaterialesQuery, ListResult<MaterialesDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    public GetAllMaterialesQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<MaterialesDTO>> Handle(GetAllMaterialesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<MaterialesDTO>
        {
            Data = new List<MaterialesDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listMateriales = await _migracionSAPParteTecnicaContext.Materiales
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<MaterialesDTO>>(listMateriales).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllMaterialesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}