﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editCaracteristicaInspeccion?.Id > 0)
{
    <EntityLockManager EntityType="CaracteristicasInspeccion"
                       EntityId="editCaracteristicaInspeccion.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar características de inspección"
         Width="800px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este característica de inspección está siendo editada por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editCaracteristicaInspeccion" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editCaracteristicaInspeccion" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Nombre:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editCaracteristicaInspeccion.Nombre" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                            <InfoIcon BodyText="@Tooltips.NombreCaracteristicasInspeccion" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Campo de búsqueda:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-search" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editCaracteristicaInspeccion.CampoBusqueda" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Método de inspección:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-clipboard-check" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox Data="@MetodosInspeccionList"
                                        Value="MetodoInspeccionSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((MetodoInspeccionComboItem? selected) => OnMetodoInspeccionChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Copia catálogo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="3">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editCaracteristicaInspeccion.CopiaCatalogo"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween" ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Copia Referencia:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="3">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editCaracteristicaInspeccion.CopiarReferencia"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween" ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tipo de característica:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-sliders2" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox Data="@TiposCaracteristica"
                                        Value="TipoSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((string? tipo) => OnTipoCaracteristicaSeleccionChanged(tipo))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Número decimales:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item"  ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-three-dots" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editCaracteristicaInspeccion.NumeroDecimales" ReadOnly="@(_lectura || EsCualitativa)" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Unidad de medida:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-rulers" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editCaracteristicaInspeccion.UnidadMedida" ReadOnly="@(_lectura || EsCualitativa)" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tolerancia inferior:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item"  ColSpanMd="2">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editCaracteristicaInspeccion.ToleranciaInf"
                                    LabelPosition="LabelPosition.Left"
                                    ReadOnly="@(_lectura || EsCualitativa)"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Límite de tolerancia inferior:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrow-down" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editCaracteristicaInspeccion.LimiteToleranciaInf" ReadOnly="@(_lectura || EsCualitativa)"
                            CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tolerancia superior:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editCaracteristicaInspeccion.ToleranciaSup"
                                    LabelPosition="LabelPosition.Left"
                                    ReadOnly="@(_lectura || EsCualitativa)"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Límite de tolerancia superior:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrow-up" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editCaracteristicaInspeccion.LimiteToleranciaSup" ReadOnly="@(_lectura || EsCualitativa)"
                            CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Categoria:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="2">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editCaracteristicaInspeccion.Categoria"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween"
                                    ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Texto Breve:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-chat-left-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editCaracteristicaInspeccion.TextoBreve" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<CaracteristicasInspeccionDTO> OnSave { get; set; }
    [Parameter] public List<MetodoInspeccionComboItem> MetodosInspeccionList { get; set; } = new();
    private CaracteristicasInspeccionDTO editCaracteristicaInspeccion = new();
    private MetodoInspeccionComboItem? MetodoInspeccionSeleccionado;

    private string? TipoSeleccionado;
    private List<string> TiposCaracteristica = new() { "Cualitativa", "Cuantitativa" };
    private bool EsCualitativa => TipoSeleccionado == "Cualitativa";

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    private void OnTipoCaracteristicaChanged(MetodoInspeccionComboItem? selected)
    {
        MetodoInspeccionSeleccionado = selected;

        if (selected != null)
        {
            editCaracteristicaInspeccion.Nombre = selected.Nombre;
        }
    }

    private void OnMetodoInspeccionChanged(MetodoInspeccionComboItem? selected)
    {
        MetodoInspeccionSeleccionado = selected;
        if (selected != null)
            editCaracteristicaInspeccion.MetodoIns = selected.Id;
    }

    public void AbrirPopUp(CaracteristicasInspeccionDTO caracteristicaInspeccion)
    {
        editCaracteristicaInspeccion = caracteristicaInspeccion;

        MetodoInspeccionSeleccionado = MetodosInspeccionList
        .FirstOrDefault(m => m.Id == editCaracteristicaInspeccion.MetodoIns);

        TipoSeleccionado = editCaracteristicaInspeccion.Cualitativa ? "Cualitativa"
              : editCaracteristicaInspeccion.Cuantitativa ? "Cuantitativa"
              : null;

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = CaracteristicasInspeccionValidator.Validar(editCaracteristicaInspeccion);

        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        editCaracteristicaInspeccion.MetodoIns = MetodoInspeccionSeleccionado.Id;

        if (EsCualitativa)
        {
            editCaracteristicaInspeccion.NumeroDecimales = null;
            editCaracteristicaInspeccion.UnidadMedida = null;
            editCaracteristicaInspeccion.ToleranciaInf = false;
            editCaracteristicaInspeccion.ToleranciaSup = false;
            editCaracteristicaInspeccion.LimiteToleranciaInf = null;
            editCaracteristicaInspeccion.LimiteToleranciaSup = null;
        }

        await OnSave.InvokeAsync(editCaracteristicaInspeccion);
        IsPopupVisible = false;
        ToastService.MostrarOk("Característica de inspección guardada correctamente.");
    }

    private void OnTipoCaracteristicaSeleccionChanged(string? tipo)
    {
        TipoSeleccionado = tipo;

        if (tipo == "Cualitativa")
        {
            editCaracteristicaInspeccion.Cualitativa = true;
            editCaracteristicaInspeccion.Cuantitativa = false;

            editCaracteristicaInspeccion.NumeroDecimales = null;
            editCaracteristicaInspeccion.UnidadMedida = null;
            editCaracteristicaInspeccion.ToleranciaInf = false;
            editCaracteristicaInspeccion.ToleranciaSup = false;
            editCaracteristicaInspeccion.LimiteToleranciaInf = null;
            editCaracteristicaInspeccion.LimiteToleranciaSup = null;
        }
        else if (tipo == "Cuantitativa")
        {
            editCaracteristicaInspeccion.Cualitativa = false;
            editCaracteristicaInspeccion.Cuantitativa = true;
        }

        StateHasChanged();
    }
}