﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Command;
using MigracionSAPParteTecnica.Services;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.ExcepcionesPreciosLotes.Command;

public class DeleteExcepcionPrecioLoteCommand : IRequest<SingleResult<bool>>
{
    public int Id { get; set; }
    public DeleteExcepcionPrecioLoteCommand(int id)
    {
        Id = id;
    }
}

internal class DeleteExcepcionPrecioLoteCommandHandler : IRequestHandler<DeleteExcepcionPrecioLoteCommand, SingleResult<bool>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeleteExcepcionPrecioLoteCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<bool>> Handle(DeleteExcepcionPrecioLoteCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = false,
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var excepcionPrecio = await _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes
                .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

            if (excepcionPrecio == null)
            {
                result.Errors.Add("No se ha encontrado la excepción de precio a borrar.");
                return result;
            }

            var datosAntes = JsonSerializer.Serialize(excepcionPrecio);

            excepcionPrecio.Borrado = true;

            _migracionSAPParteTecnicaContext.ExcepcionesPreciosLotes.Update(excepcionPrecio);
            await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

            result.Data = true;

            await _auditoriaService.RegistrarAsync(new AuditoriaDTO
            {
                Usuario = usuario,
                Fecha = DateTime.Now,
                Accion = "DELETE",
                Tabla = "MM_Barnices_ExcepcionesPreciosLotes",
                IdRegistro = excepcionPrecio.Id,
                DatosAntes = datosAntes,
                DatosDespues = null,
                Comentarios = $"Eliminación lógica de excepción precio lote: Id={excepcionPrecio.Id}"
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: DeleteExcepcionPrecioLoteCommand - {(ex.InnerException?.Message ?? ex.Message)}";

            result.Errors.Add(errorText);
        }

        return result;
    }
}