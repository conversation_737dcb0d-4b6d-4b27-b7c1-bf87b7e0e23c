﻿using MigracionSAPParteTecnica.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;

namespace MigracionSAPParteTecnica.Seeders;
public class SeedIdentity
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();

        string[] roles = new[] { "admin", "almacen", "logistica", "preprint", "calidad", "materiales", "barnices", "mmcesar"};

        foreach(var role in roles)
        {
            if (!await roleManager.RoleExistsAsync(role))
            {
                await roleManager.CreateAsync(new IdentityRole(role));
            }
        }

        //await <PERSON>rear<PERSON>suario("admin", "<EMAIL>", "Adm1n!Secure#2025", "admin", userManager);
        //await <PERSON><PERSON>r<PERSON><PERSON><PERSON>("prueba", "<EMAIL>", "Adm1n!Secure#2025", "almacen", userManager);
        //await CrearUsuario("borja", "<EMAIL>", "BorjaFresquisimo99.", "preprint", userManager);
        //await CrearUsuario("alvaro", "<EMAIL>", "Calidad2025y.", "calidad", userManager);
        //await CrearUsuario("esteban", "<EMAIL>", "Esteban2025y.", "admin", userManager);
        //await CrearUsuario("roberto", "<EMAIL>", "Barnices2025y.", "barnices", userManager);
        //await CrearUsuario("cesar", "<EMAIL>", "Zorrito24.", "mmcesar", userManager);
    }

    private static async Task CrearUsuario(string nombreUsuario, string correo, string password, string rol, UserManager<ApplicationUser> userManager)
    {
        var usuario = await userManager.FindByNameAsync(nombreUsuario);
        if (usuario == null)
        {
            var nuevoUsuario = new ApplicationUser
            {
                UserName = nombreUsuario,
                Email = correo,
                EmailConfirmed = true
            };

            var result = await userManager.CreateAsync(nuevoUsuario, password);
            if (!result.Succeeded)
                throw new Exception($"Error al crear el usuario {nombreUsuario}: {string.Join(", ", result.Errors.Select(e => e.Description))}");

            usuario = nuevoUsuario;
        }

        if (!await userManager.IsInRoleAsync(usuario, rol))
        {
            await userManager.AddToRoleAsync(usuario, rol);
        }
    }
}