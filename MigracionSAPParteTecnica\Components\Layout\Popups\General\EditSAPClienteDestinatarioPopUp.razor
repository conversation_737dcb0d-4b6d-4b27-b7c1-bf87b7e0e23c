﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editSAPClienteDestinatario?.Id > 0)
{
    <EntityLockManager EntityType="SAPClienteDestinatario"
                       EntityId="editSAPClienteDestinatario.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar destinatario"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este destinatario está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editSAPClienteDestinatario" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editSAPClienteDestinatario" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código SAP destinatario:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-hash" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editSAPClienteDestinatario.Codigo_Destinatario_SAP" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Interlocutor comercial:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-person-badge" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox Data="@ClientesList"
                                        Value="ClienteSeleccionado"
                                        TextFieldName="Display"
                                        ValidationEnabled="false"
                                        ValueChanged="@((ClienteComboItem? selected) => OnClienteChanged(selected))"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Nombre:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-person" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Nombre" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Dirección:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-geo-alt" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Direccion" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Ciudad:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-buildings" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Ciudad" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Provincia:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-map" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Provincia" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="País:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-mailbox" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Pais" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Código postal:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-search" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editSAPClienteDestinatario.Codigo_Postal" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code {
    private bool IsPopupVisible;
    [Parameter] public EventCallback<SAPClienteDestinatarioDTO> OnSave { get; set; }
    [Parameter] public List<ClienteComboItem> ClientesList { get; set; } = new();
    [Parameter] public ClienteComboItem? ClienteSeleccionado { get; set; }
    private SAPClienteDestinatarioDTO editSAPClienteDestinatario = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(SAPClienteDestinatarioDTO sAPClienteDestinatario)
    {
        editSAPClienteDestinatario = sAPClienteDestinatario;

        if (ClientesList != null && ClientesList.Any())
        {
            ClienteSeleccionado = ClientesList
                .FirstOrDefault(c => c.Codigo_IN2 == editSAPClienteDestinatario.Codigo_IN2);
        }

        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    private void OnClienteChanged(ClienteComboItem? selected)
    {
        ClienteSeleccionado = selected; 

        if (selected != null)
        {
            editSAPClienteDestinatario.Codigo_IN2 = selected.Codigo_IN2;
        }
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        editSAPClienteDestinatario.Codigo_IN2 = ClienteSeleccionado.Codigo_IN2;

        await OnSave.InvokeAsync(editSAPClienteDestinatario);
        IsPopupVisible = false;
        ToastService.MostrarOk("Destinatario guardado correctamente.");
    }
}