﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.General;
using MigracionSAPParteTecnica.DTO.QM;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Entities.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.Services;
using Nelibur.ObjectMapper;
using System.Text.Json;

namespace MigracionSAPParteTecnica.Mediatr.QM.TiposCaracteristicas.Command;
public class TratarTipoCaracteristicaCommand : IRequest<SingleResult<TiposCaracteristicasDTO>>
{
    public TiposCaracteristicasDTO TipoCaracteristicaDTO { get; set; }

    public TratarTipoCaracteristicaCommand(TiposCaracteristicasDTO tipoCaracteristicaDTO)
    {
        TipoCaracteristicaDTO = tipoCaracteristicaDTO;
    }
}
internal class TratarTipoCaracteristicaCommandHandler : IRequestHandler<TratarTipoCaracteristicaCommand, SingleResult<TiposCaracteristicasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;
    private readonly IAuditoriaService _auditoriaService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TratarTipoCaracteristicaCommandHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext, IAuditoriaService auditoriaService, IHttpContextAccessor httpContextAccessor)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
        _auditoriaService = auditoriaService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<SingleResult<TiposCaracteristicasDTO>> Handle(TratarTipoCaracteristicaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<TiposCaracteristicasDTO>
        {
            Data = new TiposCaracteristicasDTO(),
            Errors = new List<string>()
        };

        var usuario = _httpContextAccessor.HttpContext?.User?.Identity?.Name ?? "sistema";

        try
        {
            var existingTipoCaracteristica = await _migracionSAPParteTecnicaContext.TiposCaracteristicas
                .FirstOrDefaultAsync(t => t.Id == request.TipoCaracteristicaDTO.Id, cancellationToken);


            if (existingTipoCaracteristica == null)
            {
                var nuevoTipoCaracteristica = TinyMapper.Map<Entities.MigracionSAPParteTecnica.QM.TiposCaracteristicas>(request.TipoCaracteristicaDTO);

                await _migracionSAPParteTecnicaContext.TiposCaracteristicas.AddAsync(nuevoTipoCaracteristica, cancellationToken);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                request.TipoCaracteristicaDTO.Id = nuevoTipoCaracteristica.Id; // Update Id after insert
                result.Data = request.TipoCaracteristicaDTO;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "INSERT",
                    Tabla = "QM_TiposCaracteristicas",
                    IdRegistro = nuevoTipoCaracteristica.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(nuevoTipoCaracteristica),
                    Comentarios = $"Alta de tipo característica: Id = {nuevoTipoCaracteristica.Id}"
                }, cancellationToken);
            }
            else
            {
                TinyMapper.Map(request.TipoCaracteristicaDTO, existingTipoCaracteristica);

                _migracionSAPParteTecnicaContext.TiposCaracteristicas.Update(existingTipoCaracteristica);
                await _migracionSAPParteTecnicaContext.SaveChangesAsync(cancellationToken);

                result.Data = request.TipoCaracteristicaDTO;

                await _auditoriaService.RegistrarAsync(new AuditoriaDTO
                {
                    Usuario = usuario,
                    Fecha = DateTime.Now,
                    Accion = "UPDATE",
                    Tabla = "QM_TiposCaracteristicas",
                    IdRegistro = existingTipoCaracteristica.Id,
                    DatosAntes = null,
                    DatosDespues = JsonSerializer.Serialize(existingTipoCaracteristica),
                    Comentarios = $"Modificación de tipo característica: Id = {existingTipoCaracteristica.Id}"
                }, cancellationToken);
            }
        }
        catch (DbUpdateException dbEx) when (dbEx.InnerException is SqlException sqlEx && sqlEx.Number == 2627)
        {
            result.Errors.Add("Ya existe un tipo de característica con ese CodTipo y Tipo.");
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: TratarTipoCaracteristica - {(e.InnerException?.Message ?? e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}