﻿using System.Text.Json.Serialization;

namespace MigracionSAPParteTecnica.DTO.ResponseModels;

public class SingleResult<T> : PrimitiveResult
{
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    public static SingleResult<T> Ok(T data) => new()
    {
        Success = true,
        Data = data
    };

    public static SingleResult<T> Error(string mensaje) => new()
    {
        Success = false,
        Errors = [mensaje]
    };

    public static SingleResult<T> Error(IEnumerable<string> errores) => new()
    {
        Success = false,
        Errors = errores.ToList()
    };
}