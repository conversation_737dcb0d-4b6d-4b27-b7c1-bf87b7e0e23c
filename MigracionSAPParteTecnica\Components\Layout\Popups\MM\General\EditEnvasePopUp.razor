﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editEnvase?.Id > 0)
{
    <EntityLockManager EntityType="Envases"
                       EntityId="editEnvase.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Envase"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
         <BodyContentTemplate>
    <EditForm Model="@editEnvase" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator/>
            <DxFormLayout Data="@editEnvase" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Nombre:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editEnvase.Nombre" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Tipo de usos:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/tipo_material-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxComboBox @bind-Value="editEnvase.TipoUso"
                                        Data="@TiposMaterialList"
                                        ValueFieldName="Nombre"
                                        TextFieldName="Nombre"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" 
                                        ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Descripción:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-justify-left" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxMemo @bind-Text="editEnvase.Descripcion" Rows="3" CssClass="cw-480" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Peso Neto:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/kilograms-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editEnvase.PesoNeto"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Peso Bruto:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <img src="Images/peso_bruto-ico.svg" alt="Barril" style="margin-right: 10px; width: 20px; height: 20px;" />
                            <DxSpinEdit @bind-Value="editEnvase.PesoBruto"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Largo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows-vertical" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editEnvase.Largo"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Ancho:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editEnvase.Ancho"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Altura:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="4">
                    <Template Context="ItemContext">
                        <div class="popup-demo-layout-item" style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-arrows-expand" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxSpinEdit @bind-Value="editEnvase.Altura"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ShowSpinButtons="false" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<EnvasesDTO> OnSave { get; set; }
    [Parameter] public List<DropDownWrapper> TiposMaterialList { get; set; }
    private EnvasesDTO editEnvase = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(EnvasesDTO envase)
    {
        editEnvase = envase;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = EnvasesValidator.Validar(editEnvase);
        if (!string.IsNullOrEmpty(error))
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editEnvase);
        IsPopupVisible = false;
        ToastService.MostrarOk("Envase guardado correctamente.");
    }
}