﻿using MigracionSAPParteTecnica.DTO.QM;

namespace MigracionSAPParteTecnica.Validators.QM;
public static class MetodoInspeccionValidator
{
    public static string? Validar(MetodosInspeccionDTO dto)
    {
        if (string.IsNullOrWhiteSpace(dto.Nombre))
            return "Debe ingresar un nombre.";
        if (dto.Nombre.Length > 80)
            return "El nombre no puede tener más de 80 caracteres.";
        if (dto.Nombre != dto.Nombre.ToUpperInvariant())
            return "El nombre debe estar en mayúsculas.";

        if (string.IsNullOrWhiteSpace(dto.Busqueda))
            return "Debe ingresar un campo de búsqueda.";
        if (dto.Busqueda.Length > 40)
            return "El campo de búsqueda no puede tener más de 40 caracteres.";

        if (string.IsNullOrWhiteSpace(dto.Descripcion))
            return "Debe ingresar una descripción.";

        if (string.IsNullOrWhiteSpace(dto.TextoBreve))
            return "Debe ingresar un texto breve.";
        if (dto.TextoBreve.Length > 40)
            return "El texto breve no puede tener más de 40 caracteres.";

        return null;
    }
}
