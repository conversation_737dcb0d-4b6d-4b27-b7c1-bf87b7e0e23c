﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editColor?.Id > 0)
{
    <EntityLockManager EntityType="Colores"
                       EntityId="editColor.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Colores"
         Width="640px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este color está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editColor" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editColor" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Código de color:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="6">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi-card-text" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editColor.CodigoColor" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Nombre:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-justify-left" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxMemo @bind-Text="editColor.Color" Rows="3" CssClass="cw-480" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            @if (!_lectura)
            {
                <DxButton Click="GuardarCambios"
                          Text="Guardar"
                          RenderStyle="ButtonRenderStyle.Primary"
                          style="background-color: #28a745; color: white; border-color: #28a745;" />
                <DxButton Click="CerrarPopup"
                          Text="Cancelar"
                          RenderStyle="ButtonRenderStyle.Danger"
                          style="background-color: #dc3545; color: white; border-color: #dc3545;" />
            }
            else
            {
                <DxButton Text="Volver"
                          RenderStyle="ButtonRenderStyle.Info"
                          Click="CerrarPopup"
                          CssClass="boton-azul-oscuro" />
            }
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public EventCallback<ColoresDTO> OnSave { get; set; }
    private ColoresDTO editColor = new();

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(ColoresDTO colorAux)
    {
        editColor = colorAux;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = ColoresValidator.Validar(editColor);
        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editColor);
        IsPopupVisible = false;
        ToastService.MostrarOk("Color guardado correctamente.");
    }
}