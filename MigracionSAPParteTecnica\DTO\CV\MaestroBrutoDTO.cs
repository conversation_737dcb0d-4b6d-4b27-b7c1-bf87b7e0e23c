﻿namespace MigracionSAPParteTecnica.DTO.CV;
public class MaestroBrutoDTO
{
    public string? Motivo { get; set; }
    public int? Cliente_IN2 { get; set; }
    public int? Cliente_SAP { get; set; }
    public string? RefMotivoCliente { get; set; }
    public string? Marca { get; set; }
    public string? Descrip { get; set; }
    public string? TipoProducto { get; set; }
    public bool? Embuticion { get; set; }
    public string? Fotolito { get; set; }
    public string? PruebaFisica { get; set; }
    public string? Gtin { get; set; }
    public string? TipoFlejado { get; set; }
    public string? Producto { get; set; }
    public decimal? DiametroReal { get; set; }
    public decimal? AlturaElemento { get; set; }
    public decimal? Desarrollo { get; set; }
    public decimal? ResIzq { get; set; }
    public decimal? ResDcha { get; set; }
    public decimal? ResInf { get; set; }
    public decimal? ResSup { get; set; }
    public string? IdFormato { get; set; }
    public string? Plano { get; set; }
    public int? NumeroCuerpos { get; set; }
    public decimal? LargoHoja { get; set; }
    public decimal? AnchoHoja { get; set; }
    public bool? HojaScroll { get; set; }
    public decimal? LargoScroll { get; set; }
    public string Tipo { get; set; }
    public string? Tratamiento { get; set; }
    public int? PedidoEjemplo { get; set; }
}
