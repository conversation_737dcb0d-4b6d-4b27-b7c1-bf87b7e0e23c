﻿@page "/GestionTroqueles"
@attribute [Authorize(Roles = "logistica,preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject ModularBlockingService BlockingService
@inject IJSRuntime JS

@implements IAsyncDisposable

<CustomLoadingPanel @bind-Visible="_isLoading" Mensaje="@MensajeBloqueo()" />
@* <EventosRecientes @ref="eventosRef" /> *@
<CentroNotificaciones />
@if (UltimaActualizacionTroqueles != null)
{
    <div class="alert alert-secondary" style="margin-bottom:10px;">
        Última actualización: <b>@UltimaActualizacionTroqueles.Fecha.ToString("dd/MM/yyyy HH:mm:ss")</b>
        por <b>@UltimaActualizacionTroqueles.Usuario</b>
        @if (!string.IsNullOrWhiteSpace(UltimaActualizacionTroqueles.Comentarios))
        {
            <span> - @UltimaActualizacionTroqueles.Comentarios</span>
        }
    </div>
}
<DxGrid @ref="Grid" Data="Data" PageSize="21" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
        CustomizeElement="OnCustomizeGridElement" SelectionMode="GridSelectionMode.Single"
        SelectedDataItemChanged="@(row => OnTroquelSeleccionado((TroquelesDTO)row))" SelectedDataItem="@selectedTroquel">
    <Columns>
        <DxGridSelectionColumn Width="40px" FixedPosition="GridColumnFixedPosition.Left" />
        <DxGridCommandColumn Width="60px" FixedPosition="GridColumnFixedPosition.Left">
            <HeaderTemplate>
                <a class="bi bi-plus-square bold" style="text-decoration:none; color: lightskyblue" @onclick="() => AbrirTroquelDetalle(null)"></a>
            </HeaderTemplate>
            <CellDisplayTemplate>
                @{
                    var troquel = context.DataItem as TroquelesDTO;
                }
                <a class="bi bi-pencil bold" href="javascript:void(0);" @onclick="() => AbrirTroquelDetalle(troquel)" style="text-decoration: none; padding-right: 15px; color: #c75fff;"/>
                <a class="bi bi-x-lg bold" @onclick="() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex)" style="text-decoration: none; color: red;" href="javascript:void(0);"/>
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Id" Caption="Id Troquel" FixedPosition="GridColumnFixedPosition.Left" Width="120px" />
        <DxGridDataColumn FieldName="ClienteNombreConcatenado" Caption="Cliente" Width="180px" />
        <DxGridDataColumn FieldName="IdFormatoIn2" Caption="Id Formato" Width="130px" />
        <DxGridDataColumn FieldName="TipoElemento" Caption="Tipo elemento" Width="140px" />
        <DxGridDataColumn FieldName="Familia" Caption="Familia" Width="100px" />
        <DxGridDataColumn FieldName="Subfamilia" Caption="Subfamilia" Width="120px" />
        <DxGridDataColumn FieldName="DiametroEnvase" Caption="Diámetro Envase" Width="160px" />
        <DxGridDataColumn FieldName="DiametroReal" Caption="Diámetro Real" Width="140px" />
        <DxGridDataColumn FieldName="AlturaEnvase" Caption="Altura envase" Width="140px" />
        <DxGridDataColumn FieldName="AlturaElemento" Caption="Altura elemento" Width="150px" />
        <DxGridDataColumn FieldName="Desarrollo" Caption="Desarrollo" Width="120px" />
        <DxGridDataColumn FieldName="ReservasIzquierda" Caption="ResIzq" Width="90px" />
        <DxGridDataColumn FieldName="ReservasDerecha" Caption="ResDcha" Width="110px" />
        <DxGridDataColumn FieldName="ReservasSuperiores" Caption="ResSup" Width="100px" />
        <DxGridDataColumn FieldName="ReservasInferiores" Caption="ResInf" Width="90px" />
        <DxGridDataColumn FieldName="EmbuticionTexto" Caption="Embutición" Width="120px" />
        <DxGridDataColumn FieldName="EjeMayor" Caption="EjeMayor" Width="110px" />
        <DxGridDataColumn FieldName="EjeMenor" Caption="EjeMenor" Width="110px" />
        <DxGridDataColumn FieldName="FormatoEnvase" Caption="Formato Envase" Width="160px" />
        <DxGridDataColumn FieldName="Tipo" Caption="Tipo" Width="110px" />
        <DxGridDataColumn FieldName="PlanoEjemplo" Caption="Plano Ejemplo" Width="140px" />
        <DxGridDataColumn FieldName="Descripcion" Caption="Descripción" Width="300px" />
        <DxGridDataColumn FieldName="Observaciones" Caption="Observaciones" Width="300px" />
        <DxGridDataColumn FieldName="PedidoEjemplo" Caption="Pedido Ejemplo" Width="160px" DisplayFormat="0" />

    </Columns>
    <ToolbarTemplate>
        <DxToolbar>
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Secondary" Text="Duplicar troquel" Alignment="ToolbarItemAlignment.Left"
                           IconCssClass="bi bi-files" Click="OnDuplicarTroquel" CssClass="ms-3 rounded" />
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Secondary" Text="Ver incompletos" Alignment="ToolbarItemAlignment.Right"
                           IconCssClass="bi bi-exclamation-triangle-fill" Click="OnVerIncompletos" CssClass="ms-3 rounded" />
            <DxToolbarItem Text="Ver inactivos" IconCssClass="bi bi-eye-slash" Alignment="ToolbarItemAlignment.Right"
                           Click="OnVerInactivos" RenderStyle="ButtonRenderStyle.Secondary" CssClass="ms-3 rounded" />
            <DxToolbarItem Text="Ver todos" IconCssClass="bi bi-eye" Alignment="ToolbarItemAlignment.Right"
                           Click="CargarGrid" RenderStyle="ButtonRenderStyle.Secondary" CssClass="ms-3 rounded" />
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar troqueles" CssClass="ms-3 rounded"
                Alignment="ToolbarItemAlignment.Right" Click="ActualizarTroqueles" IconCssClass="bi bi-arrow-clockwise" />
        </DxToolbar>
    </ToolbarTemplate>
</DxGrid>

@code{
    bool _isLoading;
    DxGrid? Grid;
    ObservableCollection<TroquelesDTO> Data = new();
    private TroquelesDTO? selectedTroquel { get; set; }
    string GridSearchText = "";
    private bool mostrandoIncompletos = false;
    List<DropDownWrapper> ClientesList = new();
    bool _bloqueado;
    string? _usuarioBloqueador;
    string? _motivoBloqueo;
    private DotNetObjectReference<GestionTroqueles>? dotNetRef;
    AuditoriaDTO? UltimaActualizacionTroqueles { get; set; }
    private EventosRecientes? eventosRef;

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        InicializarBloqueoTroqueles();
        await CargarUltimaActualizacionTroqueles();

        await CargarGrid();
        await CargarClientesList();

        _isLoading = false;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            dotNetRef = DotNetObjectReference.Create(this);
            await JS.InvokeVoidAsync("signalRHelper.start", dotNetRef);
        }
    }

    [JSInvokable("OnTroquelModificado")]
    public async Task OnTroquelModificado(string data)
    {
        try
        {
            var evento = System.Text.Json.JsonSerializer.Deserialize<ImposicionModificadaEvent>(
                data,
                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
            if (evento != null)
            {
                var mensaje = $"El usuario {evento.Usuario} ha modificado el troquel {evento.Id} (cliente: {evento.Cliente})";
                ToastService.MostrarInfo(mensaje);
                await Task.Delay(100);

                if (eventosRef != null)
                    await eventosRef.RecargarEventosAsync();

                await CargarGrid();

                // await InvokeAsync(() => ToastService.MostrarInfo(mensaje));
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine("EXCEPCION EN OnTroquelModificado: " + ex.ToString());
        }
    }

    [JSInvokable("OnImposicionModificada")]
    public async Task OnImposicionModificada(string data)
    {
        try
        {
            var evento = System.Text.Json.JsonSerializer.Deserialize<ImposicionModificadaEvent>(
                data,
                new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );
            if (evento != null)
            {
                var mensaje = $"El usuario {evento.Usuario} ha modificado la imposición {evento.Id} (plano: {evento.Plano}, cliente: {evento.Cliente})";
                ToastService.MostrarInfo(mensaje);

                if (eventosRef != null)
                    await eventosRef.RecargarEventosAsync();

            }
            await CargarGrid();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine("EXCEPCION EN OnImposicionModificada: " + ex.ToString());
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (dotNetRef != null)
        {
            await JS.InvokeVoidAsync("signalRHelper.stop");
            dotNetRef.Dispose();
        }
        _isLoading = false;
        _bloqueado = false;
        BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;
    }

    private void InicializarBloqueoTroqueles()
    {
        BlockingService.OnModuleStateChanged += EstadoModuloActualizado;
        var estado = BlockingService.GetState("ConfiguradorVariantes");
        _bloqueado = estado.IsBlocked;
        _usuarioBloqueador = estado.Usuario;
        _motivoBloqueo = estado.Motivo;
    }

    private string MensajeBloqueo()
    {
        if (_bloqueado)
        {
            return $"El usuario {_usuarioBloqueador} está actualizando troqueles: {_motivoBloqueo}";
        }
        return "Cargando...";
    }

    private void OnTroquelSeleccionado(TroquelesDTO troquel)
    {
        selectedTroquel = troquel;
    }

    private void EstadoModuloActualizado(string modulo, ModularBlockingService.BlockingState estado)
    {
        if (modulo == "ConfiguradorVariantes")
        {
            _bloqueado = estado.IsBlocked;
            _usuarioBloqueador = estado.Usuario;
            _motivoBloqueo = estado.Motivo;
            _isLoading = _bloqueado;
            InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        _isLoading = false;
        _bloqueado = false;
        BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllTroqueles();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            var clientesResult = await MigracionSAPParteTecnicaService.GetAllSAPClientes();
            if (clientesResult.Errors.Any())
            {
                ToastService.MostrarError(clientesResult.Errors.First());
            }

            var clientesDic = clientesResult.Data.GroupBy(c => c.Codigo_SAP).ToDictionary(g => g.Key, g => g.First().Nombre);

            Data = new ObservableCollection<TroquelesDTO>(
                result.Data.Select(p =>
                {
                    var nombreCliente = (p.Cliente_SAP.HasValue && clientesDic.TryGetValue(p.Cliente_SAP.Value, out var nombre))
                     ? nombre
                     : "Desconocido";
                    p.ClienteNombreConcatenado = $"{p.Cliente_SAP} - {nombreCliente}";
                    return p;
                })
            );

            StateHasChanged();
        }
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (TroquelesDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    void AbrirTroquelDetalle(object? troquelObj)
    {
        if (troquelObj is TroquelesDTO troquel)
        {
            NavigationManager.NavigateTo($"/troquel-detalle/{troquel.Id}");
        }
        else
        {
            NavigationManager.NavigateTo("/troquel-detalle");
        }
    }

    public async Task ActualizarTroqueles()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await MigracionSAPParteTecnicaService.ActualizarTroqueles();

        if (result.Success)
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
            await CargarUltimaActualizacionTroqueles();
        }
        else
        {
            ToastService.MostrarError($"Error al actualizar troqueles: {result.Message}");
        }

        _isLoading = false;
    }

    public async Task CargarClientesList()
    {
        var resultado = await MigracionSAPParteTecnicaService.GetAllSAPClientes();

        if (resultado != null && resultado.Data != null)
        {
            ClientesList = resultado.Data
                .OrderBy(c => c.Codigo_SAP)
                .Select(c => new DropDownWrapper
                {
                    ID = c.Codigo_SAP,
                    Nombre = $"{c.Codigo_SAP}, {c.Nombre}"
                })
                .ToList();
        }
    }

    private async Task OnVerIncompletos()
    {
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.GetTroquelesIncompletos();
        if (result.Errors.Any())
        {
            ToastService.MostrarError("Error al obtener troqueles incompletos.");
        }
        else
        {
            Data = new ObservableCollection<TroquelesDTO>(result.Data);
            ToastService.MostrarInfo($"Se han cargado {Data.Count} troqueles incompletos.");
        }

        _isLoading = false;
    }

    private async Task OnVerInactivos()
    {
        _isLoading = true;

        var result = await MigracionSAPParteTecnicaService.GetTroquelesInactivos();
        if (result.Errors.Any())
        {
            ToastService.MostrarError("Error al obtener troqueles inactivos.");
        }
        else
        {
            Data = new ObservableCollection<TroquelesDTO>(result.Data);
            ToastService.MostrarInfo($"Se han cargado {Data.Count} troqueles inactivos.");
        }

        _isLoading = false;
    }

    void OnCustomizeGridElement(GridCustomizeElementEventArgs e)
    {
        if (e.ElementType != GridElementType.DataRow)
            return;

        var activoObj = e.Grid.GetRowValue(e.VisibleIndex, "Activo");

        if (activoObj is bool activo && !activo)
        {
            e.CssClass = "fila-inactiva";
        }
    }

    private async Task OnDuplicarTroquel()
    {
        if (selectedTroquel == null)
        {
            ToastService.MostrarError("Selecciona primero un troquel de la tabla.");
            return;
        }

        _isLoading = true;
        try
        {
            var result = await MigracionSAPParteTecnicaService.DuplicarTroquel(selectedTroquel.Id);

            if (result.Errors.Any())
            {
                ToastService.MostrarError(string.Join("; ", result.Errors));
            }
            else
            {
                ToastService.MostrarOk($"Troquel duplicado con éxito (nuevo Id: {result.Data.Id}).");
                await CargarGrid();
            }
        }
        catch (Exception ex)
        {
            ToastService.MostrarError("Error al duplicar troquel: " + ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task CargarUltimaActualizacionTroqueles()
    {
        var auditoriaResult = await MigracionSAPParteTecnicaService.GetUltimaActualizacionByTabla("CV_Troqueles");
        if (auditoriaResult.Errors.Any())
        {
            UltimaActualizacionTroqueles = null;
        }
        else
        {
            UltimaActualizacionTroqueles = auditoriaResult.Data;
        }
    }
}