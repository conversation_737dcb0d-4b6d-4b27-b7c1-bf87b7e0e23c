﻿@inherits ComponentBase
@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IToastService ToastService

@if (IsPopupVisible && editPlano?.Id > 0)
{
    <EntityLockManager EntityType="Plano"
                       EntityId="editPlano.Id"
                       OnLockStateChanged="OnLockStateChanged" />
}
<DxPopup @bind-Visible="IsPopupVisible"
         HeaderText="Editar Plano"
         Width="500px"
         ShowCloseButton="true"
         CloseOnEscape="true"
         ShowFooter="true"
         CssClass="custom-popup"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        @if (_lectura)
        {
            <div class="alert alert-warning mb-2">
                Este plano está siendo editado por <b>@_usuarioBloqueador</b> (@_fechaBloqueo.ToString("g")).
            </div>
        }
        <EditForm Model="@editPlano" OnValidSubmit="GuardarCambios" Context="model">
            <DataAnnotationsValidator />
            <DxFormLayout Data="@editPlano" LabelLocation="Top" CssClass="custom-form-layout">
                <DxFormLayoutItem Caption="Cliente:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="12">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-person" style="display: in-line; margin-right: 10px; font-size: 1.2em;"></i>
                            <DxComboBox @bind-Value="editPlano.Cliente"
                                        Data="@ClientesList"
                                        ValueFieldName="ID"
                                        TextFieldName="Nombre"
                                        CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Nombre del plano:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="9">
                    <Template Context="ItemContext">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <i class="bi bi-tag" style="margin-right: 10px; font-size: 1.2em;"></i>
                            <DxTextBox @bind-Text="editPlano.NombrePlano" CssClass="popup-demo-textbox full-width" style="flex-grow: 1;" ReadOnly="@_lectura" />
                        </div>
                    </Template>
                </DxFormLayoutItem>
                <DxFormLayoutItem Caption="Activo:" CaptionPosition="CaptionPosition.Vertical" CssClass="popup-demo-item" ColSpanMd="3">
                    <Template Context="ItemContext">
                        <DxCheckBox CheckType="CheckType.Switch"
                                    @bind-Checked="editPlano.Activo"
                                    LabelPosition="LabelPosition.Left"
                                    Alignment="CheckBoxContentAlignment.SpaceBetween" ReadOnly="@_lectura">
                        </DxCheckBox>
                    </Template>
                </DxFormLayoutItem>
            </DxFormLayout>
        </EditForm>
    </BodyContentTemplate>
    <FooterContentTemplate Context="Context">
        <div style="display: flex; justify-content: flex-end; width: 100%; gap: 10px;">
            <DxButton Click="GuardarCambios"
                      Text="Guardar"
                      RenderStyle="ButtonRenderStyle.Primary" Enabled="!_lectura"
                      style="background-color: #28a745; color: white; border-color: #28a745;" />
            <DxButton Click="CerrarPopup"
                      Text="Cancelar"
                      RenderStyle="ButtonRenderStyle.Danger"
                      style="background-color: #dc3545; color: white; border-color: #dc3545;" />
        </div>
    </FooterContentTemplate>
</DxPopup>

@code{
    private bool IsPopupVisible;
    [Parameter] public List<DropDownWrapper> ClientesList { get; set; }
    [Parameter] public EventCallback<PlanoDTO> OnSave { get; set; }
    private PlanoDTO editPlano = new();

    [Parameter] public int PlanoId { get; set; }

    bool _lectura = false;
    string _usuarioBloqueador;
    DateTime _fechaBloqueo;

    public void AbrirPopUp(PlanoDTO plano, List<DropDownWrapper> clientes)
    {
        editPlano = plano;
        this.ClientesList = clientes;
        PlanoId = plano.Id;
        IsPopupVisible = true;
        StateHasChanged();
    }

    private void OnLockStateChanged(LockResult estado)
    {
        _lectura = estado.Lectura;
        _usuarioBloqueador = estado.Usuario;
        _fechaBloqueo = estado.Fecha;
        StateHasChanged();
    }

    public void CerrarPopup()
    {
        IsPopupVisible = false;
    }

    private async Task GuardarCambios()
    {
        var error = PlanoValidator.Validar(editPlano);

        if (error != null)
        {
            ToastService.MostrarError(error);
            return;
        }

        await OnSave.InvokeAsync(editPlano);
        IsPopupVisible = false;
        ToastService.MostrarOk("Plano guardado correctamente.");
    }
}