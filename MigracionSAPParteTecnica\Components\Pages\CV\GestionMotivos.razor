﻿@page "/GestionMotivos"
@attribute [Authorize(Roles = "preprint, admin")]

@inject IMigracionSAPParteTecnicaService MigracionSAPParteTecnicaService
@inject IServinService ServinService
@inject IToastService ToastService
@inject NavigationManager NavigationManager
@inject ModularBlockingService BlockingService

<CustomLoadingPanel @bind-Visible="_isLoading" Mensaje="@MensajeBloqueo()" />
<DxGrid @ref="Grid" Data="Data" PageSize="22" TextWrapEnabled="false"
        SizeMode="SizeMode.Medium" ShowFilterRow="true" AllowDragRows="true"
        ItemsDropped="Grid_ItemsDropped" ColumnResizeMode="GridColumnResizeMode.NextColumn"
        CssClass="ch-480" PageSizeSelectorVisible="true"
        PagerSwitchToInputBoxButtonCount="10" PagerVisibleNumericButtonCount="10"
        PageSizeSelectorItems="@(new[] { 10, 20, 100 })" PageSizeSelectorAllRowsItemVisible="true"
        EditMode="GridEditMode.PopupEditForm" HighlightRowOnHover="true"
        SearchText="@GridSearchText" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always">
    <Columns>
        <DxGridDataColumn FieldName="Id" Caption="Id Motivo" Width="140px" />
        <DxGridDataColumn FieldName="IdTroquel" Caption="Id Troquel" Width="140px" />
        <DxGridDataColumn FieldName="IdCliente" Caption="Cliente" Width="100px" />
        <DxGridDataColumn FieldName="RefMotivoCliente" Caption="RefMotivoCliente" Width="200px" />
        <DxGridDataColumn FieldName="Marca" Caption="Marca" Width="140px" />
        <DxGridDataColumn FieldName="Descrip" Caption="Descripción" Width="200px" />
        <DxGridDataColumn FieldName="TipoProducto" Caption="Tipo Producto" Width="140px" />
        <DxGridDataColumn FieldName="Embuticion" Caption="Embutición" Width="140px" />
        <DxGridDataColumn FieldName="IdTratamiento" Caption="Id Tratamiento" Width="160px" />
        <DxGridDataColumn FieldName="CodigoArchivo" Caption="Código Archivo" Width="170px" />
        <DxGridDataColumn FieldName="ReferenciaColor" Caption="Referencia Color" Width="170px" />
        <DxGridDataColumn FieldName="MuestraFisica" Caption="Muestra Física" Width="170px" />
        <DxGridDataColumn FieldName="IdMotivoPrecedente" Caption="Id Motivo Precedente" Width="190px" />
        <DxGridDataColumn FieldName="Gama" Caption="Gama" Width="110px" />
        <DxGridDataColumn FieldName="Gtin" Caption="Gtin" Width="140px" />
        <DxGridDataColumn FieldName="PosFlejado" Caption="Posición Flejado" Width="190px" />
        <DxGridDataColumn FieldName="Activo" Caption="Activo" Width="100px" />
    </Columns>
    <ToolbarTemplate>
        <DxToolbar>
            <DxToolbarItem RenderStyle="ButtonRenderStyle.Info" Text="Actualizar motivos" CssClass="ms-3 rounded"
                           IconCssClass="bi bi-arrow-clockwise" Click="ActualizarMotivos" Alignment="ToolbarItemAlignment.Right" />
        </DxToolbar>
    </ToolbarTemplate>
</DxGrid>

@code{
    bool _isLoading { get; set; }
    DxGrid? Grid;
    ObservableCollection<MotivosDTO> Data = new();
    string GridSearchText = "";
    private DotNetObjectReference<GestionMotivos>? dotNetRef;
    bool _bloqueado;
    string? _usuarioBloqueador;
    string? _motivoBloqueo;

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        InicializarBloqueoMotivos();
        await CargarGrid();

        _isLoading = false;
    }

    public async Task CargarGrid()
    {
        var result = await MigracionSAPParteTecnicaService.GetAllMotivos();
        if (result.Errors.Any())
        {
            ToastService.MostrarError(result.Errors.First());
        }
        else
        {
            Data = new ObservableCollection<MotivosDTO>(result.Data);
            StateHasChanged();
        }
    }

    private void InicializarBloqueoMotivos()
    {
        BlockingService.OnModuleStateChanged += EstadoModuloActualizado;
        var estado = BlockingService.GetState("CV_Motivos");
        _bloqueado = estado.IsBlocked;
        _usuarioBloqueador = estado.Usuario;
        _motivoBloqueo = estado.Motivo;
    }

    private async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        var droppedItem = (MotivosDTO)evt.DroppedItems[0];
        Data.Remove(droppedItem);
        var index = await evt.GetTargetDataSourceIndexAsync();
        Data.Insert(index, droppedItem);
    }

    private string MensajeBloqueo()
    {
        if (_bloqueado)
        {
            return $"El usuario {_usuarioBloqueador} está actualizando motivos: {_motivoBloqueo}";
        }
        return "Cargando...";
    }

    private void EstadoModuloActualizado(string modulo, ModularBlockingService.BlockingState estado)
    {
        if (modulo == "CV_Motivos")
        {
            _bloqueado = estado.IsBlocked;
            _usuarioBloqueador = estado.Usuario;
            _motivoBloqueo = estado.Motivo;
            _isLoading = _bloqueado;
            InvokeAsync(StateHasChanged);
        }
    }

    public void Dispose()
    {
        _isLoading = false;
        _bloqueado = false;
        BlockingService.OnModuleStateChanged -= EstadoModuloActualizado;
    }

    public async Task ActualizarMotivos()
    {
        _isLoading = true;
        StateHasChanged();

        var result = await MigracionSAPParteTecnicaService.ActualizarMotivos();

        if (result.Success)
        {
            ToastService.MostrarInfo(result.Message);
            await CargarGrid();
        }
        else
        {
            ToastService.MostrarError($"Error al actualizar motivos: {result.Message}");
        }

        _isLoading = false;
    }
}