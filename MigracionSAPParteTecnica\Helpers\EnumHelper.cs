﻿using System.ComponentModel;
using System.Reflection;

namespace MigracionSAPParteTecnica.Helpers;
public class EnumHelper
{
    public static List<string> GetEnumNames<T>() where T : Enum
    {
        return Enum.GetNames(typeof(T)).ToList();
    }

    public static List<(string Name, string Description)> GetEnumNamesAndDescriptions<T>() where T : Enum
    {
        var type = typeof(T);
        var list = new List<(string Name, string Description)>();

        foreach (var field in type.GetFields(BindingFlags.Public | BindingFlags.Static))
        {
            var name = field.Name;
            var descriptionAttr = field.GetCustomAttribute<DescriptionAttribute>();
            var description = descriptionAttr?.Description ?? name;
            list.Add((name, description));
        }

        return list;
    }
}