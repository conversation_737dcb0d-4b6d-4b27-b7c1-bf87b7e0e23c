﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using MigracionSAPParteTecnica.Data.MigracionSAPParteTecnica;
using MigracionSAPParteTecnica.DTO.CV;
using MigracionSAPParteTecnica.DTO.MM.Barnices;
using MigracionSAPParteTecnica.DTO.ResponseModels;
using MigracionSAPParteTecnica.Mediatr.CV.Imposiciones.Query;
using MigracionSAPParteTecnica.Mediatr.MM.Barnices.CodigoEnvases.Query;
using Nelibur.ObjectMapper;

namespace MigracionSAPParteTecnica.Mediatr.MM.Barnices.Familias.Query;

public class GetAllFamiliasQuery : IRequest<ListResult<FamiliasDTO>>
{
}

internal class GetAllFamiliasQueryHandler : IRequestHandler<GetAllFamiliasQuery, ListResult<FamiliasDTO>>
{
    private readonly MigracionSAPParteTecnicaContext _migracionSAPParteTecnicaContext;

    public GetAllFamiliasQueryHandler(MigracionSAPParteTecnicaContext migracionSAPParteTecnicaContext)
    {
        _migracionSAPParteTecnicaContext = migracionSAPParteTecnicaContext;
    }

    public async Task<ListResult<FamiliasDTO>> Handle(GetAllFamiliasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<FamiliasDTO>
        {
            Data = new List<FamiliasDTO>(),
            Errors = new List<string>()
        };

        try
        {
            var listFamilias = await _migracionSAPParteTecnicaContext.Familias
                .AsNoTracking()
                .Where(l => !l.Borrado)
                .ToListAsync(cancellationToken);

            result.Data = TinyMapper.Map<List<FamiliasDTO>>(listFamilias).ToList();
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: GetAllFamiliasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}